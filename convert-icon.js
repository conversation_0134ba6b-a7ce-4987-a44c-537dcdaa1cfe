const pngToIco = require('png-to-ico');
const fs = require('fs');
const path = require('path');

async function convertPngToIco() {
  try {
    console.log('开始转换 PNG 到 ICO...');
    
    // 读取 PNG 文件
    const pngPath = path.join(__dirname, 'build', 'icon.png');
    console.log(`PNG 文件路径: ${pngPath}`);
    
    // 检查 PNG 文件是否存在
    if (!fs.existsSync(pngPath)) {
      console.error('PNG 文件不存在!');
      return;
    }
    
    // 转换 PNG 到 ICO
    const icoBuffer = await pngToIco(pngPath);
    
    // 保存 ICO 文件
    const icoPath = path.join(__dirname, 'build', 'icon.ico');
    fs.writeFileSync(icoPath, icoBuffer);
    
    console.log(`ICO 文件已保存到: ${icoPath}`);
  } catch (error) {
    console.error('转换过程中出错:', error);
  }
}

convertPngToIco();
