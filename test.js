const WebSocket = require('ws');
const fs = require('fs').promises;
const path = require('path');

// 固定的设备序列号列表
const FIXED_DEVICE_SNS = [
  'D3HD00104D90000001', // 测试设备1
  'D3HD00204D90000002', // 测试设备2
  'D3HD00304D90000003', // 测试设备3
  'D3HD00404D90000004', // 测试设备4
  'D3HD00504D90000005',  // 测试设备5
  'D3HD00504D90000006',  // 测试设备6
  'D3HD00504D90000007', // 测试设备7
  'D3HD00504D90000008', // 测试设备8
  'D3HD00504D90000009', // 测试设备9
  'D3HD00504D90000010', // 测试设备10
  'D3HD00504D90000011',
  'D3HD00504D90000012',
  'D3HD00504D90000013',
  'D3HD00504D90000014',
  'D3HD00504D90000015',
  'D3HD00504D90000016',
  'D3HD00504D90000017',
  'D3HD00504D90000018',
  'D3HD00504D90000019'
];

const maxConnections = Math.max(FIXED_DEVICE_SNS.length, 10);
const sockets = new Map(); // 当前活跃的连接
const deviceStates = new Map(); // 存储所有设备状态，包括离线设备
const fileReceivers = new Map(); // 存储文件接收状态
const ipSnMap = new Map(); // IP 和 SN 的映射关系

// 添加命令行参数解析
const args = process.argv.slice(2);
const forceOnline = args.includes('--force-online');

// 生成模拟IP地址
function generateIP(index) {
  return `192.168.1.${100 + index}`;
}

// 生成设备序列号
function generateSN(clientIP) {
  // 如果该IP已经有对应的SN，直接返回
  if (ipSnMap.has(clientIP)) {
    return ipSnMap.get(clientIP);
  }

  // 从固定SN列表中选择一个未使用的SN
  const usedSNs = Array.from(ipSnMap.values());
  const availableFixedSN = FIXED_DEVICE_SNS.find(sn => !usedSNs.includes(sn));
  
  if (availableFixedSN) {
    ipSnMap.set(clientIP, availableFixedSN);
    return availableFixedSN;
  }

  // 如果所有固定SN都已使用，则生成随机SN
  const randomHex = () => Math.floor(Math.random() * 4096).toString(16).toUpperCase().padStart(3, '0');
  const randomNum = () => Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
  const sn = `D3HD${randomHex()}04D9${randomNum()}`;
  
  ipSnMap.set(clientIP, sn);
  return sn;
}

// 生成随机设备状态
function generateDeviceStatus(clientIP, forceOnline = false) {
  const sn = generateSN(clientIP);
  const deviceState = deviceStates.get(sn) || {};
  
  // 如果启用了强制在线参数或传入forceOnline为true，则始终返回在线状态
  const isOnline = forceOnline || args.includes('--force-online') || Math.random() > 0.2;
  
  const status = {
    type: 1000,
    data: {
      sn: sn,
      timeStamp: Date.now(),
      deviceStatus: isOnline ? 1 : 0,
      onuse: Math.random() > 0.5 ? 1 : 0,
      playStatus: Math.random() > 0.5 ? 1 : 0,
      controled: Math.random() > 0.5 ? 1 : 0,
      playIndex: -1,
      battery: Math.min(100, (deviceState.battery || 100) + Math.floor(Math.random() * 5) - 2),
      monitorStatus: 0
    }
  };

  deviceStates.set(sn, status.data);
  return status;
}

function createConnection(index, forceConnect = false) {
  const clientIP = generateIP(index);
  const sn = generateSN(clientIP);
  
  // 如果设备已连接，不要创建新连接
  if (sockets.has(clientIP)) {
    return;
  }

  // 在强制在线模式下，总是创建连接
  if (!args.includes('--force-online') && !forceConnect && Math.random() > 0.8) {
    console.log(`设备 ${clientIP} (${sn}) 当前不想连接`);
    return;
  }

  const ws = new WebSocket('ws://localhost:50208', {
    headers: {
      'X-Forwarded-For': clientIP,
      'X-Real-IP': clientIP
    }
  });

  ws.on('open', () => {
    console.log(`设备 ${clientIP} (${sn}) 已连接到 WebSocket 服务器`);
    
    // 处理接收到的控制状态消息
    ws.on('message', (data) => {
      const message = JSON.parse(data);
      if (message.type === 2004) {
        console.log(`设备 ${clientIP} (${sn}) 收到控制状态:`, message.data);
        // 更新设备状态
        const deviceState = deviceStates.get(sn) || {};
        deviceState.controlled = message.data.controlled;
        deviceStates.set(sn, deviceState);
      }
    });

    // 发送设备状态消息
    const message = generateDeviceStatus(clientIP, true);
    ws.send(JSON.stringify(message));
    
    // 存储连接信息
    sockets.set(clientIP, {
      ws,
      heartbeatInterval: null,
      reconnectTimeout: null,
      lastActiveTime: Date.now()
    });

    // 启动心跳
    startHeartbeat(clientIP);
  });

  setupMessageHandlers(ws, clientIP);
  setupErrorHandlers(ws, clientIP);
}

function startHeartbeat(clientIP) {
  const socket = sockets.get(clientIP);
  if (!socket) return;

  // 清除现有的心跳定时器
  if (socket.heartbeatInterval) {
    clearInterval(socket.heartbeatInterval);
  }

  // 设置新的心跳定时器
  socket.heartbeatInterval = setInterval(() => {
    if (socket.ws.readyState === WebSocket.OPEN) {
      // 在强制在线模式下，传入forceOnline为true
      const heartbeat = generateDeviceStatus(clientIP, args.includes('--force-online'));
      
      // 在强制在线模式下，跳过离线检查
      if (!args.includes('--force-online') && heartbeat.data.deviceStatus === 0) {
        console.log(`设备 ${clientIP} (${generateSN(clientIP)}) 状态变为离线，主动断开连接`);
        closeConnection(clientIP);
        return;
      }
      
      socket.ws.send(JSON.stringify(heartbeat));
      socket.lastActiveTime = Date.now();
    }
  }, 5000);
}

function setupMessageHandlers(ws, clientIP) {
  ws.on('message', async (data) => {
    try {
      const msg = JSON.parse(data);
      console.log(`设备 ${clientIP} 收到消息:`, msg);

      if (msg.type === 2008) {
        console.log(`设备 ${clientIP} 收到重启命令`);
        simulateRestart(clientIP);
      } else if (msg.type === 2009) {
        console.log(`设备 ${clientIP} 收到关机命令`);
        closeConnection(clientIP);
      } else if (msg.type === 2021) {
        const { fileName, content, isConfig, isStart, isEnd, index, targetDir } = msg.data;

        // 如果是配置文件，解析并创建目录结构
        if (isConfig) {
          const configDir = path.join(__dirname, `received_files_${clientIP}`);
          await fs.mkdir(configDir, { recursive: true });
          await fs.writeFile(path.join(configDir, fileName), content);
          console.log(`设备 ${clientIP} 配置文件 ${fileName} 已保存`);

          try {
            // 解析配置文件并创建目录结构
            const config = JSON.parse(content);
            for (const item of config.list) {
              const dirPath = path.join(configDir, item.path);
              await fs.mkdir(dirPath, { recursive: true });
              console.log(`设备 ${clientIP} 创建目录: ${dirPath}`);
            }

            // 发送准备就绪消息
            ws.send(JSON.stringify({
              type: 1001,
              data: {
                sn: `DEV_${clientIP.replace(/\./g, '_')}`,
                timeStamp: Date.now(),
                status: 'ready'
              }
            }));
          } catch (error) {
            console.error(`设备 ${clientIP} 处理配置文件错误:`, error);
          }
          return;
        }

        const baseDir = path.join(__dirname, `received_files_${clientIP}`);

        // 处理媒体文件
        if (isStart) {
          fileReceivers.set(fileName, {
            chunks: [],
            currentIndex: 0,
            targetDir: targetDir
          });
          console.log(`设备 ${clientIP} 开始接收文件: ${fileName}, 目标目录: ${targetDir}`);
          return;
        }

        if (content) {
          const receiver = fileReceivers.get(fileName);
          if (!receiver) {
            console.error(`设备 ${clientIP} 未找到文件接收器: ${fileName}`);
            return;
          }

          receiver.chunks[index] = Buffer.from(content, 'base64');
          console.log(`设备 ${clientIP} 接收文件 ${fileName} 的第 ${index} 块`);
        }

        if (isEnd) {
          const receiver = fileReceivers.get(fileName);
          if (!receiver) {
            console.error(`设备 ${clientIP} 未找到文件接收器: ${fileName}`);
            return;
          }

          const fileBuffer = Buffer.concat(receiver.chunks);
          const targetPath = path.join(baseDir, receiver.targetDir);
          await fs.mkdir(targetPath, { recursive: true });
          await fs.writeFile(path.join(targetPath, fileName), fileBuffer);

          fileReceivers.delete(fileName);
          console.log(`设备 ${clientIP} 文件 ${fileName} 接收完成并保存到 ${targetPath}`);

          // 发送文件接收完成消息
          ws.send(JSON.stringify({
            type: 1002,
            data: {
              sn: `DEV_${clientIP.replace(/\./g, '_')}`,
              timeStamp: Date.now(),
              fileName: fileName,
              status: 'complete'
            }
          }));
        }
      }
    } catch (error) {
      console.error(`设备 ${clientIP} 处理消息错误:`, error);
    }
  });
}

function setupErrorHandlers(ws, clientIP) {
  ws.on('error', (error) => {
    console.error(`设备 ${clientIP} WebSocket 错误:`, error);
  });

  ws.on('close', () => {
    console.log(`设备 ${clientIP} WebSocket 连接已关闭`);
    cleanup(clientIP);
  });
}

function cleanup(clientIP) {
  const socket = sockets.get(clientIP);
  if (socket) {
    if (socket.heartbeatInterval) {
      clearInterval(socket.heartbeatInterval);
    }
    if (socket.reconnectTimeout) {
      clearTimeout(socket.reconnectTimeout);
    }
    sockets.delete(clientIP);
  }
}

function closeConnection(clientIP) {
  const socket = sockets.get(clientIP);
  if (socket && socket.ws) {
    socket.ws.close();
    cleanup(clientIP);
  }
}

function simulateRestart(clientIP) {
  const socket = sockets.get(clientIP);
  if (socket) {
    // 关闭当前连接
    closeConnection(clientIP);
    
    // 设置重连定时器
    socket.reconnectTimeout = setTimeout(() => {
      const index = parseInt(clientIP.split('.')[3]) - 100;
      createConnection(index);
    }, 5000);
  }
}

// 模拟设备的随机行为
function simulateRandomBehavior() {
  setInterval(() => {
    // 如果是强制在线模式，不执行随机断开操作
    if (args.includes('--force-online')) {
      return;
    }

    // 遍历所有可能的设备索引
    for (let i = 0; i < maxConnections; i++) {
      const clientIP = generateIP(i);
      const sn = generateSN(clientIP);
      const socket = sockets.get(clientIP);
      
      if (socket) {
        // 已连接设备的随机行为
        if (Math.random() > 0.8) { // 20% 概率执行随机行为
          const behaviors = [
            () => {
              console.log(`设备 ${clientIP} (${sn}) 主动断开连接`);
              closeConnection(clientIP);
            },
            () => {
              console.log(`设备 ${clientIP} (${sn}) 模拟重启`);
              simulateRestart(clientIP);
            }
          ];
          const randomBehavior = behaviors[Math.floor(Math.random() * behaviors.length)];
          randomBehavior();
        }
      } else {
        // 未连接设备尝试重连
        if (Math.random() > 0.7) { // 30% 概率尝试重连
          console.log(`设备 ${clientIP} (${sn}) 尝试重新连接`);
          createConnection(i, true);
        }
      }
    }
  }, 10000); // 每10秒检查一次
}

// 监控设备状态
function monitorDevices() {
  setInterval(() => {
    const now = Date.now();
    sockets.forEach((socket, clientIP) => {
      // 如果设备超过30秒没有活动，认为它可能已经断开
      if (now - socket.lastActiveTime > 30000) {
        console.log(`设备 ${clientIP} (${generateSN(clientIP)}) 超时，关闭连接`);
        closeConnection(clientIP);
      }
    });

    // 打印当前状态
    console.log('\n当前设备状态:');
    console.log(`总计划设备数: ${maxConnections}`);
    console.log(`当前在线设备数: ${sockets.size}`);
    console.log(`离线设备数: ${maxConnections - sockets.size}`);
    
    // 打印每个设备的详细状态
    for (let i = 0; i < maxConnections; i++) {
      const clientIP = generateIP(i);
      const sn = generateSN(clientIP);
      const isOnline = sockets.has(clientIP);
      const status = deviceStates.get(sn);
      console.log(`设备 ${sn}: ${isOnline ? '在线' : '离线'}, 电量: ${status?.battery || '-'}%`);
    }
  }, 30000); // 每30秒打印一次状态
}

function startMassiveConnections() {
  console.log(`开始创建 ${maxConnections} 个设备连接...`);
  
  // 初始连接，在强制在线模式下强制连接所有设备
  for (let i = 0; i < maxConnections; i++) {
    setTimeout(() => {
      createConnection(i, args.includes('--force-online'));
    }, i * 100);
  }

  // 启动随机行为模拟
  setTimeout(() => {
    simulateRandomBehavior();
    monitorDevices();
  }, maxConnections * 100 + 1000);
}

// 确保程序退出时清理所有连接
process.on('SIGINT', () => {
  console.log('正在关闭所有连接...');
  for (const [clientIP] of sockets) {
    closeConnection(clientIP);
  }
  process.exit();
});

// 在启动时显示当前模式
console.log(`启动模式: ${forceOnline ? '强制在线' : '随机在线/离线'}`);
console.log(`使用方法: node test.js [--force-online]`);

startMassiveConnections();
