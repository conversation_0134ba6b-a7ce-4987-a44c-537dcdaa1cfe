const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',

  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },

  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

// 日志函数
function log(message, type = 'info') {
  const timestamp = new Date().toISOString().replace(/T/, ' ').replace(/\..+/, '');

  switch (type) {
    case 'info':
      console.log(`${colors.fg.cyan}[${timestamp}] [INFO]${colors.reset} ${message}`);
      break;
    case 'success':
      console.log(`${colors.fg.green}[${timestamp}] [SUCCESS]${colors.reset} ${message}`);
      break;
    case 'warning':
      console.log(`${colors.fg.yellow}[${timestamp}] [WARNING]${colors.reset} ${message}`);
      break;
    case 'error':
      console.log(`${colors.fg.red}[${timestamp}] [ERROR]${colors.reset} ${message}`);
      break;
    default:
      console.log(`${colors.fg.white}[${timestamp}] [${type.toUpperCase()}]${colors.reset} ${message}`);
  }
}

// 执行命令并记录输出
function runCommand(command, description) {
  log(`${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    log(`${description} 完成`, 'success');
    return true;
  } catch (error) {
    log(`${description} 失败: ${error.message}`, 'error');
    return false;
  }
}

// 检查文件是否存在
function checkFileExists(filePath, description) {
  log(`检查 ${description} 是否存在...`);
  if (fs.existsSync(filePath)) {
    log(`${description} 存在`, 'success');
    return true;
  } else {
    log(`${description} 不存在`, 'error');
    return false;
  }
}

// 主构建流程
async function build() {
  log('开始构建应用程序', 'info');

  // 清理之前的构建
  if (!runCommand('npm run clean', '清理之前的构建')) {
    return;
  }

  // 构建 Vue 前端
  if (!runCommand('npm run build:vue', '构建 Vue 前端')) {
    log('Vue 构建失败，尝试再次构建...', 'warning');
    // 有时第一次构建可能会失败，尝试再次构建
    if (!runCommand('npm run build:vue', '再次尝试构建 Vue 前端')) {
      return;
    }
  }

  // 检查 Vue 构建输出
  const indexHtmlPath = path.join(__dirname, 'dist', 'renderer', 'index.html');
  if (!checkFileExists(indexHtmlPath, 'Vue 构建输出 (index.html)')) {
    return;
  }

  // 转换图标
  if (!runCommand('node convert-icon.js', '转换图标')) {
    log('图标转换失败，但继续构建', 'warning');
  }

  // 检查图标
  const iconPath = path.join(__dirname, 'build', 'icon.ico');
  if (!checkFileExists(iconPath, '应用图标 (icon.ico)')) {
    log('图标不存在，但继续构建', 'warning');
  }

  // 构建 Windows 应用
  if (!runCommand('npm run build:win64', '构建 Windows 应用')) {
    return;
  }

  log('应用程序构建完成！', 'success');
  log('输出目录: ' + path.join(__dirname, 'dist'), 'info');
}

// 执行构建
build().catch(error => {
  log(`构建过程中出错: ${error.message}`, 'error');
  process.exit(1);
});
