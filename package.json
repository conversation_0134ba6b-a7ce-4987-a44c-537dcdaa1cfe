{"name": "pfdm", "version": "2.0.14", "productName": "企业管理套件", "description": "A powerful device management tool for controlling and monitoring devices", "company": "玩出梦想", "author": "玩出梦想", "copyright": "© 2025 玩出梦想. All rights reserved.", "main": "src/main/main.js", "scripts": {"start": "electron .", "start:dev": "electron . --dev", "start:vue": "cross-env USE_VUE=true electron .", "start:vue:dev": "cross-env USE_VUE=true electron . --dev", "dev": "vite --config vite.config.js", "build": "node build-app.js", "build:mac": "electron-builder --mac", "build:win": "electron-builder --win", "build:win32": "electron-builder --win --ia32", "build:win64": "electron-builder --win --x64", "build:app": "npm run build:vue && npm run build:mac", "build:full": "npm run build:vue && npm run build:win", "build:vue": "vite build --config vite.config.js --debug", "preview": "vite preview", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src", "purge-css": "purgecss --content ./src/renderer/**/*.html ./src/renderer/**/*.js --css ./src/renderer/styles/**/*.css --output ../purged/", "css:lint": "stylelint \"src/renderer/styles/**/*.css\"", "css:lint:fix": "stylelint \"src/renderer/styles/**/*.css\" --fix", "css:format": "prettier --write \"src/renderer/styles/**/*.css\"", "css:check": "npm run css:lint && npm run css:format", "css:purge": "purgecss --content ./src/renderer/**/*.html ./src/renderer/**/*.js --css ./src/renderer/styles/**/*.css --output ./src/renderer/styles/dist/", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "postinstall": "patch-package"}, "keywords": ["electron", "device", "management", "control"], "license": "ISC", "devDependencies": {"@babel/preset-env": "^7.26.9", "@vitejs/plugin-vue": "^5.2.3", "babel-jest": "^29.7.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^36.2.1", "electron-builder": "^26.0.12", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "jest": "^29.7.0", "patch-package": "^8.0.0", "png-to-ico": "^2.1.8", "postinstall-postinstall": "^2.1.0", "prettier": "^3.0.0", "purgecss": "^5.0.0", "rimraf": "^5.0.10", "stylelint": "^15.0.0", "stylelint-config-standard": "^34.0.0", "terser": "^5.39.0", "vite": "^6.3.4", "wait-on": "^7.2.0"}, "dependencies": {"@cycjimmy/jsmpeg-player": "^6.1.2", "@easydarwin/easyplayer": "^5.1.4", "adbkit-apkreader": "^3.2.0", "ali-oss": "^6.20.0", "archiver": "^7.0.1", "axios": "^1.6.2", "dotenv": "^16.4.5", "element-plus": "^2.9.10", "ffprobe": "^1.1.2", "fluent-ffmpeg": "^2.1.3", "hls.js": "^1.6.2", "js-yaml": "^4.1.0", "jsmpeg": "^1.0.0", "jsmpeg-player": "^3.0.3", "node-rtsp-stream": "^0.0.9", "pinia": "^3.0.2", "sharp": "^0.34.2", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "ws": "^8.18.2"}, "build": {"appId": "com.pfdm.devicemanager", "productName": "企业管理套件", "directories": {"output": "dist"}, "files": ["src/**/*", "package.json", "build/**/*", "dist/renderer/**/*", "!**/*.map", "!**/*.ts", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}", "!**/.env*"], "extraResources": [{"from": "ffmpeg-binaries/", "to": "ffmpeg-binaries", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "build/icon.ico", "publish": {"provider": "generic", "url": "https://pfdm-eas.oss-cn-shanghai.aliyuncs.com/releases/v${version}"}}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "企业管理套件", "installerIcon": "build/icon.ico", "uninstallerIcon": "build/icon.ico", "artifactName": "${productName}-Setup-${version}.${ext}"}, "mac": {"target": ["dmg"], "icon": "build/mac.icns"}, "linux": {"target": ["AppImage"], "icon": "build/icon.ico"}, "asar": true, "compression": "maximum"}}