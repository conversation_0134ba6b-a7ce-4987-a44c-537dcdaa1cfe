diff --git a/node_modules/node-rtsp-stream/videoStream.js b/node_modules/node-rtsp-stream/videoStream.js
index 720cb53..ec1ede8 100644
--- a/node_modules/node-rtsp-stream/videoStream.js
+++ b/node_modules/node-rtsp-stream/videoStream.js
@@ -27,8 +27,32 @@ VideoStream = function(options) {
 util.inherits(VideoStream, events.EventEmitter)
 
 VideoStream.prototype.stop = function() {
-  this.wsServer.close()
-  this.stream.kill()
+  // 1. 先移除事件监听器，防止在关闭过程中触发事件
+  this.removeAllListeners('camdata')
+
+  // 2. 安全地关闭WebSocket服务器
+  if (this.wsServer) {
+    try {
+      // 保存引用，然后清空，防止后续访问
+      const wsServer = this.wsServer
+      this.wsServer = null
+
+      // 关闭WebSocket服务器
+      wsServer.close()
+    } catch (error) {
+      console.log("Error closing WebSocket server: " + error.message)
+    }
+  }
+
+  // 3. 安全地终止FFmpeg进程
+  if (this.stream) {
+    try {
+      this.stream.kill()
+    } catch (error) {
+      console.log("Error killing stream: " + error.message)
+    }
+  }
+
   this.inputStreamStarted = false
   return this
 }
@@ -95,15 +119,26 @@ VideoStream.prototype.pipeStreamToSocketServer = function() {
     return this.onSocketConnect(socket, request)
   })
   this.wsServer.broadcast = function(data, opts) {
-    var results
-    results = []
-    for (let client of this.clients) {
-      if (client.readyState === 1) {
-        results.push(client.send(data, opts))
-      } else {
-        results.push(console.log("Error: Client from remoteAddress " + client.remoteAddress + " not connected."))
+    var results = []
+
+    // 添加安全检查，确保this和this.clients存在
+    if (!this || !this.clients) {
+      console.log("Warning: WebSocket server or clients not available, skipping broadcast")
+      return results
+    }
+
+    try {
+      for (let client of this.clients) {
+        if (client && client.readyState === 1) {
+          results.push(client.send(data, opts))
+        } else if (client) {
+          results.push(console.log("Error: Client from remoteAddress " + client.remoteAddress + " not connected."))
+        }
       }
+    } catch (error) {
+      console.log("Error in broadcast: " + error.message)
     }
+
     return results
   }
   return this.on('camdata', (data) => {
@@ -127,7 +162,12 @@ VideoStream.prototype.onSocketConnect = function(socket, request) {
   socket.remoteAddress = request.connection.remoteAddress
 
   return socket.on("close", (code, message) => {
-    return console.log(`${this.name}: Disconnected WebSocket (` + this.wsServer.clients.size + " total)")
+    // 添加安全检查，确保wsServer和clients存在
+    if (this.wsServer && this.wsServer.clients) {
+      return console.log(`${this.name}: Disconnected WebSocket (` + this.wsServer.clients.size + " total)")
+    } else {
+      return console.log(`${this.name}: Disconnected WebSocket (wsServer or clients not available)`)
+    }
   })
 }
 
