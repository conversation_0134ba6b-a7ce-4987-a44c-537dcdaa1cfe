module.exports = {
  // 测试环境
  testEnvironment: 'jsdom',

  // 测试文件匹配模式
  testMatch: [
    '**/tests/**/*.test.js',
    '**/__tests__/**/*.js'
  ],

  // 忽略的目录
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/'
  ],

  // 覆盖率收集
  collectCoverage: true,
  collectCoverageFrom: [
    'src/renderer/models/**/*.js',
    'src/renderer/controllers/**/*.js',
    'src/renderer/views/**/*.js',
    'src/shared/types/**/*.js',
    'src/shared/constants/**/*.js'
  ],

  // 覆盖率报告目录
  coverageDirectory: 'coverage',

  // 模块名称映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^../../src/renderer/services/EventService$': '<rootDir>/tests/mocks/EventService.js'
  },

  // 转换器
  transform: {
    '^.+\\.js$': 'babel-jest'
  },

  // 设置测试超时时间
  testTimeout: 10000,

  // 在每个测试文件执行前运行的脚本
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js']
};
