import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/renderer/vue'),
      '@assets': resolve(__dirname, 'src/renderer/assets'),
    }
  },
  base: './',
  build: {
    outDir: resolve(__dirname, 'dist/renderer'),
    emptyOutDir: true,
    assetsDir: 'assets',
    minify: true, // 使用默认的压缩工具 (esbuild)
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'src/renderer/vue/index.html'),
      },
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
        },
        entryFileNames: 'assets/[name].[hash].js',
        chunkFileNames: 'assets/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash].[ext]',
      },
    },
  },
  server: {
    port: 3001,
  },
  // 指定Vue项目根目录
  root: resolve(__dirname, 'src/renderer/vue'),
});
