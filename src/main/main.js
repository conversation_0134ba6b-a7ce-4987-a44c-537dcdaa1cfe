// 在文件最开始添加环境变量配置
require('dotenv').config();

const { app, BrowserWindow } = require('electron');
const path = require('path');
const configService = require('./services/config');
const storageServices = require('./services/storage');
const ipcService = require('./services/ipc/ipc-service');
const resourceController = require('./controllers/resource-controller');
const solutionController = require('./controllers/solution-controller');
const deviceController = require('./controllers/device-controller');
const networkController = require('./controllers/network-controller');
const rtspProxyService = require('./services/stream/rtsp-proxy-service');
const publishRecordService = require('./services/publish-record-service');
const { ensureDirectories } = require('./utils/paths');
const logger = require('./utils/logger');

/**
 * 应用程序主类
 * 负责管理应用生命周期和主窗口
 */
class Application {
  constructor() {
    this.mainWindow = null;
    this.isQuitting = false;
    this.isDev = process.argv.includes('--dev');

    this.ensureSingleInstance();
  }

  /**
   * 确保应用程序只运行一个实例
   * @private
   */
  ensureSingleInstance() {
    const gotTheLock = app.requestSingleInstanceLock();
    if (!gotTheLock) {
      app.quit();
      return;
    }

    app.on('second-instance', () => {
      if (this.mainWindow) {
        if (this.mainWindow.isMinimized()) {
          this.mainWindow.restore();
        }
        this.mainWindow.focus();
      }
    });
  }

  /**
   * 初始化应用程序
   * @async
   */
  async init() {
    try {
      // 设置日志级别（开发环境使用DEBUG级别，生产环境使用INFO级别）
      if (this.isDev) {
        logger.setLevel('DEBUG');
        logger.debug('日志级别设置为DEBUG（开发环境）');
      } else {
        logger.setLevel('INFO');
        logger.info('日志级别设置为INFO（生产环境）');
      }

      logger.info('应用程序开始初始化');

      await app.whenReady();
      logger.info('Electron应用就绪');

      await ensureDirectories();
      logger.info('目录结构已确保存在');

      this.createWindow();
      logger.info('主窗口已创建');

      await this.setupServices();
      logger.info('服务已设置');

      this.setupAppEvents();
      logger.info('应用事件已设置');

      logger.info('应用程序初始化完成');
      return this;
    } catch (error) {
      logger.error('应用程序初始化失败', error);
      throw error;
    }
  }

  /**
   * 获取应用图标路径
   * @private
   * @returns {string} 图标路径
   */
  getAppIconPath() {
    // 根据平台选择正确的图标格式
    const fs = require('fs');
    const iconName = process.platform === 'win32' ? 'icon.png' : 'icon.png';

    // 尝试多个可能的路径
    const possiblePaths = [
      path.join(__dirname, '../../build', iconName),
      path.join(process.cwd(), 'build', iconName),
      path.join(app.getAppPath(), 'build', iconName),
      path.join(__dirname, '../..', iconName)
    ];

    // 检查每个路径，返回第一个存在的路径
    for (const iconPath of possiblePaths) {
      try {
        if (fs.existsSync(iconPath)) {
          logger.info(`找到图标文件: ${iconPath}`);
          return iconPath;
        }
      } catch (error) {
        logger.warn(`检查图标路径时出错: ${iconPath}`, error);
      }
    }

    // 如果没有找到图标，记录警告并返回默认路径
    logger.warn('未找到有效的图标文件，将使用默认路径');
    return possiblePaths[0];
  }

  /**
   * 创建主窗口
   * @private
   */
  createWindow() {
    // 获取应用图标路径
    const iconPath = this.getAppIconPath();
    logger.info(`使用应用图标: ${iconPath}`);

    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1200,  // 降低最小宽度
      minHeight: 800,  // 降低最小高度
      frame: false,  // 移除整个窗口框架
      titleBarStyle: 'hidden', // 隐藏标题栏
      autoHideMenuBar: true,   // 隐藏菜单栏
      icon: iconPath, // 设置应用图标
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: false, // 禁用 web 安全策略，允许加载本地资源
        allowRunningInsecureContent: true, // 允许运行不安全内容
        preload: path.join(__dirname, '../preload/preload.js'),
        // 允许应用内部拖拽
        enableDragAndDrop: true,
        enableRemoteModule: false,
        devTools: this.isDev // 根据环境决定是否启用开发者工具
      }
    });

    // 加载主页面
    let indexPath;

    // 开发环境使用Vite服务
    if (this.isDev) {
      indexPath = 'http://localhost:3001';
      this.mainWindow.loadURL(indexPath);
    } else {
      // 生产环境加载构建后的Vue文件
      indexPath = path.join(__dirname, '../../dist/renderer/index.html');
      console.log('加载生产环境HTML路径:', indexPath);

      // 检查文件是否存在
      const fs = require('fs');
      if (fs.existsSync(indexPath)) {
        console.log('HTML文件存在，正在加载...');
        this.mainWindow.loadFile(indexPath);
      } else {
        console.error('HTML文件不存在:', indexPath);
        // 尝试使用相对路径
        const alternativePath = path.join(process.resourcesPath, 'app/dist/renderer/index.html');
        console.log('尝试替代路径:', alternativePath);

        if (fs.existsSync(alternativePath)) {
          console.log('替代HTML文件存在，正在加载...');
          this.mainWindow.loadFile(alternativePath);
        } else {
          console.error('替代HTML文件也不存在:', alternativePath);
          // 显示错误页面
          this.mainWindow.loadURL(`data:text/html;charset=utf-8,
            <html>
              <head>
                <title>加载错误</title>
                <style>
                  body { font-family: Arial, sans-serif; padding: 20px; color: #333; background-color: #f5f5f5; }
                  h1 { color: #d9534f; }
                  pre { background-color: #eee; padding: 10px; border-radius: 5px; }
                </style>
              </head>
              <body>
                <h1>应用加载失败</h1>
                <p>无法找到主页面文件。请确保应用正确安装。</p>
                <p>尝试加载的路径:</p>
                <pre>${indexPath}</pre>
                <pre>${alternativePath}</pre>
              </body>
            </html>
          `);
        }
      }
    }

    // 开发环境自动打开开发者工具
    if (this.isDev) {
      this.mainWindow.webContents.openDevTools();
    }

    // 设置窗口事件
    this.setupWindowEvents();
  }

  /**
   * 设置窗口事件
   * @private
   */
  setupWindowEvents() {
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  /**
   * 设置服务
   * @private
   */
  async setupServices() {
    try {
      // 初始化配置服务
      await configService.init();
      logger.info('配置服务初始化完成');

      // 初始化存储服务（文件服务和方案服务）
      await storageServices.init();
      logger.info('存储服务初始化完成');

      // 初始化资源控制器
      await resourceController.init();
      logger.info('资源控制器初始化完成');

      // 初始化方案控制器
      await solutionController.init();
      logger.info('方案控制器初始化完成');

      // 初始化IPC服务
      await ipcService.init();
      logger.info('IPC服务初始化完成');

      // 初始化网络控制器
      await networkController.init();
      logger.info('网络控制器初始化完成');

      // 设置网络事件处理
      this.setupNetworkEvents();
      logger.info('网络事件处理设置完成');

      // 初始化设备控制器
      await deviceController.init();
      logger.info('设备控制器初始化完成');

      // 初始化RTSP流代理服务
      await rtspProxyService.init();
      logger.info('RTSP流代理服务初始化完成');

      // 初始化发布记录服务
      await publishRecordService.init();
      logger.info('发布记录服务初始化完成');
    } catch (error) {
      logger.error('服务初始化失败', error);
      throw error;
    }
  }

  /**
   * 设置网络事件处理
   * @private
   */
  setupNetworkEvents() {
    // 监听设备控制器的业务事件
    deviceController.on(deviceController.constructor.EVENTS.DEVICE_CONNECTED, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('device-connected', data);
      }
    });

    deviceController.on(deviceController.constructor.EVENTS.DEVICE_DISCONNECTED, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('device-disconnected', data.ip);
      }
    });

    deviceController.on(deviceController.constructor.EVENTS.DEVICE_STATUS_UPDATE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('device-status-update', data);
      }
    });

    // 监听设备添加和删除事件
    deviceController.on(deviceController.constructor.EVENTS.DEVICE_ADDED, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('device-added', data);
      }
    });

    deviceController.on(deviceController.constructor.EVENTS.DEVICE_DELETED, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('device-deleted', data);
      }
    });

    // 监听音量响应事件
    deviceController.on(deviceController.constructor.EVENTS.VOLUME_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('volume-response', data);
      }
    });

    // 监听设置音量响应事件
    deviceController.on(deviceController.constructor.EVENTS.SET_VOLUME_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('set-volume-response', data);
      }
    });

    // 监听点亮屏幕响应事件
    deviceController.on(deviceController.constructor.EVENTS.SCREEN_ON_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('screen-on-response', data);
      }
    });

    // 监听关闭屏幕响应事件
    deviceController.on(deviceController.constructor.EVENTS.SCREEN_OFF_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('screen-off-response', data);
      }
    });

    // 监听开启定位响应事件
    deviceController.on(deviceController.constructor.EVENTS.LOCATE_START_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('locate-start-response', data);
      }
    });

    // 监听停止定位响应事件
    deviceController.on(deviceController.constructor.EVENTS.LOCATE_STOP_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('locate-stop-response', data);
      }
    });

    // 监听重置视野响应事件
    deviceController.on(deviceController.constructor.EVENTS.RESET_VIEW_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('reset-view-response', data);
      }
    });

    // 监听应用播控响应事件
    deviceController.on(deviceController.constructor.EVENTS.APP_CONTROL_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('app-control-response', data);
      }
    });

    // 监听快捷入口响应事件
    deviceController.on(deviceController.constructor.EVENTS.QUICK_ACCESS_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        this.mainWindow.webContents.send('quick-access-response', data);
      }
    });

    // 监听网络错误事件
    networkController.on(networkController.constructor.EVENTS.NETWORK_ERROR, (data) => {
      logger.error('网络错误', data);
    });

    // 监听投屏响应事件
    networkController.on(networkController.constructor.EVENTS.SCREEN_CAST_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        logger.info('转发投屏响应事件到渲染进程', data);
        this.mainWindow.webContents.send('screen-cast-response', data);
      }
    });

    // 监听停止投屏响应事件
    networkController.on(networkController.constructor.EVENTS.SCREEN_CAST_STOP_RESPONSE, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        logger.info('转发停止投屏响应事件到渲染进程', data);
        this.mainWindow.webContents.send('screen-cast-stop-response', data);
      }
    });

    // 获取 solution-controller
    const solutionController = require('./controllers/solution-controller');

    // 监听方案部署进度事件
    solutionController.on(solutionController.constructor.EVENTS.TRANSFER_PROGRESS, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        logger.info('转发进度事件到渲染进程', {
          type: data.type,
          progress: data.progress,
          deviceSN: data.deviceSN
        });
        this.mainWindow.webContents.send('transfer-progress', data);
      }
    });

    // 监听方案部署错误事件
    solutionController.on(solutionController.constructor.EVENTS.DEPLOY_ERROR, (data) => {
      if (!this.isQuitting && this.mainWindow?.webContents) {
        logger.info('转发错误事件到渲染进程', {
          message: data.message,
          deviceSN: data.deviceSN
        });
        this.mainWindow.webContents.send('deploy-error', data);
      }
    });
  }

  /**
   * 设置应用程序事件
   * @private
   */
  setupAppEvents() {
    app.on('window-all-closed', () => {
      app.quit();
    });

    app.on('activate', () => {
      if (this.mainWindow === null) {
        this.createWindow();
      }
    });

    app.on('before-quit', async () => {
      this.isQuitting = true;
      await this.cleanup();
    });
  }

  /**
   * 清理资源
   * @private
   * @async
   */
  async cleanup() {
    try {
      logger.info('开始清理资源');

      // 关闭网络服务
      networkController.closeNetworkServices();
      logger.info('网络服务已关闭');

      // 清理IPC服务
      ipcService.cleanup();
      logger.info('IPC服务已清理');

      // 清除配置缓存
      configService.clearCache();
      logger.info('配置缓存已清除');

      // 关闭RTSP流代理服务
      await rtspProxyService.close();
      logger.info('RTSP流代理服务已关闭');

      // 文件服务不需要特别关闭，因为它没有持久连接

      logger.info('资源清理完成');
    } catch (error) {
      logger.error('资源清理失败', error);
    } finally {
      // 关闭日志系统
      logger.close();
    }
  }
}

// 创建应用实例
const application = new Application();

// 启动应用
application.init().catch(error => {
  console.error('Failed to initialize application:', error);
  app.quit();
});

// 导出应用实例
module.exports = application;
