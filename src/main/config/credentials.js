/**
 * 凭据配置
 * 根据环境返回不同的凭据
 */
const isDevelopment = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');

// 生产环境凭据
const PRODUCTION_CREDENTIALS = {
  OSS_ACCESS_KEY_ID: 'LTAI5t6YN8PXRKTTPcnSTH52',     // 替换为实际的生产环境 Key ID
  OSS_ACCESS_KEY_SECRET: '******************************'  // 替换为实际的生产环境 Key Secret
};

// 开发环境使用环境变量中的凭据
const DEVELOPMENT_CREDENTIALS = {
  OSS_ACCESS_KEY_ID: process.env.OSS_ACCESS_KEY_ID,
  OSS_ACCESS_KEY_SECRET: process.env.OSS_ACCESS_KEY_SECRET
};

// 导出对应环境的凭据
module.exports = isDevelopment ? DEVELOPMENT_CREDENTIALS : PRODUCTION_CREDENTIALS; 