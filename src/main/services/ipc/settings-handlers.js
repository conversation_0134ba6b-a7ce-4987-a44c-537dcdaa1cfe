/**
 * 设置相关IPC处理器
 */

const fs = require('fs').promises;
const path = require('path');
const { paths, updateResourcesPath } = require('../../utils/paths');
const logger = require('../../utils/logger');
const { SETTINGS } = require('./channels');
const deviceController = require('../../controllers/device-controller');

/**
 * 读取设置文件
 * @param {string} configPath - 配置文件路径
 * @returns {Promise<Object>} 配置对象
 */
async function readSettingsFile(configPath) {
  try {
    const configData = await fs.readFile(configPath, 'utf8');
    return JSON.parse(configData);
  } catch {
    return {
      basic: {
        serverName: '',
        udpPort: '',
        wsPort: ''
      },
      advanced: {
        heartbeatInterval: ''
      }
    };
  }
}

/**
 * 设置相关IPC处理器
 */
const settingsHandlers = {
  // 保存设置
  [SETTINGS.SAVE]: async (_, settings) => {
    const configPath = paths.settings.root;
    await fs.mkdir(path.dirname(configPath), { recursive: true });

    const config = await readSettingsFile(configPath);
    // 合并新设置
    const newConfig = { ...config, ...settings };

    // 检查是否修改了资源路径
    const oldResourcesPath = config.basic?.resourcesPath || '';
    const newResourcesPath = settings.basic?.resourcesPath || '';

    // 保存设置
    await fs.writeFile(configPath, JSON.stringify(newConfig, null, 2));

    // 如果资源路径发生变化，更新资源路径
    if (newResourcesPath !== oldResourcesPath) {
      logger.info('资源路径已更改', {
        oldPath: oldResourcesPath || '默认路径',
        newPath: newResourcesPath || '默认路径'
      });

      try {
        // 更新资源路径
        const updatedPaths = updateResourcesPath(newResourcesPath);
        logger.info('资源路径已更新', updatedPaths);

        // 如果旧路径不为空，且新旧路径不同，可以考虑迁移数据
        if (oldResourcesPath && oldResourcesPath !== newResourcesPath) {
          // 这里可以添加数据迁移逻辑，但需要谨慎处理
          logger.info('资源路径变更可能需要手动迁移数据');
        }
      } catch (error) {
        logger.error('更新资源路径失败', error);
        // 不抛出错误，继续返回成功，因为设置已经保存成功
      }
    }

    // 如果设置包含受控模式的变更，处理相关逻辑
    if (settings.controlledMode !== undefined && settings.controlledMode !== config.controlledMode) {
      const isControlled = settings.controlledMode;
      logger.info('受控模式已更改', { controlledMode: isControlled });

      // 更新所有在线设备的受控模式状态
      try {
        const result = await deviceController.updateAllDevicesControlledMode(isControlled);
        logger.info('更新设备受控模式状态结果', result);
      } catch (error) {
        logger.error('更新设备受控模式状态失败', error);
        // 不抛出错误，继续返回成功，因为设置已经保存成功
      }
    }

    return true;
  },

  // 获取设置
  [SETTINGS.GET]: async () => {
    const configPath = paths.settings.root;
    return await readSettingsFile(configPath);
  },

  // 获取特定类型的配置
  'get-config': async (_, configType) => {
    try {
      const configPath = paths.settings.root;
      const config = await readSettingsFile(configPath);

      // 如果请求特定类型的配置，返回该类型的配置
      if (configType && typeof configType === 'string') {
        return config[configType] || null;
      }

      // 否则返回整个配置
      return config;
    } catch (error) {
      logger.error('获取配置失败', error);
      throw error;
    }
  },

  // 保存特定类型的配置
  'save-config-by-type': async (_, { config, configType }) => {
    try {
      const configPath = paths.settings.root;
      await fs.mkdir(path.dirname(configPath), { recursive: true });

      // 读取现有配置
      const existingConfig = await readSettingsFile(configPath);

      // 如果指定了配置类型，只更新该类型的配置
      if (configType && typeof configType === 'string') {
        existingConfig[configType] = config;
      } else {
        // 否则更新整个配置
        Object.assign(existingConfig, config);
      }

      // 写入配置文件
      await fs.writeFile(configPath, JSON.stringify(existingConfig, null, 2));

      return true;
    } catch (error) {
      logger.error('保存配置失败', error);
      throw error;
    }
  }
};

module.exports = settingsHandlers;
