/**
 * 发布记录IPC处理程序
 */
const { PUBLISH_RECORD } = require('./channels');
const publishRecordService = require('../publish-record-service');
const logger = require('../../utils/logger');

/**
 * 获取所有发布记录
 * @returns {Array} 发布记录列表
 */
const getPublishRecords = async () => {
  try {
    const records = publishRecordService.getRecords();
    return records;
  } catch (error) {
    logger.error('获取发布记录失败:', error);
    throw {
      message: '获取发布记录失败',
      code: 'ERR_GET_PUBLISH_RECORDS',
      details: error
    };
  }
};

/**
 * 创建发布记录
 * @param {Object} event - IPC事件对象
 * @param {Object} recordData - 记录数据
 * @returns {Object} 创建的记录
 */
const createPublishRecord = async (event, recordData) => {
  try {
    if (!recordData || !recordData.resources || !recordData.devices) {
      throw new Error('记录数据不完整');
    }

    // 确保数据是可序列化的，并且是基本类型
    const cleanRecordData = {
      name: String(recordData.name || ''),
      resources: Array.isArray(recordData.resources)
        ? recordData.resources.map(r => Number(r))
        : [],
      devices: Array.isArray(recordData.devices)
        ? recordData.devices.map(d => String(d))
        : [],
      loopPlay: Boolean(recordData.loopPlay),
      showPlayScreen: Boolean(recordData.showPlayScreen)
    };

    // 记录处理后的数据，便于调试
    console.log('处理后的记录数据:', JSON.stringify(cleanRecordData));

    const record = await publishRecordService.createRecord(cleanRecordData);
    return record;
  } catch (error) {
    logger.error('创建发布记录失败:', error);
    throw {
      message: '创建发布记录失败',
      code: 'ERR_CREATE_PUBLISH_RECORD',
      details: error
    };
  }
};

/**
 * 更新发布记录
 * @param {Object} event - IPC事件对象
 * @param {number} id - 记录ID
 * @param {Object} updateData - 更新数据
 * @returns {Object} 更新后的记录
 */
const updatePublishRecord = async (event, id, updateData) => {
  try {
    if (!id) {
      throw new Error('记录ID不能为空');
    }

    const record = await publishRecordService.updateRecord(id, updateData);
    if (!record) {
      throw new Error(`未找到ID为 ${id} 的发布记录`);
    }

    return record;
  } catch (error) {
    logger.error('更新发布记录失败:', error);
    throw {
      message: '更新发布记录失败',
      code: 'ERR_UPDATE_PUBLISH_RECORD',
      details: error
    };
  }
};

/**
 * 停止发布记录
 * @param {Object} event - IPC事件对象
 * @param {number} id - 记录ID
 * @returns {Object} 更新后的记录
 */
const stopPublishRecord = async (event, id) => {
  try {
    if (!id) {
      throw new Error('记录ID不能为空');
    }

    const record = await publishRecordService.stopRecord(id);
    if (!record) {
      throw new Error(`未找到ID为 ${id} 的发布记录`);
    }

    return record;
  } catch (error) {
    logger.error('停止发布记录失败:', error);
    throw {
      message: '停止发布记录失败',
      code: 'ERR_STOP_PUBLISH_RECORD',
      details: error
    };
  }
};

/**
 * 激活发布记录（再次发布）
 * @param {Object} event - IPC事件对象
 * @param {number} id - 记录ID
 * @returns {Object} 更新后的记录
 */
const activatePublishRecord = async (event, id) => {
  try {
    if (!id) {
      throw new Error('记录ID不能为空');
    }

    const record = await publishRecordService.activateRecord(id);
    if (!record) {
      throw new Error(`未找到ID为 ${id} 的发布记录`);
    }

    return record;
  } catch (error) {
    logger.error('激活发布记录失败:', error);
    throw {
      message: '激活发布记录失败',
      code: 'ERR_ACTIVATE_PUBLISH_RECORD',
      details: error
    };
  }
};

/**
 * 删除发布记录
 * @param {Object} event - IPC事件对象
 * @param {number} id - 记录ID
 * @returns {boolean} 是否删除成功
 */
const deletePublishRecord = async (event, id) => {
  try {
    if (!id) {
      throw new Error('记录ID不能为空');
    }

    const success = await publishRecordService.deleteRecord(id);
    if (!success) {
      throw new Error(`未找到ID为 ${id} 的发布记录`);
    }

    return true;
  } catch (error) {
    logger.error('删除发布记录失败:', error);
    throw {
      message: '删除发布记录失败',
      code: 'ERR_DELETE_PUBLISH_RECORD',
      details: error
    };
  }
};

/**
 * 清空所有发布记录
 * @returns {boolean} 是否清空成功
 */
const clearPublishRecords = async () => {
  try {
    const success = await publishRecordService.clearRecords();
    return success;
  } catch (error) {
    logger.error('清空发布记录失败:', error);
    throw {
      message: '清空发布记录失败',
      code: 'ERR_CLEAR_PUBLISH_RECORDS',
      details: error
    };
  }
};

// 导出处理程序
module.exports = {
  [PUBLISH_RECORD.GET_PUBLISH_RECORDS]: getPublishRecords,
  [PUBLISH_RECORD.CREATE_PUBLISH_RECORD]: createPublishRecord,
  [PUBLISH_RECORD.UPDATE_PUBLISH_RECORD]: updatePublishRecord,
  [PUBLISH_RECORD.STOP_PUBLISH_RECORD]: stopPublishRecord,
  [PUBLISH_RECORD.ACTIVATE_PUBLISH_RECORD]: activatePublishRecord,
  [PUBLISH_RECORD.DELETE_PUBLISH_RECORD]: deletePublishRecord,
  [PUBLISH_RECORD.CLEAR_PUBLISH_RECORDS]: clearPublishRecords
};
