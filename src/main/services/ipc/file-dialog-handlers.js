/**
 * 文件对话框处理器
 * 处理文件选择对话框相关的IPC请求
 */

const { dialog } = require('electron');
const logger = require('../../utils/logger');

/**
 * 显示文件选择对话框
 * @param {Electron.IpcMainInvokeEvent} event - IPC事件对象
 * @param {Object} options - 对话框选项
 * @returns {Promise<string[]>} 选择的文件路径数组
 */
const selectFile = async (event, options) => {
  try {
    logger.debug('显示文件选择对话框', options);

    // 获取BrowserWindow实例
    const window = event.sender.getOwnerBrowserWindow();

    // 设置默认选项
    const defaultOptions = {
      properties: ['openFile'],
      filters: [
        { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'gif', 'svg'] },
        { name: '所有文件', extensions: ['*'] }
      ]
    };

    // 合并选项
    const dialogOptions = {
      ...defaultOptions,
      ...options
    };

    // 显示对话框
    const result = await dialog.showOpenDialog(window, dialogOptions);

    // 如果用户取消了选择，返回空数组
    if (result.canceled) {
      logger.debug('用户取消了文件选择');
      return [];
    }

    logger.debug('用户选择的文件:', result.filePaths);
    return result.filePaths;
  } catch (error) {
    logger.error('显示文件选择对话框失败', error);
    throw error;
  }
};

// 导出处理器
module.exports = {
  'select-file': selectFile
};
