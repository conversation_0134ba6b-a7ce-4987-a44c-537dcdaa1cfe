/**
 * 流媒体相关IPC处理器
 */

const { ipcMain } = require('electron');
const rtspProxyService = require('../stream/rtsp-proxy-service');
const logger = require('../../utils/logger');
const { STREAM } = require('./channels');

/**
 * 流媒体相关IPC处理器
 */
const streamHandlers = {
  // 启动RTSP流代理
  [STREAM.START]: async (_, { sn, rtspUrl }) => {
    try {
      logger.debug('启动RTSP流代理', { sn, rtspUrl });
      return await rtspProxyService.startStream(sn, rtspUrl);
    } catch (error) {
      logger.error(`启动RTSP流代理失败: ${sn}, URL: ${rtspUrl}`, error);
      throw error;
    }
  },

  // 停止RTSP流代理
  [STREAM.STOP]: async (_, sn) => {
    try {
      logger.debug('停止RTSP流代理', { sn });
      return await rtspProxyService.stopStream(sn);
    } catch (error) {
      logger.error(`停止RTSP流代理失败: ${sn}`, error);
      throw error;
    }
  },

  // 获取流信息
  [STREAM.GET_INFO]: (_, sn) => {
    try {
      logger.debug('获取流信息', { sn });
      return rtspProxyService.getStreamInfo(sn);
    } catch (error) {
      logger.error(`获取流信息失败: ${sn}`, error);
      throw error;
    }
  },

  // 获取所有流信息
  [STREAM.GET_ALL]: () => {
    try {
      logger.debug('获取所有流信息');
      return rtspProxyService.getAllStreams();
    } catch (error) {
      logger.error('获取所有流信息失败', error);
      throw error;
    }
  }
};

// 导出IPC通道和处理器
module.exports = {
  STREAM,
  streamHandlers
};
