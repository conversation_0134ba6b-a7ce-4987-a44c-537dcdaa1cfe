/**
 * 升级相关IPC处理器
 */

const updateController = require('../../controllers/update-controller');
const logger = require('../../utils/logger');

// 升级相关频道常量
const UPDATE = {
  CHECK: 'check-update',
  DOWNLOAD: 'download-update',
  INSTALL: 'install-update'
};

/**
 * 升级相关IPC处理器
 */
const updateHandlers = {
  // 检查更新
  [UPDATE.CHECK]: async () => {
    try {
      logger.debug('收到检查更新请求');
      return await updateController.checkUpdate();
    } catch (error) {
      logger.error('检查更新失败', error);
      throw error;
    }
  },

  // 下载更新
  [UPDATE.DOWNLOAD]: async () => {
    try {
      logger.debug('收到下载更新请求');
      return await updateController.downloadUpdate();
    } catch (error) {
      logger.error('下载更新失败', error);
      throw error;
    }
  },

  // 安装更新
  [UPDATE.INSTALL]: async () => {
    try {
      logger.debug('收到安装更新请求');
      return await updateController.installUpdate();
    } catch (error) {
      logger.error('安装更新失败', error);
      throw error;
    }
  }
};

// 导出
module.exports = {
  UPDATE,
  updateHandlers
}; 