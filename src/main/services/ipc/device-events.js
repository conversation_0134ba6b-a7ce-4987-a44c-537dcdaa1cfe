/**
 * 设备事件处理模块
 * 处理设备相关的IPC事件
 */

const { ipcMain } = require('electron');
const deviceController = require('../../controllers/device-controller');
const logger = require('../../utils/logger');
const { CommandType } = require('../../../shared/constants/commands');

/**
 * 设置设备事件监听器
 * @returns {void}
 */
function setupDeviceEventListeners() {
  // 设备重启事件
  ipcMain.on('device:restart', async (_, sn) => {
    try {
      logger.info(`接收到设备重启事件: ${sn}`);

      // 检查设备是否在线
      if (!deviceController.isDeviceOnline(sn)) {
        logger.warn(`设备不在线，无法重启: ${sn}`);
        return;
      }

      // 重启设备
      await deviceController.rebootDevice(sn);
      logger.info(`设备重启命令已发送: ${sn}`);
    } catch (error) {
      logger.error(`设备重启失败: ${sn}`, error);
    }
  });

  // 设备关机事件
  ipcMain.on('device:shutdown', async (_, sn) => {
    try {
      logger.info(`接收到设备关机事件: ${sn}`);

      // 检查设备是否在线
      if (!deviceController.isDeviceOnline(sn)) {
        logger.warn(`设备不在线，无法关机: ${sn}`);
        return;
      }

      // 关闭设备
      await deviceController.shutdownDevice(sn);
      logger.info(`设备关机命令已发送: ${sn}`);
    } catch (error) {
      logger.error(`设备关机失败: ${sn}`, error);
    }
  });

  // 批量控制音量事件
  ipcMain.on('device:batch-volume', async (event, { deviceList, volume }) => {
    try {
      logger.info(`接收到批量控制音量事件: ${deviceList.join(', ')}, 音量: ${volume}`);

      // 批量控制音量
      const result = await deviceController.batchControlVolume(deviceList, volume);
      
      // 发送结果回渲染进程
      event.sender.send('device:batch-volume-result', result);
      
      logger.info(`批量控制音量完成: ${JSON.stringify(result)}`);
    } catch (error) {
      logger.error('批量控制音量失败', error);
      event.sender.send('device:batch-volume-result', {
        successful: [],
        failed: deviceList.map(sn => ({ sn, error: error.message || '未知错误' }))
      });
    }
  });

  // 批量控制屏幕事件
  ipcMain.on('device:batch-screen', async (event, { deviceList, turnOn }) => {
    try {
      logger.info(`接收到批量控制屏幕事件: ${deviceList.join(', ')}, 操作: ${turnOn ? '开启' : '关闭'}`);

      // 批量控制屏幕
      const result = await deviceController.batchControlScreen(deviceList, turnOn);
      
      // 发送结果回渲染进程
      event.sender.send('device:batch-screen-result', result);
      
      logger.info(`批量控制屏幕完成: ${JSON.stringify(result)}`);
    } catch (error) {
      logger.error('批量控制屏幕失败', error);
      event.sender.send('device:batch-screen-result', {
        successful: [],
        failed: deviceList.map(sn => ({ sn, error: error.message || '未知错误' }))
      });
    }
  });

  // 批量控制快捷入口事件
  ipcMain.on('device:batch-shortcut', async (event, { deviceList, show }) => {
    try {
      logger.info(`接收到批量控制快捷入口事件: ${deviceList.join(', ')}, 操作: ${show ? '显示' : '隐藏'}`);

      // 批量控制快捷入口
      const result = await deviceController.batchControlShortcut(deviceList, show);
      
      // 发送结果回渲染进程
      event.sender.send('device:batch-shortcut-result', result);
      
      logger.info(`批量控制快捷入口完成: ${JSON.stringify(result)}`);
    } catch (error) {
      logger.error('批量控制快捷入口失败', error);
      event.sender.send('device:batch-shortcut-result', {
        successful: [],
        failed: deviceList.map(sn => ({ sn, error: error.message || '未知错误' }))
      });
    }
  });

  logger.info('设备事件监听器已设置');
}

module.exports = {
  setupDeviceEventListeners
};
