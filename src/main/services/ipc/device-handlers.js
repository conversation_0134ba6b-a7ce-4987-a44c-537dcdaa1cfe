/**
 * 设备相关IPC处理器
 */

const deviceController = require('../../controllers/device-controller');
const logger = require('../../utils/logger');
const { DEVICE } = require('./channels');

/**
 * 验证设备是否在线
 * @param {string} sn - 设备序列号
 * @throws {Object} 如果设备不在线，抛出错误
 */
function validateDeviceOnline(sn) {
  if (!deviceController.isDeviceOnline(sn)) {
    logger.warn(`设备不在线，无法执行操作: ${sn}`);
    throw {
      message: `设备不在线: ${sn}`,
      code: 'DEVICE_OFFLINE',
      details: { sn }
    };
  }
}

/**
 * 验证设备列表
 * @param {Array<string>} deviceList - 设备列表
 * @throws {Object} 如果设备列表无效，抛出错误
 */
function validateDeviceList(deviceList) {
  if (!deviceList || !Array.isArray(deviceList) || deviceList.length === 0) {
    logger.error('设备列表为空或格式不正确');
    throw {
      message: '设备列表为空或格式不正确',
      code: 'INVALID_DEVICE_LIST',
      details: { deviceList }
    };
  }
}

/**
 * 设备相关IPC处理器
 */
const deviceHandlers = {
  // 获取设备列表
  [DEVICE.GET_LIST]: () => deviceController.getConnectedDevices(),

  // 删除设备
  [DEVICE.DELETE]: async (_, sn) => {
    return await deviceController.deleteDevice(sn);
  },

  // 发送命令
  [DEVICE.SEND_COMMAND]: async (_, { sn, command }) => {
    validateDeviceOnline(sn);
    logger.debug('发送命令', { sn, command });
    return await deviceController.sendCommand(sn, command);
  },

  // 获取连接的设备
  [DEVICE.GET_CONNECTED]: () => deviceController.getConnectedDevices(),

  // 获取设备历史
  [DEVICE.GET_HISTORY]: () => deviceController.getDeviceHistory(),

  // 检查设备是否在线
  [DEVICE.IS_ONLINE]: (_, sn) => deviceController.isDeviceOnline(sn),

  // 添加设备
  [DEVICE.ADD]: async (_, sn) => {
    return await deviceController.addDevice(sn);
  },

  // 获取所有设备
  [DEVICE.GET_ALL]: () => deviceController.getAllDevices(),

  // 检查设备是否已添加
  [DEVICE.IS_ADDED]: (_, sn) => deviceController.isDeviceAdded(sn),

  // 播放资源
  [DEVICE.PLAY_RESOURCE]: async (_, { sn, index, loopPlay }) => {
    validateDeviceOnline(sn);
    logger.debug('播放资源', { sn, index, loopPlay });
    return await deviceController.playResource(sn, index, loopPlay);
  },

  // 停止播放
  [DEVICE.STOP_PLAY]: async (_, sn) => {
    validateDeviceOnline(sn);
    logger.debug('停止播放', { sn });
    return await deviceController.stopPlay(sn);
  },

  // 重启设备
  [DEVICE.REBOOT]: async (_, sn) => {
    validateDeviceOnline(sn);
    logger.debug('重启设备', { sn });
    return await deviceController.rebootDevice(sn);
  },

  // 关闭设备
  [DEVICE.SHUTDOWN]: async (_, sn) => {
    validateDeviceOnline(sn);
    logger.debug('关闭设备', { sn });
    return await deviceController.shutdownDevice(sn);
  },

  // 获取设备音量
  [DEVICE.GET_VOLUME]: async (_, sn) => {
    validateDeviceOnline(sn);
    logger.debug('获取设备音量', { sn });
    return await deviceController.getDeviceVolume(sn);
  },

  // 设置设备音量
  [DEVICE.SET_VOLUME]: async (_, { sn, volume }) => {
    validateDeviceOnline(sn);
    logger.debug('设置设备音量', { sn, volume });
    return await deviceController.setDeviceVolume(sn, volume);
  },

  // 点亮设备屏幕
  [DEVICE.SCREEN_ON]: async (_, sn) => {
    validateDeviceOnline(sn);
    logger.debug('点亮设备屏幕', { sn });
    return await deviceController.turnScreenOn(sn);
  },

  // 关闭设备屏幕
  [DEVICE.SCREEN_OFF]: async (_, sn) => {
    validateDeviceOnline(sn);
    logger.debug('关闭设备屏幕', { sn });
    return await deviceController.turnScreenOff(sn);
  },

  // 开启设备定位
  [DEVICE.LOCATE_START]: async (_, sn) => {
    validateDeviceOnline(sn);
    logger.debug('开启设备定位', { sn });
    return await deviceController.startLocateDevice(sn);
  },

  // 停止设备定位
  [DEVICE.LOCATE_STOP]: async (_, sn) => {
    validateDeviceOnline(sn);
    logger.debug('停止设备定位', { sn });
    return await deviceController.stopLocateDevice(sn);
  },

  // 重置设备视野
  [DEVICE.RESET_VIEW]: async (_, sn) => {
    validateDeviceOnline(sn);
    logger.debug('重置设备视野', { sn });
    return await deviceController.resetDeviceView(sn);
  },

  // 控制应用
  [DEVICE.APP_CONTROL]: async (_, sn, packageName, open, resourceIndex) => {
    validateDeviceOnline(sn);
    logger.debug('控制应用', { sn, packageName, open, resourceIndex });
    return await deviceController.controlApp(sn, packageName, open, resourceIndex);
  },

  // 批量播放资源
  [DEVICE.BATCH_PLAY]: async (_, { deviceList, index, loopPlay }) => {
    validateDeviceList(deviceList);
    logger.debug('批量播放资源', {
      deviceCount: deviceList.length,
      index,
      loopPlay
    });
    return await deviceController.batchPlayResource(deviceList, index, loopPlay);
  },

  // 批量停止播放
  [DEVICE.BATCH_STOP]: async (_, deviceList) => {
    validateDeviceList(deviceList);
    logger.debug('批量停止播放', { deviceCount: deviceList.length });
    return await deviceController.batchStopPlay(deviceList);
  },

  // 获取设备组列表
  [DEVICE.GET_GROUPS]: () => {
    logger.debug('获取设备组列表');
    return deviceController.getDeviceGroups();
  },

  // 获取特定设备组
  [DEVICE.GET_GROUP]: (_, groupId) => {
    logger.debug('获取设备组', { groupId });
    return deviceController.getDeviceGroup(groupId);
  },

  // 创建设备组
  [DEVICE.CREATE_GROUP]: async (_, { name, description }) => {
    logger.debug('创建设备组', { name, description });
    return await deviceController.createDeviceGroup(name, description);
  },

  // 更新设备组
  [DEVICE.UPDATE_GROUP]: async (_, { groupId, data }) => {
    logger.debug('更新设备组', { groupId, data });
    return await deviceController.updateDeviceGroup(groupId, data);
  },

  // 删除设备组
  [DEVICE.DELETE_GROUP]: async (_, groupId) => {
    logger.debug('删除设备组', { groupId });
    return await deviceController.deleteDeviceGroup(groupId);
  },

  // 添加设备到组
  [DEVICE.ADD_TO_GROUP]: async (_, { groupId, sn }) => {
    logger.debug('添加设备到组', { groupId, sn });
    return await deviceController.addDeviceToGroup(groupId, sn);
  },

  // 从组中移除设备
  [DEVICE.REMOVE_FROM_GROUP]: async (_, { groupId, sn }) => {
    logger.debug('从组中移除设备', { groupId, sn });
    return await deviceController.removeDeviceFromGroup(groupId, sn);
  },

  // 获取组内设备
  [DEVICE.GET_DEVICES_BY_GROUP]: async (_, groupId) => {
    logger.debug('获取组内设备', { groupId });
    return await deviceController.getDevicesByGroup(groupId);
  },

  // 更新设备ID
  [DEVICE.UPDATE_DEVICE_ID]: async (_, { sn, id, forceSwap }) => {
    logger.debug('更新设备ID', { sn, id, forceSwap });
    return await deviceController.updateDeviceId(sn, id, forceSwap);
  },

  // 批量控制快捷入口
  [DEVICE.BATCH_SHORTCUT]: async (_, { deviceList, show }) => {
    validateDeviceList(deviceList);
    logger.debug('批量控制快捷入口', {
      deviceCount: deviceList.length,
      show
    });
    return await deviceController.batchControlShortcut(deviceList, show);
  },

  // 快捷入口控制
  [DEVICE.QUICK_ACCESS]: async (_, { sn, isShow }) => {
    validateDeviceOnline(sn);
    logger.debug('控制快捷入口', { sn, isShow });
    return await deviceController.quickAccess(sn, isShow);
  }
};

module.exports = deviceHandlers;
