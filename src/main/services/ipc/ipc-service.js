/**
 * IPC服务类
 * 负责管理IPC通信
 */

const { ipcMain } = require('electron');
const logger = require('../../utils/logger');
const { CHANNELS } = require('./channels');

/**
 * IPC服务类
 * @class
 */
class IpcService {
  /**
   * 构造函数
   */
  constructor() {
    this.handlers = new Map();
    this.initialized = false;
    logger.debug('IpcService: 初始化完成');
  }

  /**
   * 初始化IPC服务
   * @returns {Promise<void>}
   */
  async init() {
    if (this.initialized) {
      logger.warn('IpcService: 已经初始化');
      return;
    }

    try {
      // 注册所有处理器
      this.registerHandlers();

      // 设置设备事件监听器
      const { setupDeviceEventListeners } = require('./device-events');
      setupDeviceEventListeners();

      this.initialized = true;
      logger.info('IpcService: 初始化完成');
    } catch (error) {
      logger.error('IpcService: 初始化失败', error);
      throw error;
    }
  }

  /**
   * 注册处理器
   * @param {string} channel - IPC通道名称
   * @param {Function} handler - 处理函数
   * @returns {void}
   */
  register(channel, handler) {
    if (this.handlers.has(channel)) {
      logger.warn(`IpcService: 通道 ${channel} 已注册，将被覆盖`);
    }

    // 包装处理器，添加错误处理和日志记录
    const wrappedHandler = async (event, ...args) => {
      try {
        logger.debug(`IpcService: 处理请求 ${channel}`, { args });
        const result = await handler(event, ...args);
        return result;
      } catch (error) {
        logger.error(`IpcService: 处理请求 ${channel} 失败`, error);

        // 如果错误已经是格式化的错误对象，直接抛出
        if (error.code) {
          throw error;
        }

        // 否则，创建一个格式化的错误对象
        throw {
          message: error.message || `处理请求 ${channel} 失败`,
          code: error.code || 'ERR_IPC_HANDLER',
          details: error.details || {},
          context: { channel, args }
        };
      }
    };

    // 注册处理器
    ipcMain.handle(channel, wrappedHandler);
    this.handlers.set(channel, wrappedHandler);
    logger.debug(`IpcService: 注册处理器 ${channel}`);
  }

  /**
   * 注册多个处理器
   * @param {Object} handlers - 处理器对象，键为通道名称，值为处理函数
   * @returns {void}
   */
  registerBulk(handlers) {
    Object.entries(handlers).forEach(([channel, handler]) => {
      logger.debug(`IpcService: 注册处理器 ${channel}`);
      this.register(channel, handler);
    });
  }

  /**
   * 移除处理器
   * @param {string} channel - IPC通道名称
   * @returns {boolean} 是否成功移除
   */
  unregister(channel) {
    if (!this.handlers.has(channel)) {
      logger.warn(`IpcService: 通道 ${channel} 未注册`);
      return false;
    }

    ipcMain.removeHandler(channel);
    this.handlers.delete(channel);
    logger.debug(`IpcService: 移除处理器 ${channel}`);
    return true;
  }

  /**
   * 移除所有处理器
   * @returns {void}
   */
  unregisterAll() {
    this.handlers.forEach((_, channel) => {
      ipcMain.removeHandler(channel);
    });
    this.handlers.clear();
    logger.debug('IpcService: 移除所有处理器');
  }

  /**
   * 注册所有处理器
   * @private
   * @returns {void}
   */
  registerHandlers() {
    // 导入所有处理器模块
    const deviceHandlers = require('./device-handlers');
    const fileHandlers = require('./file-handlers');
    const fileDialogHandlers = require('./file-dialog-handlers');
    const settingsHandlers = require('./settings-handlers');
    const solutionHandlers = require('./solution-handlers');
    const resourceHandlers = require('./resource-handlers');
    const windowHandlers = require('./window-handlers');
    const { streamHandlers } = require('./stream-handlers');
    const { appHandlers } = require('./app-handlers');
    const publishRecordHandlers = require('./publish-record-handlers');
    const { updateHandlers } = require('./update-handlers');

    // 注册所有处理器
    logger.debug('IpcService: 开始注册处理器');
    this.registerBulk(deviceHandlers);
    this.registerBulk(fileHandlers);
    this.registerBulk(fileDialogHandlers);
    this.registerBulk(settingsHandlers);
    this.registerBulk(solutionHandlers);
    this.registerBulk(resourceHandlers);
    this.registerBulk(windowHandlers);
    this.registerBulk(streamHandlers);
    this.registerBulk(appHandlers);
    this.registerBulk(publishRecordHandlers);
    this.registerBulk(updateHandlers);
    logger.debug('IpcService: 处理器注册完成');
  }

  /**
   * 清理资源
   * @returns {void}
   */
  cleanup() {
    this.unregisterAll();
    this.initialized = false;
    logger.debug('IpcService: 清理完成');
  }
}

// 创建单例
const ipcService = new IpcService();

// 导出单例
module.exports = ipcService;
