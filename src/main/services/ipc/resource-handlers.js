/**
 * 资源相关IPC处理器
 */

const fs = require('fs').promises;
const resourceController = require('../../controllers/resource-controller');
const deviceController = require('../../controllers/device-controller');
const { wsService } = require('../../services/network');
const { paths } = require('../../utils/paths');
const logger = require('../../utils/logger');
const { RESOURCE } = require('./channels');
const { FileType } = require('../../../shared/types/resource');

/**
 * 资源相关IPC处理器
 */
const resourceHandlers = {
  // 创建资源
  [RESOURCE.CREATE]: async (_, data) => {
    logger.debug('创建资源', data);

    // 检查数据格式
    if (data.resource && data.group) {
      // 从前端传来的 resource 对象构建 formData
      const formData = {
        name: data.resource.showName,
        description: data.resource.describe || '',
        group: data.group,
        coverPath: data.resource.poster || ''
      };

      // 构建 files 数组
      const files = [{
        path: data.resource.path
      }];

      // 确保 formData 包含 type 字段
      if (data.resource.type !== undefined) {
        formData.type = data.resource.type;
        logger.debug(`使用资源指定的类型: ${formData.type}`);
      }

      logger.debug('处理后的参数', { files, formData });

      return await resourceController.createResource(files, formData);
    } else if (data.files && data.formData) {
      // 原有的调用方式
      return await resourceController.createResource(data.files, data.formData);
    } else {
      throw new Error('创建资源失败: 参数格式不正确');
    }
  },

  // 获取资源列表
  [RESOURCE.GET]: () => resourceController.getResources(),

  // 删除资源
  [RESOURCE.DELETE]: (_, uuid) => resourceController.deleteResource(uuid),

  // 保存配置
  [RESOURCE.SAVE_CONFIG]: async (_, config) => {
    await fs.writeFile(paths.resources.config, JSON.stringify(config, null, 2), 'utf8');
    return true;
  },

  // 删除分组 - 已废弃，请使用 SOLUTION.DELETE_GROUP
  [RESOURCE.DELETE_GROUP]: (_, groupName) => {
    logger.warn('使用已废弃的 RESOURCE.DELETE_GROUP 通道，请改用 SOLUTION.DELETE_GROUP');
    // 由于 solutionService.deleteGroup 需要 solutionId 参数，这里无法直接调用
    // 返回一个错误，提示客户端使用新的 API
    throw new Error('此 API 已废弃，请使用 deleteSolutionGroup(solutionId, name)');
  },

  // 更新资源
  [RESOURCE.UPDATE]: (_, resource) => resourceController.updateResource(resource),

  // 发布资源
  [RESOURCE.PUBLISH]: async (_, { resourceIndex, deviceList, loopPlay }) => {
    // 确保参数是基本类型
    const cleanResourceIndex = Number(resourceIndex);
    const cleanDeviceList = Array.isArray(deviceList)
      ? deviceList.map(sn => String(sn))
      : [];
    const cleanLoopPlay = Boolean(loopPlay);

    // 获取资源信息
    const resources = await resourceController.getResources();

    // 检查资源是否存在
    if (!resources || resources.length === 0 || !resources[0] || !resources[0].list || !Array.isArray(resources[0].list)) {
      logger.error('资源列表为空或格式不正确');
      throw new Error('资源列表为空或格式不正确');
    }

    // 检查资源索引是否有效
    if (cleanResourceIndex < 0 || cleanResourceIndex >= resources[0].list.length) {
      logger.error(`资源索引无效: ${cleanResourceIndex}，资源总数: ${resources[0].list.length}`);
      throw new Error('资源索引无效');
    }

    const resource = resources[0].list[cleanResourceIndex];

    // 特殊处理：如果资源类型是应用(APK)
    if (resource.type === FileType.APP) {
      // 获取应用包名
      let packageName = resource.pkg || '';
      let open = true; // 默认为打开应用
      
      // 使用设备控制器批量控制应用
      return await deviceController.batchControlApp(
        cleanDeviceList, 
        packageName, 
        open,
        cleanResourceIndex  // 添加资源索引参数
      );
    }

    // 正常资源播放流程
    logger.debug('发布资源', {
      resourceIndex: cleanResourceIndex,
      resourceName: resource.showName || resource.fileName,
      deviceCount: cleanDeviceList.length,
      devices: cleanDeviceList,
      loopPlay: cleanLoopPlay
    });

    // 使用设备控制器批量播放资源
    return await deviceController.batchPlayResource(cleanDeviceList, cleanResourceIndex, cleanLoopPlay);
  },

  // 停止资源
  [RESOURCE.STOP]: async (_, { deviceList, resourceType, packageName }) => {
    // 检查设备列表是否有效
    if (!deviceList || !Array.isArray(deviceList) || deviceList.length === 0) {
      logger.error('设备列表为空或格式不正确');
      throw new Error('设备列表为空或格式不正确');
    }

    // 确保设备列表中的每个元素都是字符串
    const cleanDeviceList = deviceList.map(sn => String(sn));

    // 如果是应用类型资源且提供了包名，使用应用播控命令停止
    if (resourceType === FileType.APP && packageName) {
      logger.debug('停止应用播放', {
        deviceCount: cleanDeviceList.length,
        devices: cleanDeviceList,
        packageName
      });

      // 使用应用播控命令（type=2020, open=false）停止应用
      return await deviceController.batchControlApp(cleanDeviceList, packageName, false);
    } else {
      // 对于其他类型资源，使用普通停止命令（type=2003）
      logger.debug('停止普通资源播放', {
        deviceCount: cleanDeviceList.length,
        devices: cleanDeviceList
      });

      // 使用设备控制器批量停止播放
      return await deviceController.batchStopPlay(cleanDeviceList);
    }
  }
};

module.exports = resourceHandlers;
