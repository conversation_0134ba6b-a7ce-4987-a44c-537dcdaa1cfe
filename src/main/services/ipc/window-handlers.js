/**
 * 窗口相关IPC处理器
 */

const { BrowserWindow } = require('electron');
const logger = require('../../utils/logger');
const { WINDOW } = require('./channels');

/**
 * 窗口相关IPC处理器
 */
const windowHandlers = {
  // 窗口控制
  [WINDOW.CONTROL]: async (event, command) => {
    const win = BrowserWindow.fromWebContents(event.sender);
    if (!win) {
      logger.warn('无法获取窗口实例');
      return;
    }

    switch (command) {
      case 'minimize':
        win.minimize();
        logger.debug('窗口已最小化');
        break;
      case 'maximize':
        if (win.isMaximized()) {
          win.restore();
          logger.debug('窗口已还原');
        } else {
          win.maximize();
          logger.debug('窗口已最大化');
        }
        break;
      case 'close':
        win.close();
        logger.debug('窗口已关闭');
        break;
      default:
        logger.warn(`未知的窗口命令: ${command}`);
    }
  }
};

module.exports = windowHandlers;
