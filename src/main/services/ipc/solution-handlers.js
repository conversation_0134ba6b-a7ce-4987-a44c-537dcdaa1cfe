/**
 * 解决方案相关IPC处理器
 */

const solutionController = require('../../controllers/solution-controller');
const resourceController = require('../../controllers/resource-controller');
const logger = require('../../utils/logger');
const { SOLUTION } = require('./channels');
const { solutionService } = require('../storage');
const fs = require('fs').promises;
const { paths } = require('../../utils/paths');
const configService = require('../config');
const { checkConfigFolderSize } = require('../../utils/disk-space');

/**
 * 解决方案相关IPC处理器
 */
const solutionHandlers = {
  // 获取解决方案列表
  [SOLUTION.GET]: () => resourceController.getResources(),

  // 创建解决方案
  [SOLUTION.CREATE]: (_, data) => {
    logger.debug('创建方案', { name: data.name, pcVersion: data.pcVersion });
    return solutionController.createSolution(data.files, data.name, data.description, data.pcVersion);
  },

  // 检查config文件夹大小
  [SOLUTION.CHECK_CONFIG_SIZE]: async () => {
    logger.debug('检查config文件夹大小');
    return await checkConfigFolderSize(paths.resources.root);
  },

  // 部署解决方案
  [SOLUTION.DEPLOY]: async (_, data) => {
    // 如果指定了增量部署参数，使用指定的值
    if (data.incremental !== undefined) {
      logger.debug('使用指定的部署参数', { incremental: data.incremental });
      return await solutionController.deploySolution(data);
    } else {
      // 默认使用增量部署
      logger.debug('使用默认增量部署');
      return await solutionController.deploySolution({ ...data, incremental: true });
    }
  },

  // 导出解决方案
  [SOLUTION.EXPORT]: (_, data) => {
    logger.debug('导出方案', data);
    // 兼容两种调用方式
    const solutionId = data.solutionId || (data.solution ? data.solution.UUID : null);
    return solutionController.exportSolution(solutionId);
  },

  // 取消部署
  [SOLUTION.CANCEL_DEPLOY]: (_, deviceSN) => {
    logger.debug('取消部署', { deviceSN });
    return solutionController.cancelDeploy(deviceSN);
  },

  // 删除解决方案
  [SOLUTION.DELETE]: (_, uuid) => {
    logger.debug('删除方案', { uuid });
    return resourceController.deleteSolution(uuid);
  },

  // 更新解决方案
  [SOLUTION.UPDATE]: async (_, solution) => {
    logger.debug('更新方案', solution);
    try {
      // 从配置文件中读取原始内容
      const configContent = await fs.readFile(paths.resources.config, 'utf8');
      const config = JSON.parse(configContent);

      // 检查UUID是否匹配
      if (config.UUID !== solution.UUID) {
        throw new Error(`方案不存在: ${solution.UUID}`);
      }

      // 更新字段
      if (solution.name !== undefined) {
        config.name = solution.name;
      }
      if (solution.description !== undefined) {
        config.description = solution.description;
      }
      if (solution.updatedAt !== undefined) {
        config.updatedAt = solution.updatedAt;
      }

      // 保存更新后的内容
      await fs.writeFile(paths.resources.config, JSON.stringify(config, null, 2));

      // 清除配置缓存
      configService.clearCache('resources');

      return config;
    } catch (error) {
      logger.error('更新方案失败', error);
      throw error;
    }
  },

  // 添加分组
  [SOLUTION.ADD_GROUP]: async (_, data) => {
    logger.debug('添加分组', data);
    const { solutionId, name, description } = data;
    return await solutionService.addGroup(solutionId, name, description);
  },

  // 更新分组
  [SOLUTION.UPDATE_GROUP]: async (_, data) => {
    logger.debug('更新分组', data);
    const { solutionId, oldName, newName, description } = data;
    return await solutionService.updateGroup(solutionId, oldName, newName, description);
  },

  // 删除分组
  [SOLUTION.DELETE_GROUP]: async (_, data) => {
    logger.debug('删除分组', data);

    // 打印原始数据
    console.log(`\n原始数据: ${JSON.stringify(data)}`);

    // 直接从 data 对象中获取属性，不做任何默认值处理
    const solutionId = data.solutionId;
    const name = data.name;
    const deleteResources = data.deleteResources;

    // 打印接收到的参数
    console.log(`\n开始删除分组: ${name} (方案ID: ${solutionId})`);
    console.log(`删除资源参数: ${deleteResources} (${typeof deleteResources})`);
    console.log('正在分析受影响的资源...');

    try {
      // 直接使用原始参数，不做任何转换
      const result = await solutionService.deleteGroup(solutionId, name, deleteResources);
      console.log(`分组 ${name} 删除成功\n`);
      return result;
    } catch (error) {
      console.error(`删除分组 ${name} 失败: ${error.message}\n`);
      throw error;
    }
  }
};

module.exports = {
  ...solutionHandlers,
  // 确保所有通道都被导出
  [SOLUTION.GET]: solutionHandlers[SOLUTION.GET],
  [SOLUTION.CREATE]: solutionHandlers[SOLUTION.CREATE],
  [SOLUTION.DEPLOY]: solutionHandlers[SOLUTION.DEPLOY],
  [SOLUTION.EXPORT]: solutionHandlers[SOLUTION.EXPORT],
  [SOLUTION.DELETE]: solutionHandlers[SOLUTION.DELETE],
  [SOLUTION.UPDATE]: solutionHandlers[SOLUTION.UPDATE],
  [SOLUTION.ADD_GROUP]: solutionHandlers[SOLUTION.ADD_GROUP],
  [SOLUTION.UPDATE_GROUP]: solutionHandlers[SOLUTION.UPDATE_GROUP],
  [SOLUTION.DELETE_GROUP]: solutionHandlers[SOLUTION.DELETE_GROUP],
  [SOLUTION.CANCEL_DEPLOY]: solutionHandlers[SOLUTION.CANCEL_DEPLOY],
  [SOLUTION.CHECK_CONFIG_SIZE]: solutionHandlers[SOLUTION.CHECK_CONFIG_SIZE]
};
