/**
 * WebSocket服务类
 * 负责WebSocket连接和通信，不处理设备业务逻辑
 */

const { EventEmitter } = require('events');
const WebSocket = require('ws');
const logger = require('../../utils/logger');
const { createNetworkError } = require('../../utils/error-handler');
const { CommandType } = require('../../../shared/constants/command-types');



/**
 * WebSocket服务类
 * @class
 * @extends EventEmitter
 */
class WebSocketService extends EventEmitter {
  /**
   * WebSocket事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      DEVICE_CONNECTED: 'deviceConnected',
      DEVICE_DISCONNECTED: 'deviceDisconnected',
      DEVICE_STATUS_UPDATE: 'deviceStatusUpdate',
      SCREEN_CAST_RESPONSE: 'screenCastResponse',
      SCREEN_CAST_STOP_RESPONSE: 'screenCastStopResponse',
      VOLUME_RESPONSE: 'volumeResponse',
      SET_VOLUME_RESPONSE: 'setVolumeResponse',
      SCREEN_ON_RESPONSE: 'screenOnResponse',
      SCREEN_OFF_RESPONSE: 'screenOffResponse',
      LOCATE_START_RESPONSE: 'locateStartResponse',
      LOCATE_STOP_RESPONSE: 'locateStopResponse',
      RESET_VIEW_RESPONSE: 'resetViewResponse',
      APP_CONTROL_RESPONSE: 'appControlResponse',
      QUICK_ACCESS_RESPONSE: 'quickAccessResponse',
      CONFIG_RESPONSE: 'configResponse',
      ERROR: 'error'
    };
  }

  /**
   * 构造函数
   */
  constructor() {
    super();
    this.clients = new Map(); // ip -> { ws, deviceStatus }
    this.deviceList = new Map(); // ip -> { status, lastSeen }
    this.snMap = new Map(); // sn -> ip
    this.server = null;
    this.isClosing = false;
    this.lastActiveTime = new Map(); // 记录每个客户端最后活动时间
    this.connectionMonitorInterval = null;
    this.TIMEOUT_THRESHOLD = 300000; // 5分钟无响应视为断开
    this.recentResponses = new Map(); // sn -> { timestamp, type } 用于去重

    logger.debug('WebSocketService: 初始化完成');
  }

  /**
   * 初始化WebSocket服务
   * @param {number} [port=50208] - 监听端口
   * @returns {Promise<this>} 服务实例
   */
  async init(port = 50208) {
    try {
      // 创建WebSocket服务器
      this.server = new WebSocket.Server({ port });
      this.server.on('connection', this._handleConnection.bind(this));

      // 启动连接监控
      this.startConnectionMonitor();

      logger.info(`WebSocket服务启动成功，监听端口: ${port}`);
      return this;
    } catch (error) {
      logger.error('初始化WebSocket服务失败', error);
      throw error;
    }
  }

  /**
   * 启动连接监控
   * @private
   */
  startConnectionMonitor() {
    this.connectionMonitorInterval = setInterval(() => {
      const now = Date.now();

      this.clients.forEach((client, clientIP) => {
        const lastActive = this.lastActiveTime.get(clientIP) || 0;

        // 如果超过阈值没有活动，认为连接已断开
        if (now - lastActive > this.TIMEOUT_THRESHOLD) {
          logger.warn(`连接超时: ${clientIP}，强制断开`);
          if (client.ws.readyState === WebSocket.OPEN) {
            client.ws.terminate(); // 强制关闭连接
          } else {
            // 如果连接已经不是OPEN状态，直接处理断开逻辑
            this._handleDisconnection(clientIP);
          }
        } else {
          // 发送心跳包检查连接状态
          try {
            client.ws.ping();
          } catch (error) {
            logger.error(`发送心跳包失败: ${clientIP}`, error);
            this._handleDisconnection(clientIP);
          }
        }
      });
    }, 10000); // 每10秒检查一次
  }



  /**
   * 获取当前连接的设备
   * @returns {Array<Object>} 连接的设备
   */
  getConnectedDevices() {
    const devices = [];
    this.deviceList.forEach((deviceInfo, ip) => {
      devices.push({
        ip,
        status: deviceInfo.status
      });
    });
    return devices;
  }

  /**
   * 检查设备是否在线
   * @param {string} sn - 设备序列号
   * @returns {boolean} 是否在线
   */
  isDeviceOnline(sn) {
    return this.snMap.has(sn);
  }

  /**
   * 获取设备IP
   * @param {string} sn - 设备序列号
   * @returns {string|undefined} 设备IP
   */
  getDeviceIP(sn) {
    return this.snMap.get(sn);
  }

  /**
   * 获取设备状态
   * @param {string} sn - 设备序列号
   * @returns {Object|null} 设备状态，如果设备不在线则返回null
   */
  getDeviceStatus(sn) {
    const ip = this.snMap.get(sn);
    if (ip) {
      const deviceInfo = this.deviceList.get(ip);
      if (deviceInfo) {
        return deviceInfo.status;
      }
    }
    return null;
  }

  /**
   * 处理WebSocket连接
   * @private
   * @param {WebSocket} ws - WebSocket连接
   * @param {Object} req - HTTP请求
   */
  _handleConnection(ws, req) {
    // 从X-Forwarded-For或X-Real-IP获取IP，如果都没有则使用远程地址
    const clientIP = req.headers['x-forwarded-for'] ||
                    req.headers['x-real-ip'] ||
                    req.socket.remoteAddress;

    // 记录初始活动时间
    this.lastActiveTime.set(clientIP, Date.now());

    let isFirstMessage = true;  // 标记是否是首条消息

    ws.on('message', async (message) => {
      try {
        const data = JSON.parse(message);
        logger.debug(`收到消息: ${clientIP}`, data);

        if (data.type === CommandType.STATUS) {
          const status = {
            sn: data.data.sn,
            timeStamp: data.data.timeStamp,
            deviceStatus: data.data.deviceStatus,
            onuse: data.data.onuse,
            playStatus: data.data.playStatus,
            controlled: data.data.controled, // 使用设备端的 controled 拼写
            battery: data.data.battery,
            monitorStatus: data.data.monitorStatus
          };

          // 检查SN是否已存在且连接的是不同IP
          const existingIP = this.snMap.get(status.sn);
          if (existingIP && existingIP !== clientIP) {
            logger.warn(`检测到重复SN: ${status.sn}，现有IP: ${existingIP}，新IP: ${clientIP}`);

            // 断开新连接
            ws.send(JSON.stringify({
              type: 2099,
              data: {
                error: 'Duplicate SN detected',
                message: 'This device SN is already connected from different IP'
              }
            }));

            ws.close(1000, 'Duplicate SN');
            return;
          }

          // 检查是否是首次连接或重新连接
          const isNewConnection = !this.deviceList.has(clientIP) ||
                                 this.deviceList.get(clientIP).status.sn !== status.sn;

          // 更新SN映射和设备列表
          this.snMap.set(status.sn, clientIP);
          this.clients.set(clientIP, { ws, deviceStatus: status });
          this.deviceList.set(clientIP, {
            status,
            lastSeen: Date.now()
          });

          // 只有在首次连接或重新连接时才触发设备连接事件
          if (isNewConnection) {
            logger.info(`设备首次连接或重新连接: ${status.sn} (${clientIP})`);
            this.emit(WebSocketService.EVENTS.DEVICE_CONNECTED, {
              ip: clientIP,
              status: status
            });
          }

          // 始终触发设备状态更新事件
          this.emit(WebSocketService.EVENTS.DEVICE_STATUS_UPDATE, {
            ip: clientIP,
            status: status
          });

          // 不再自动发送控制命令，由设备控制器来处理
          // 这样可以确保只向已添加的设备发送控制命令
          isFirstMessage = false;
        }
        // 处理投屏响应消息
        else if (data.type === CommandType.MONITOR_START_RES) {
          // 获取设备SN
          const sn = data.data.sn;
          if (!sn) {
            logger.warn(`收到无效的投屏响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          // 检查是否是重复的响应
          const now = Date.now();
          const recentResponse = this.recentResponses.get(sn);
          if (recentResponse &&
              recentResponse.type === CommandType.MONITOR_START_RES &&
              now - recentResponse.timestamp < 2000) { // 2秒内的重复响应
            logger.warn(`收到重复的投屏响应: ${sn}，忽略`);
            return;
          }

          // 记录响应
          this.recentResponses.set(sn, { timestamp: now, type: CommandType.MONITOR_START });

          logger.info(`收到设备 ${sn} 的投屏响应: ${JSON.stringify(data.data)}`);

          // 触发投屏响应事件
          this.emit(WebSocketService.EVENTS.SCREEN_CAST_RESPONSE, {
            sn: sn,
            status: data.data.status,
            address: data.data.address,
            port: data.data.port
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理停止投屏响应消息
        else if (data.type === CommandType.MONITOR_STOP_RES) {
          // 获取设备SN
          const sn = data.data.sn;
          if (!sn) {
            logger.warn(`收到无效的停止投屏响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          // 检查是否是重复的响应
          const now = Date.now();
          const recentResponse = this.recentResponses.get(sn);
          if (recentResponse &&
              recentResponse.type === CommandType.MONITOR_STOP_RES &&
              now - recentResponse.timestamp < 2000) { // 2秒内的重复响应
            logger.warn(`收到重复的停止投屏响应: ${sn}，忽略`);
            return;
          }

          // 记录响应
          this.recentResponses.set(sn, { timestamp: now, type: CommandType.MONITOR_STOP });

          logger.info(`收到设备 ${sn} 的停止投屏响应: ${JSON.stringify(data.data)}`);

          // 触发停止投屏响应事件
          this.emit(WebSocketService.EVENTS.SCREEN_CAST_STOP_RESPONSE, {
            sn: sn,
            status: data.data.status
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理获取音量响应消息
        else if (data.type === CommandType.GET_VOLUME) {
           // 获取设备SN
          const sn = data.sn;
          if (!sn) {
            logger.warn(`收到无效的音量响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          logger.info(`收到设备 ${sn} 的音量响应: ${JSON.stringify(data)}`);

          // 触发音量响应事件
          this.emit(WebSocketService.EVENTS.VOLUME_RESPONSE, {
            sn: sn,
            volume: data.volume
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理设置音量响应消息
        else if (data.type === CommandType.SET_VOLUME) {
          // 获取设备SN
          const sn = data.sn;
          if (!sn) {
            logger.warn(`收到无效的设置音量响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          logger.info(`收到设备 ${sn} 的设置音量响应: ${JSON.stringify(data)}`);

          // 触发设置音量响应事件
          this.emit(WebSocketService.EVENTS.SET_VOLUME_RESPONSE, {
            sn: sn,
            code: data.code
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理点亮屏幕响应消息
        else if (data.type === CommandType.SCREEN_ON) {
          // 获取设备SN
          const sn = data.sn;
          if (!sn) {
            logger.warn(`收到无效的点亮屏幕响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          logger.info(`收到设备 ${sn} 的点亮屏幕响应: ${JSON.stringify(data)}`);

          // 触发点亮屏幕响应事件
          this.emit(WebSocketService.EVENTS.SCREEN_ON_RESPONSE, {
            sn: sn,
            code: data.code
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理关闭屏幕响应消息
        else if (data.type === CommandType.SCREEN_OFF) {
          // 获取设备SN
          const sn = data.sn;
          if (!sn) {
            logger.warn(`收到无效的关闭屏幕响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          logger.info(`收到设备 ${sn} 的关闭屏幕响应: ${JSON.stringify(data)}`);

          // 触发关闭屏幕响应事件
          this.emit(WebSocketService.EVENTS.SCREEN_OFF_RESPONSE, {
            sn: sn,
            code: data.code
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理开启定位响应消息
        else if (data.type === CommandType.LOCATE_START) {
          // 获取设备SN
          const sn = data.sn;
          if (!sn) {
            logger.warn(`收到无效的开启定位响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          logger.info(`收到设备 ${sn} 的开启定位响应: ${JSON.stringify(data)}`);

          // 触发开启定位响应事件
          this.emit(WebSocketService.EVENTS.LOCATE_START_RESPONSE, {
            sn: sn,
            code: data.code
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理停止定位响应消息
        else if (data.type === CommandType.LOCATE_STOP) {
          // 获取设备SN
          const sn = data.sn;
          if (!sn) {
            logger.warn(`收到无效的停止定位响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          logger.info(`收到设备 ${sn} 的停止定位响应: ${JSON.stringify(data)}`);

          // 触发停止定位响应事件
          this.emit(WebSocketService.EVENTS.LOCATE_STOP_RESPONSE, {
            sn: sn,
            code: data.code
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理重置视野响应消息
        else if (data.type === CommandType.RESET_VIEW) {
          // 获取设备SN
          const sn = data.sn;
          if (!sn) {
            logger.warn(`收到无效的重置视野响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          logger.info(`收到设备 ${sn} 的重置视野响应: ${JSON.stringify(data)}`);

          // 触发重置视野响应事件
          this.emit(WebSocketService.EVENTS.RESET_VIEW_RESPONSE, {
            sn: sn,
            code: data.code
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理应用播控响应消息
        else if (data.type === CommandType.APP_CONTROL) {
          // 获取设备SN
          const sn = data.sn;
          if (!sn) {
            logger.warn(`收到无效的应用播控响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          logger.info(`收到设备 ${sn} 的应用播控响应: ${JSON.stringify(data)}`);

          // 触发应用播控响应事件
          this.emit(WebSocketService.EVENTS.APP_CONTROL_RESPONSE, {
            sn: sn,
            code: data.code,
            pkg: data.pkg
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理快捷入口响应消息
        else if (data.type === CommandType.QUICK_ACCESS) {
          // 获取设备SN
          const sn = data.sn;
          if (!sn) {
            logger.warn(`收到无效的快捷入口响应消息，缺少SN: ${clientIP}`, data);
            return;
          }

          logger.info(`收到设备 ${sn} 的快捷入口响应: ${JSON.stringify(data)}`);

          // 触发快捷入口响应事件
          this.emit(WebSocketService.EVENTS.QUICK_ACCESS_RESPONSE, {
            sn: sn,
            code: data.code
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
        // 处理配置响应消息
        else if (data.type === CommandType.FETCH_CONFIG) {
          // 从设备状态中获取SN
          const deviceInfo = this.deviceList.get(clientIP);
          if (!deviceInfo || !deviceInfo.status || !deviceInfo.status.sn) {
            logger.warn(`收到无效的配置响应消息，无法获取设备SN: ${clientIP}`, data);
            return;
          }

          const sn = deviceInfo.status.sn;
          logger.info(`收到设备 ${sn} 的配置响应: ${JSON.stringify(data)}`);

          // 触发配置响应事件
          this.emit(WebSocketService.EVENTS.CONFIG_RESPONSE, {
            sn: sn,
            code: data.code,
            config: data.data
          });

          // 更新最后活动时间
          this.lastActiveTime.set(clientIP, Date.now());
        }
      } catch (error) {
        logger.error('处理消息失败', error);
      }
    });

    ws.on('close', () => {
      logger.debug(`WebSocket连接关闭: ${clientIP}`);
      const deviceInfo = this.deviceList.get(clientIP);
      if (deviceInfo?.status?.sn) {
        // 只有当这个IP对应的SN在映射中时才删除
        if (this.snMap.get(deviceInfo.status.sn) === clientIP) {
          this.snMap.delete(deviceInfo.status.sn);
        }
      }
      this._handleDisconnection(clientIP);
    });

    ws.on('error', (error) => {
      logger.error(`WebSocket错误: ${clientIP}`, error);
      this.emit(WebSocketService.EVENTS.ERROR, {
        ip: clientIP,
        error: error.message
      });
    });

    // 处理心跳响应
    ws.on('pong', () => {
      this.lastActiveTime.set(clientIP, Date.now());
    });
  }

  /**
   * 处理设备断开连接
   * @private
   * @param {string} clientIP - 客户端IP
   */
  _handleDisconnection(clientIP) {
    const wasConnected = this.clients.has(clientIP);
    const deviceInfo = this.deviceList.get(clientIP);

    if (wasConnected && deviceInfo) {
      const sn = deviceInfo.status.sn;

      // 清理所有相关数据
      this.clients.delete(clientIP);
      this.deviceList.delete(clientIP);
      this.lastActiveTime.delete(clientIP);

      if (this.snMap.get(sn) === clientIP) {
        this.snMap.delete(sn);
      }

      // 发送断开事件
      this.emit(WebSocketService.EVENTS.DEVICE_DISCONNECTED, {
        ip: clientIP,
        sn: sn
      });

      logger.info(`设备断开连接: ${sn} (${clientIP})`);
    }
  }

  /**
   * 广播消息到所有客户端
   * @param {Object} message - 消息对象
   */
  broadcastMessage(message) {
    this.clients.forEach((client, ip) => {
      if (client.ws && client.ws.readyState === WebSocket.OPEN) {
        try {
          client.ws.send(JSON.stringify(message));
        } catch (error) {
          logger.error(`广播消息失败: ${ip}`, error);
        }
      }
    });
  }

  /**
   * 发送消息到指定客户端
   * @param {string} ip - 客户端IP
   * @param {Object} message - 消息对象
   * @returns {Promise<boolean>} 是否发送成功
   */
  async sendToClient(ip, message) {
    const client = this.clients.get(ip);
    if (!client || !client.ws || client.ws.readyState !== WebSocket.OPEN) {
      const status = !client ? '客户端不存在' :
                    !client.ws ? '客户端WebSocket不存在' :
                    `客户端WebSocket状态: ${client.ws.readyState}`;

      logger.warn(`客户端未连接或未就绪: ${ip}, 状态: ${status}`);

      // 抛出错误而不是返回false，以便更好地追踪问题
      throw createNetworkError(`客户端未连接或未就绪: ${ip}, 状态: ${status}`);
    }

    return new Promise((resolve, reject) => {
      try {
        const messageStr = JSON.stringify(message);
        client.ws.send(messageStr, (error) => {
          if (error) {
            const errorMessage = error.message || '未知错误';
            const errorStack = error.stack || '';
            logger.error(`发送消息失败: ${ip}, 错误: ${errorMessage}`, {
              error,
              stack: errorStack,
              message: messageStr
            });
            reject(createNetworkError(`发送消息失败: ${errorMessage}`));
          } else {
            resolve(true);
          }
        });
      } catch (error) {
        // 捕获JSON.stringify可能抛出的错误
        const errorMessage = error.message || '未知错误';
        const errorStack = error.stack || '';
        logger.error(`准备消息失败: ${ip}, 错误: ${errorMessage}`, {
          error,
          stack: errorStack,
          message: JSON.stringify(message)
        });
        reject(createNetworkError(`准备消息失败: ${errorMessage}`));
      }
    });
  }



  /**
   * 根据IP获取设备SN
   * @private
   * @param {string} ip - 客户端IP
   * @returns {string|null} 设备SN，如果找不到则返回null
   */
  _getSNFromIP(ip) {
    const deviceInfo = this.deviceList.get(ip);
    if (deviceInfo && deviceInfo.status && deviceInfo.status.sn) {
      return deviceInfo.status.sn;
    }
    return null;
  }

  /**
   * 关闭WebSocket服务
   */
  close() {
    this.isClosing = true;

    // 清除连接监控定时器
    if (this.connectionMonitorInterval) {
      clearInterval(this.connectionMonitorInterval);
      this.connectionMonitorInterval = null;
    }

    // 关闭所有客户端连接
    this.clients.forEach((client, ip) => {
      try {
        if (client.ws && client.ws.readyState === WebSocket.OPEN) {
          client.ws.close(1000, 'Server shutting down');
        }
      } catch (error) {
        logger.error(`关闭客户端连接失败: ${ip}`, error);
      }
    });

    // 关闭服务器
    if (this.server) {
      this.server.close((error) => {
        if (error) {
          logger.error('关闭WebSocket服务器失败', error);
        } else {
          logger.info('WebSocket服务器已关闭');
        }
      });
    }

    // 清理数据
    this.clients.clear();
    this.deviceList.clear();
    this.snMap.clear();
    this.lastActiveTime.clear();
    this.isClosing = false;

    logger.info('WebSocket服务已关闭');
  }

  /**
   * 发送命令并等待响应
   * @param {string} sn - 设备序列号
   * @param {Object} command - 命令对象
   * @param {number} [timeout=10000] - 超时时间（毫秒）
   * @returns {Promise<Object>} 命令响应
   */
  async sendCommandAndWaitResponse(sn, command, timeout = 10000) {
    return new Promise((resolve, reject) => {
      // 获取设备IP
      const ip = this.snMap.get(sn);
      if (!ip) {
        reject(new Error(`设备 ${sn} 未连接`));
        return;
      }

      // 创建响应处理器
      const responseHandler = (response) => {
        if (response.sn === sn) {
          clearTimeout(timeoutId);
          this.removeListener(WebSocketService.EVENTS.CONFIG_RESPONSE, responseHandler);
          resolve(response);
        }
      };

      // 设置超时处理
      const timeoutId = setTimeout(() => {
        this.removeListener(WebSocketService.EVENTS.CONFIG_RESPONSE, responseHandler);
        reject(new Error(`等待设备 ${sn} 响应超时`));
      }, timeout);

      // 监听响应事件
      this.once(WebSocketService.EVENTS.CONFIG_RESPONSE, responseHandler);

      // 发送命令
      this.sendToClient(ip, command).catch(error => {
        clearTimeout(timeoutId);
        this.removeListener(WebSocketService.EVENTS.CONFIG_RESPONSE, responseHandler);
        reject(error);
      });
    });
  }
}

module.exports = WebSocketService;
