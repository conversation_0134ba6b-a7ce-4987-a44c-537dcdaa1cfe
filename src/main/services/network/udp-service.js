/**
 * UDP服务类
 * 负责设备发现和广播
 */

const dgram = require('dgram');
const os = require('os');
const EventEmitter = require('events');
const logger = require('../../utils/logger');
const { handleError, createNetworkError } = require('../../utils/error-handler');
const { NETWORK } = require('../../../shared/constants/network');

/**
 * UDP服务类
 * @class
 * @extends EventEmitter
 */
class UDPService extends EventEmitter {
  /**
   * UDP事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      LISTENING: 'listening',
      MESSAGE: 'message',
      ERROR: 'error',
      BROADCAST_ERROR: 'broadcastError'
    };
  }

  /**
   * 构造函数
   */
  constructor() {
    super();
    this.servers = new Map(); // 网卡地址 -> { server, network }
    this.broadcastTimer = null;
    this.port = NETWORK.DEFAULT_UDP_PORT;
    this.broadcastPort = 9943;
    this.broadcastInterval = NETWORK.BROADCAST_INTERVAL;
    this.serverName = 'PFDMPlayControlServer';
    this.wsPort = NETWORK.DEFAULT_WS_PORT;
    this.isRunning = false;

    logger.debug('UDPService: 初始化完成');
  }

  /**
   * 获取广播地址列表
   * @private
   * @returns {Array<Object>} 网络接口信息数组
   */
  getBroadcastAddresses() {
    try {
      const interfaces = os.networkInterfaces();
      const networkInfo = [];

      for (const [name, addrs] of Object.entries(interfaces)) {
        for (const addr of addrs) {
          if (addr.family === 'IPv4' && !addr.internal) {
            const broadcastAddress = this._calculateBroadcastAddress(addr.address, addr.netmask);
            networkInfo.push({
              name,
              address: addr.address,
              broadcast: broadcastAddress,
              netmask: addr.netmask
            });
          }
        }
      }

      if (networkInfo.length === 0) {
        logger.warn('未找到有效的网络接口，使用默认广播地址');
        return [{ broadcast: '***************' }];
      }

      logger.debug(`找到 ${networkInfo.length} 个网络接口`);
      return networkInfo;
    } catch (error) {
      logger.error('获取网络接口信息失败', error);
      return [{ broadcast: '***************' }];
    }
  }

  /**
   * 计算广播地址
   * @private
   * @param {string} ip - IP地址
   * @param {string} netmask - 子网掩码
   * @returns {string} 广播地址
   */
  _calculateBroadcastAddress(ip, netmask) {
    const ipParts = ip.split('.').map(part => parseInt(part));
    const maskParts = netmask.split('.').map(part => parseInt(part));
    const broadcastParts = ipParts.map((part, i) => (part & maskParts[i]) | (~maskParts[i] & 255));
    return broadcastParts.join('.');
  }

  /**
   * 创建UDP服务器
   * @private
   * @param {Object} network - 网络接口信息
   * @returns {Promise<Object>} UDP服务器实例
   */
  _createServer(network) {
    return new Promise((resolve, reject) => {
      const server = dgram.createSocket({
        type: 'udp4',
        reuseAddr: true
      });

      server.on('error', (err) => {
        logger.error(`UDP错误 (${network.address || '0.0.0.0'})`, err);
        this.emit(UDPService.EVENTS.ERROR, { interface: network, error: err });
      });

      server.on('listening', () => {
        const address = server.address();
        logger.info(`UDP服务启动 (${network.address || '0.0.0.0'}): ${address.address}:${address.port}`);
        server.setBroadcast(true);
        this.emit(UDPService.EVENTS.LISTENING, { interface: network, address });
        resolve(server);
      });

      server.on('message', (msg, rinfo) => {
        try {
          const message = msg.toString();
          logger.debug(`收到UDP消息: ${rinfo.address}:${rinfo.port}`, message);
          this.emit(UDPService.EVENTS.MESSAGE, { message, rinfo });
        } catch (error) {
          logger.error('处理UDP消息失败', error);
        }
      });

      try {
        server.bind({
          port: this.port,
          address: network.address || '0.0.0.0'
        });
      } catch (error) {
        logger.error(`绑定UDP端口失败: ${this.port}`, error);
        reject(error);
      }
    });
  }

  /**
   * 初始化UDP服务
   * @param {number} [port=9944] - 监听端口
   * @param {Object} [config] - 配置对象
   * @returns {Promise<boolean>} 初始化结果
   */
  async init(port = 9944, config = {}) {
    try {
      if (this.isRunning) {
        await this.close();
      }

      this.port = port;
      this.serverName = config.serverName || this.serverName;
      this.wsPort = config.wsPort || this.wsPort;

      const networks = this.getBroadcastAddresses();

      for (const network of networks) {
        try {
          const server = await this._createServer(network);
          this.servers.set(network.address || '0.0.0.0', { server, network });
        } catch (error) {
          logger.error(`初始化UDP服务失败 (${network.address || '0.0.0.0'})`, error);
          this.emit(UDPService.EVENTS.ERROR, { interface: network, error });
        }
      }

      if (this.servers.size > 0) {
        this.isRunning = true;
        this.startBroadcast();
        logger.info(`UDP服务初始化完成，监听端口: ${port}`);
        return true;
      }

      logger.error('UDP服务初始化失败，没有可用的网络接口');
      return false;
    } catch (error) {
      handleError(error, 'UDPService.init');
      return false;
    }
  }

  /**
   * 开始广播服务信息
   */
  startBroadcast() {
    if (this.broadcastTimer) {
      clearInterval(this.broadcastTimer);
    }

    const broadcastData = Buffer.from(JSON.stringify({
      name: this.serverName,
      port: this.wsPort
    }));

    const broadcast = () => {
      for (const [_, { server, network }] of this.servers) {
        this._sendBroadcast(server, network, broadcastData)
          .catch(error => {
            logger.error(`广播失败 (${network.address || '0.0.0.0'})`, error);
            this.emit(UDPService.EVENTS.BROADCAST_ERROR, { network, error });
          });
      }
    };

    this.broadcastTimer = setInterval(broadcast, this.broadcastInterval);
    broadcast(); // 立即发送第一次广播

    logger.debug(`开始广播服务信息，间隔: ${this.broadcastInterval}ms`);
  }

  /**
   * 发送广播消息
   * @private
   * @param {Object} server - UDP服务器实例
   * @param {Object} network - 网络接口信息
   * @param {Buffer} data - 广播数据
   * @returns {Promise<void>}
   */
  _sendBroadcast(server, network, data) {
    return new Promise((resolve, reject) => {
      server.send(
        data,
        0,
        data.length,
        this.broadcastPort,
        network.broadcast,
        (error) => {
          if (error) {
            reject(error);
          } else {
            resolve();
          }
        }
      );
    });
  }

  /**
   * 发送自定义广播消息
   * @param {string|Object} message - 广播消息
   * @param {number} [port=9943] - 目标端口
   * @returns {Promise<void>}
   */
  async broadcast(message, port = 9943) {
    try {
      if (!this.isRunning || this.servers.size === 0) {
        throw createNetworkError('UDP服务未初始化');
      }

      const data = Buffer.from(typeof message === 'string' ? message : JSON.stringify(message));
      const promises = [];

      for (const [_, { server, network }] of this.servers) {
        promises.push(
          this._sendBroadcast(server, network, data)
            .catch(error => {
              logger.error(`发送广播消息失败 (${network.address || '0.0.0.0'})`, error);
              this.emit(UDPService.EVENTS.BROADCAST_ERROR, { network, error });
            })
        );
      }

      await Promise.all(promises);
      logger.debug('广播消息发送成功');
    } catch (error) {
      handleError(error, 'UDPService.broadcast');
      throw error;
    }
  }

  /**
   * 关闭UDP服务
   * @returns {Promise<void>}
   */
  async close() {
    try {
      this.isRunning = false;

      if (this.broadcastTimer) {
        clearInterval(this.broadcastTimer);
        this.broadcastTimer = null;
      }

      const closePromises = Array.from(this.servers.values()).map(({ server, network }) => {
        return new Promise(resolve => {
          try {
            server.close(() => {
              logger.debug(`UDP服务关闭 (${network.address || '0.0.0.0'})`);
              resolve();
            });
          } catch (error) {
            logger.error(`关闭UDP服务器失败 (${network.address || '0.0.0.0'})`, error);
            resolve();
          }
        });
      });

      await Promise.all(closePromises);
      this.servers.clear();

      logger.info('UDP服务已关闭');
    } catch (error) {
      logger.error('关闭UDP服务失败', error);
    }
  }
}

module.exports = UDPService;
