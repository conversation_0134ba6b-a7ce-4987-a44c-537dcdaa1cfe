/**
 * 方案服务类
 * 处理方案管理相关功能
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');
const { paths } = require('../../utils/paths');
const logger = require('../../utils/logger');
const { handleError, createFileError } = require('../../utils/error-handler');
const configService = require('../config');
const FileService = require('./file-service');
const { createSolution } = require('../../../shared/types/solution');
const { createResource } = require('../../../shared/types/resource');
const { DEFAULT_GROUPS } = require('../../../shared/constants/defaults');

/**
 * 方案服务类
 * @class
 * @extends EventEmitter
 */
class SolutionService extends EventEmitter {
  /**
   * 方案事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      SOLUTION_CREATED: 'solutionCreated',
      SOLUTION_DELETED: 'solutionDeleted',
      SOLUTION_UPDATED: 'solutionUpdated',
      GROUP_ADDED: 'groupAdded',
      GROUP_UPDATED: 'groupUpdated',
      GROUP_DELETED: 'groupDeleted'
    };
  }

  /**
   * 构造函数
   * @param {FileService} fileService - 文件服务实例
   */
  constructor(fileService) {
    super();
    this.fileService = fileService;
    logger.debug('SolutionService: 初始化完成');
  }

  /**
   * 初始化方案服务
   * @returns {Promise<void>}
   */
  async init() {
    try {
      logger.info('SolutionService: 初始化完成');
    } catch (error) {
      handleError(error, 'SolutionService.init');
      throw error;
    }
  }

  /**
   * 创建方案
   * @param {Array<Object>} files - 文件列表
   * @param {string} name - 方案名称
   * @param {string} description - 方案描述
   * @param {string} pcVersion - PC端版本号
   * @returns {Promise<Object>} 创建的方案对象
   * @throws {Error} 创建失败时抛出错误
   */
  async createSolution(files, name, description, pcVersion) {
    try {
      const solutionUUID = uuidv4();

      const fileList = await this._processFiles(files, description);
      const solution = this._createSolutionConfig(solutionUUID, fileList, name, description, pcVersion);

      // 确保资源配置目录存在
      await this._ensureDirectoryExists(path.dirname(paths.resources.config));

      // 获取现有的资源配置
      let existingConfig = null;
      try {
        const configData = await fs.readFile(paths.resources.config, 'utf8');
        existingConfig = JSON.parse(configData);
      } catch (err) {
        logger.debug(`读取现有资源配置失败，将创建新配置: ${err.message}`);
      }

      // 如果存在现有配置，将新方案添加到配置中
      if (existingConfig) {
        // 保持原有格式，不创建solutions数组
        Object.assign(existingConfig, solution);
        
        // 保存更新后的配置
        await fs.writeFile(
          paths.resources.config,
          JSON.stringify(existingConfig, null, 2)
        );
      } else {
        // 如果没有现有配置，直接使用solution作为配置
        await fs.writeFile(
          paths.resources.config,
          JSON.stringify(solution, null, 2)
        );
      }

      // 触发方案创建事件
      this.emit(SolutionService.EVENTS.SOLUTION_CREATED, solution);

      logger.info(`创建方案成功: ${name} (${solutionUUID})`);
      return solution;
    } catch (error) {
      handleError(error, 'SolutionService.createSolution');
      throw createFileError(`创建方案失败: ${error.message}`);
    }
  }

  /**
   * 获取所有方案列表
   * @returns {Promise<Array<Object>>} 方案列表
   */
  async getSolutions() {
    try {
      // 从配置文件中读取配置
      const configData = await fs.readFile(paths.resources.config, 'utf8');
      const config = JSON.parse(configData);

      // 直接返回配置对象，因为配置本身就是方案
      logger.debug(`获取方案列表成功`);
      return config;
    } catch (error) {
      handleError(error, 'SolutionService.getSolutions');
      return null;
    }
  }

  /**
   * 获取方案详情
   * @param {string} uuid - 方案UUID
   * @returns {Promise<Object|null>} 方案详情
   */
  async getSolution(uuid) {
    try {
      // 从配置文件中读取配置
      const configData = await fs.readFile(paths.resources.config, 'utf8');
      const config = JSON.parse(configData);

      // 检查UUID是否匹配
      if (config.UUID !== uuid) {
        logger.warn(`方案不存在: ${uuid}`);
        return null;
      }

      logger.debug(`获取方案详情成功: ${uuid}`);
      return config;
    } catch (error) {
      handleError(error, `SolutionService.getSolution(${uuid})`);
      return null;
    }
  }

  /**
   * 删除方案
   * @param {string} uuid - 方案UUID
   * @returns {Promise<boolean>} 是否成功删除
   */
  async deleteSolution(uuid) {
    try {
      // 从配置文件中读取配置
      const configData = await fs.readFile(paths.resources.config, 'utf8');
      const config = JSON.parse(configData);

      // 检查UUID是否匹配
      if (config.UUID !== uuid) {
        logger.warn(`方案不存在: ${uuid}`);
        return false;
      }

      // 删除配置文件
      await fs.unlink(paths.resources.config);

      // 触发方案删除事件
      this.emit(SolutionService.EVENTS.SOLUTION_DELETED, { uuid });

      logger.info(`删除方案成功: ${uuid}`);
      return true;
    } catch (error) {
      handleError(error, `SolutionService.deleteSolution(${uuid})`);
      return false;
    }
  }

  /**
   * 检查配置文件是否存在
   * @returns {Promise<boolean>} 配置文件是否存在
   */
  async hasConfigFile() {
    try {
      return await configService.hasConfig('resources');
    } catch (error) {
      handleError(error, 'SolutionService.hasConfigFile');
      return false;
    }
  }

  /**
   * 处理文件列表
   * @private
   * @param {Array<Object>} files - 文件列表
   * @param {string} description - 文件描述
   * @returns {Promise<Array<Object>>} 处理后的文件列表
   */
  async _processFiles(files, description) {
    try {
      return await Promise.all(
        files.map(async (file, index) => {
          const md5 = await this.fileService.calculateFileMD5(file.path);
          const fileType = this.fileService._determineFileType(file.name);

          return createResource({
            MD5: md5,
            describe: description || file.name,
            fileName: file.name,
            groups: ["默认"],
            index,
            path: file.path,
            poster: "",
            showName: file.name,
            type: fileType
          });
        })
      );
    } catch (error) {
      logger.error('处理文件列表失败', error);
      throw error;
    }
  }

  /**
   * 创建方案配置对象
   * @private
   * @param {string} uuid - 方案UUID
   * @param {Array<Object>} fileList - 文件列表
   * @param {string} name - 方案名称
   * @param {string} description - 方案描述
   * @param {string} pcVersion - PC端版本号
   * @returns {Object} 方案配置对象
   */
  _createSolutionConfig(uuid, fileList, name, description, pcVersion) {
    return createSolution({
      UUID: uuid,
      name: name || "播控方案",
      description: description || "自动创建的默认方案",
      groups: DEFAULT_GROUPS,
      list: fileList,
      logo: "",
      pcVersion: pcVersion || "1.0.0" // 使用传入的版本号，如果没有则使用默认值
    });
  }

  /**
   * 确保目录存在
   * @private
   * @param {string} dirPath - 目录路径
   * @returns {Promise<void>}
   */
  async _ensureDirectoryExists(dirPath) {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      logger.error(`创建目录失败: ${dirPath}`, error);
      throw error;
    }
  }

  /**
   * 添加分组
   * @param {string} solutionId - 方案ID
   * @param {string} groupName - 分组名称
   * @param {string} [description=''] - 分组描述
   * @returns {Promise<Object>} 更新后的方案对象
   */
  async addGroup(solutionId, groupName, description = '') {
    try {
      // 从配置文件中读取配置
      const configData = await fs.readFile(paths.resources.config, 'utf8');
      const config = JSON.parse(configData);

      // 检查UUID是否匹配
      if (config.UUID !== solutionId) {
        throw new Error(`方案不存在: ${solutionId}`);
      }

      // 检查分组名称是否已存在
      if (config.groups && config.groups.includes(groupName)) {
        throw new Error(`分组名称已存在: ${groupName}`);
      }

      // 保存原始资源列表
      const originalList = config.list || [];

      // 导入分组工具函数
      const { isSubcategory, getMainCategory } = require('../../../renderer/vue/utils/group-utils');

      // 添加分组
      if (!config.groups) {
        config.groups = [];
      }

      // 检查是否是子分类
      if (isSubcategory(groupName)) {
        // 获取主分类
        const mainCategory = getMainCategory(groupName);

        // 检查主分类是否已存在
        if (!config.groups.includes(mainCategory)) {
          // 如果主分类不存在，先添加主分类
          console.error(`添加子分类 ${groupName}，主分类 ${mainCategory} 不存在，自动创建`);
          config.groups.push(mainCategory);
        }
      }

      // 检查分组是否已存在（再次检查，以防在添加主分类后有变化）
      if (!config.groups.includes(groupName)) {
        // 添加分组
        config.groups.push(groupName);
      } else {
        console.error(`分组 ${groupName} 已存在，跳过添加`);
      }

      config.updatedAt = Date.now();

      // 确保保留原始资源列表
      config.list = originalList;

      // 不再自动将默认分组中的资源添加到新分组中
      // 用户可以手动将资源添加到新分组
      console.error(`创建新分组 ${groupName}，不自动添加资源`);

      // 打印当前资源列表信息
      if (config.list && Array.isArray(config.list) && config.list.length > 0) {
        console.error(`当前有 ${config.list.length} 个资源，不会自动添加到新分组 ${groupName} 中`);
      }

      // 保存更新后的配置
      await fs.writeFile(
        paths.resources.config,
        JSON.stringify(config, null, 2)
      );

      // 同时更新配置服务的缓存
      if (configService && typeof configService.saveConfig === 'function') {
        await configService.saveConfig(config, 'resources', true);
        logger.debug(`更新配置服务缓存成功: ${solutionId}`);
      }

      // 触发分组添加事件
      this.emit(SolutionService.EVENTS.GROUP_ADDED, { solutionId, groupName, description });

      logger.info(`添加分组成功: ${groupName} (${solutionId})`);
      return config;
    } catch (error) {
      handleError(error, `SolutionService.addGroup(${solutionId}, ${groupName})`);
      throw error;
    }
  }

  /**
   * 更新分组
   * @param {string} solutionId - 方案ID
   * @param {string} oldGroupName - 旧分组名称
   * @param {string} newGroupName - 新分组名称
   * @param {string} [description=''] - 分组描述
   * @returns {Promise<Object>} 更新后的方案对象
   */
  async updateGroup(solutionId, oldGroupName, newGroupName, description = '') {
    try {
      // 从配置文件中读取配置
      const configData = await fs.readFile(paths.resources.config, 'utf8');
      const config = JSON.parse(configData);

      // 检查UUID是否匹配
      if (config.UUID !== solutionId) {
        throw new Error(`方案不存在: ${solutionId}`);
      }

      // 导入分组工具函数
      const { isSubcategory, getMainCategory, getSubcategory, createFullGroupName } = require('../../../renderer/vue/utils/group-utils');

      // 检查新分组名称是否已存在（排除旧分组名称）
      if (oldGroupName !== newGroupName && config.groups && config.groups.includes(newGroupName)) {
        throw new Error(`分组名称已存在: ${newGroupName}`);
      }

      // 如果新名称是子分类，确保父分类存在
      if (isSubcategory(newGroupName)) {
        const mainCategory = getMainCategory(newGroupName);
        console.error(`更新为子分类 ${newGroupName}，父分类为 ${mainCategory}`);

        // 检查父分类是否存在
        if (!config.groups.includes(mainCategory)) {
          console.error(`父分类 ${mainCategory} 不存在，自动创建`);
          // 自动添加父分类
          config.groups.push(mainCategory);
        }
      }

      // 确保 groups 数组存在
      if (!config.groups) {
        config.groups = [];
      }

      // 更新分组名称
      const groupIndex = config.groups.indexOf(oldGroupName);
      if (groupIndex !== -1) {
        config.groups[groupIndex] = newGroupName;
      } else {
        // 如果分组不在列表中，添加新分组
        config.groups.push(newGroupName);
      }

      config.updatedAt = Date.now();

      // 更新资源中的分组引用
      if (config.list && Array.isArray(config.list)) {
        config.list.forEach(resource => {
          if (resource.groups && Array.isArray(resource.groups)) {
            // 更新主分类引用
            const index = resource.groups.indexOf(oldGroupName);
            if (index !== -1) {
              resource.groups[index] = newGroupName;
            }

            // 更新子分类引用
            resource.groups = resource.groups.map(group => {
              if (isSubcategory(group) && getMainCategory(group) === oldGroupName) {
                return createFullGroupName(newGroupName, getSubcategory(group));
              }
              return group;
            });
          }
        });
      }

      // 更新子分类的分组名称
      if (config.groups) {
        config.groups = config.groups.map(group => {
          if (isSubcategory(group) && getMainCategory(group) === oldGroupName) {
            return createFullGroupName(newGroupName, getSubcategory(group));
          }
          return group;
        });
      }

      // 保存更新后的配置
      await fs.writeFile(
        paths.resources.config,
        JSON.stringify(config, null, 2)
      );

      // 同时更新配置服务的缓存
      if (configService && typeof configService.saveConfig === 'function') {
        await configService.saveConfig(config, 'resources', true);
        logger.debug(`更新配置服务缓存成功: ${solutionId}`);
      }

      // 发送更新事件
      this.emit(SolutionService.EVENTS.GROUP_UPDATED, {
        solutionId,
        oldGroupName,
        newGroupName,
        description
      });

      return config;
    } catch (err) {
      console.error('更新分组失败:', err);
      throw err;
    }
  }

  /**
   * 删除分组
   * @param {string} solutionId - 方案ID
   * @param {string} groupName - 分组名称
   * @param {boolean} [deleteResources=false] - 是否同时删除资源
   * @returns {Promise<Object>} 更新后的方案对象
   */
  async deleteGroup(solutionId, groupName, deleteResources = false) {
    try {
      // 从配置文件中读取配置
      const configData = await fs.readFile(paths.resources.config, 'utf8');
      const config = JSON.parse(configData);

      // 检查UUID是否匹配
      if (config.UUID !== solutionId) {
        throw new Error(`方案不存在: ${solutionId}`);
      }

      // 导入分组工具函数
      const { isSubcategory, getMainCategory } = require('../../../renderer/vue/utils/group-utils');

      // 检查分组是否存在于方案的分组列表中
      if (!config.groups || !config.groups.includes(groupName)) {
        // 如果分组不在方案的分组列表中，但资源中有引用该分组的，则自动添加该分组
        let groupExistsInResources = false;

        if (config.list && Array.isArray(config.list)) {
          for (const resource of config.list) {
            if (resource.groups && Array.isArray(resource.groups) && resource.groups.includes(groupName)) {
              groupExistsInResources = true;
              console.error(`分组 ${groupName} 不在方案的分组列表中，但在资源 ${resource.showName || resource.fileName} 的分组中找到了`);
              break;
            }
          }
        }

        if (groupExistsInResources) {
          // 自动添加分组到方案的分组列表中
          console.error(`自动添加分组 ${groupName} 到方案的分组列表中`);
          if (!config.groups) {
            config.groups = [];
          }
          config.groups.push(groupName);
        } else {
          throw new Error(`分组不存在: ${groupName}`);
        }
      }

      // 确定是主分类还是子分类
      const isSubCat = isSubcategory(groupName);

      // 要删除的分组列表
      let groupsToDelete = [groupName];

      // 如果是主分类，同时删除其下的所有子分类
      if (!isSubCat) {
        if (config.groups) {
          config.groups.forEach(group => {
            if (isSubcategory(group) && getMainCategory(group) === groupName) {
              groupsToDelete.push(group);
            }
          });
        }
      }

      // 从分组列表中删除分组
      config.groups = config.groups.filter(group => !groupsToDelete.includes(group));

      // 如果删除后没有任何分组，添加默认分组
      if (config.groups.length === 0) {
        config.groups.push(DEFAULT_GROUPS[0]);
      }

      // 更新资源的分组引用
      if (config.list && Array.isArray(config.list)) {
        if (deleteResources) {
          // 删除属于这些分组的资源
          const resourcesToDelete = config.list.filter(resource => {
            if (!resource.groups || !Array.isArray(resource.groups)) {
              return false;
            }
            return resource.groups.some(group => groupsToDelete.includes(group));
          });

          // 删除资源文件
          for (const resource of resourcesToDelete) {
            const fullResourcePath = path.join(paths.resources.root, resource.path);
            try {
              await fs.rm(fullResourcePath, { recursive: true, force: true });
              logger.debug(`删除资源目录成功: ${fullResourcePath}`);
            } catch (err) {
              logger.error(`删除资源目录失败: ${fullResourcePath}`, err);
              // 继续处理其他资源，不中断流程
            }
          }

          // 从配置中移除这些资源
          config.list = config.list.filter(resource => {
            if (!resource.groups || !Array.isArray(resource.groups)) {
              return true;
            }
            return !resource.groups.some(group => groupsToDelete.includes(group));
          });
        } else {
          // 仅从资源的分组列表中移除被删除的分组
          config.list.forEach(resource => {
            if (resource.groups && Array.isArray(resource.groups)) {
              resource.groups = resource.groups.filter(group => !groupsToDelete.includes(group));
              // 如果资源没有分组了，添加到默认分组
              if (resource.groups.length === 0) {
                resource.groups.push(DEFAULT_GROUPS[0]);
              }
            }
          });
        }
      }

      config.updatedAt = Date.now();

      // 保存更新后的配置
      await fs.writeFile(
        paths.resources.config,
        JSON.stringify(config, null, 2)
      );

      // 同时更新配置服务的缓存
      if (configService && typeof configService.saveConfig === 'function') {
        await configService.saveConfig(config, 'resources', true);
        logger.debug(`更新配置服务缓存成功: ${solutionId}`);
      }

      // 发送删除事件
      this.emit(SolutionService.EVENTS.GROUP_DELETED, {
        solutionId,
        groupName,
        deleteResources
      });

      return config;
    } catch (err) {
      console.error('删除分组失败:', err);
      throw err;
    }
  }
}

module.exports = SolutionService;
