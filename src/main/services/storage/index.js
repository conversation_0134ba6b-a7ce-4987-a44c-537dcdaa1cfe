/**
 * 存储服务模块
 * 提供文件和方案服务的单例实例
 */

const FileService = require('./file-service');
const SolutionService = require('./solution-service');

// 创建单例实例
const fileService = new FileService();
const solutionService = new SolutionService(fileService);

/**
 * 初始化所有存储服务
 * @returns {Promise<void>}
 */
async function init() {
  await fileService.init();
  await solutionService.init();
}

// 导出服务实例和初始化函数
module.exports = {
  fileService,
  solutionService,
  init
};
