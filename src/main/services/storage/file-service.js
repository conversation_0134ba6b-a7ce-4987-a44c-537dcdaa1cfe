/**
 * 文件服务类
 * 处理基本文件操作
 */

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const crypto = require('crypto');
const EventEmitter = require('events');
const { paths } = require('../../utils/paths');
const { FILE_TYPES, FILE_EXTENSIONS } = require('../../utils/constants');
const logger = require('../../utils/logger');
const { handleError, createFileError } = require('../../utils/error-handler');

/**
 * 文件服务类
 * @class
 * @extends EventEmitter
 */
class FileService extends EventEmitter {
  /**
   * 文件事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      FILE_CREATED: 'fileCreated',
      FILE_DELETED: 'fileDeleted',
      FILE_COPIED: 'fileCopied',
      FILE_MOVED: 'fileMoved',
      FILE_ERROR: 'fileError'
    };
  }

  /**
   * 构造函数
   */
  constructor() {
    super();
    this.chunkSize = 2 * 1024 * 1024; // 2MB
    this.resourceDir = paths.resources.root;

    logger.debug('FileService: 初始化完成');
  }

  /**
   * 初始化文件服务
   * @returns {Promise<void>}
   */
  async init() {
    try {
      // 确保目录存在
      await this._ensureDirectoriesExist();
      logger.info('FileService: 初始化完成');
    } catch (error) {
      handleError(error, 'FileService.init');
      throw error;
    }
  }

  /**
   * 确保所有必要的目录存在
   * @private
   * @returns {Promise<void>}
   */
  async _ensureDirectoriesExist() {
    try {
      // 确保资源目录存在
      await this._ensureDirectoryExists(this.resourceDir);
    } catch (error) {
      logger.error('确保目录存在失败', error);
      throw error;
    }
  }

  /**
   * 创建文件读取流
   * @param {string} filePath - 文件路径
   * @param {Object} [options] - 读取选项
   * @returns {fs.ReadStream} 文件读取流
   */
  createReadStream(filePath, options = {}) {
    const defaultOptions = {
      highWaterMark: this.chunkSize
    };

    const mergedOptions = { ...defaultOptions, ...options };

    return fsSync.createReadStream(filePath, mergedOptions);
  }

  /**
   * 分块读取文件
   * @param {string} filePath - 文件路径
   * @param {number} [chunkSize] - 块大小，默认使用this.chunkSize
   * @yields {Object} 包含块数据和索引的对象
   */
  async* readFileInChunks(filePath, chunkSize) {
    const bufferSize = chunkSize || this.chunkSize;
    let fileHandle = null;

    try {
      fileHandle = await fs.open(filePath, 'r');
      const buffer = Buffer.alloc(bufferSize);
      let bytesRead;
      let index = 0;

      while ((bytesRead = (await fileHandle.read(buffer, 0, bufferSize, null)).bytesRead) > 0) {
        yield {
          chunk: buffer.slice(0, bytesRead),
          index: index++
        };
      }
    } catch (error) {
      handleError(error, 'FileService.readFileInChunks');
      throw createFileError(`读取文件块失败: ${error.message}`);
    } finally {
      if (fileHandle) {
        await fileHandle.close().catch(err => logger.error('关闭文件句柄失败', err));
      }
    }
  }

  /**
   * 复制文件
   * @param {string} sourcePath - 源文件路径
   * @param {string} targetPath - 目标文件路径
   * @returns {Promise<boolean>} 是否成功复制
   */
  async copyFile(sourcePath, targetPath) {
    try {
      // 确保目标目录存在
      await this._ensureDirectoryExists(path.dirname(targetPath));

      // 复制文件
      await fs.copyFile(sourcePath, targetPath);

      // 触发文件复制事件
      this.emit(FileService.EVENTS.FILE_COPIED, {
        sourcePath,
        targetPath
      });

      logger.debug(`文件复制成功: ${sourcePath} -> ${targetPath}`);
      return true;
    } catch (error) {
      handleError(error, 'FileService.copyFile');
      return false;
    }
  }

  /**
   * 移动文件
   * @param {string} sourcePath - 源文件路径
   * @param {string} targetPath - 目标文件路径
   * @returns {Promise<boolean>} 是否成功移动
   */
  async moveFile(sourcePath, targetPath) {
    try {
      // 确保目标目录存在
      await this._ensureDirectoryExists(path.dirname(targetPath));

      // 移动文件
      await fs.rename(sourcePath, targetPath);

      // 触发文件移动事件
      this.emit(FileService.EVENTS.FILE_MOVED, {
        sourcePath,
        targetPath
      });

      logger.debug(`文件移动成功: ${sourcePath} -> ${targetPath}`);
      return true;
    } catch (error) {
      handleError(error, 'FileService.moveFile');
      return false;
    }
  }

  /**
   * 删除文件
   * @param {string} filePath - 文件路径
   * @returns {Promise<boolean>} 是否成功删除
   */
  async deleteFile(filePath) {
    try {
      // 检查文件是否存在
      const exists = await this._exists(filePath);
      if (!exists) {
        logger.warn(`文件不存在，无法删除: ${filePath}`);
        return false;
      }

      // 删除文件
      await fs.unlink(filePath);

      // 触发文件删除事件
      this.emit(FileService.EVENTS.FILE_DELETED, { filePath });

      logger.debug(`文件删除成功: ${filePath}`);
      return true;
    } catch (error) {
      handleError(error, 'FileService.deleteFile');
      return false;
    }
  }

  /**
   * 删除目录
   * @param {string} dirPath - 目录路径
   * @returns {Promise<boolean>} 是否成功删除
   */
  async removeDirectory(dirPath) {
    try {
      // 检查目录是否存在
      const exists = await this._exists(dirPath);
      if (!exists) {
        logger.warn(`目录不存在，无法删除: ${dirPath}`);
        return false;
      }

      // 删除目录
      await this._removeDirectory(dirPath);

      logger.debug(`目录删除成功: ${dirPath}`);
      return true;
    } catch (error) {
      handleError(error, 'FileService.removeDirectory');
      return false;
    }
  }

  /**
   * 计算文件MD5
   * @param {string} filePath - 文件路径
   * @returns {Promise<string>} MD5哈希值
   */
  async calculateFileMD5(filePath) {
    try {
      return await this._calculateFileMD5(filePath);
    } catch (error) {
      handleError(error, 'FileService.calculateFileMD5');
      throw createFileError(`计算文件MD5失败: ${error.message}`);
    }
  }

  /**
   * 获取文件信息
   * @param {string} filePath - 文件路径
   * @returns {Promise<Object>} 文件信息
   */
  async getFileInfo(filePath) {
    try {
      const stats = await fs.stat(filePath);
      const ext = path.extname(filePath).toLowerCase();
      const fileName = path.basename(filePath);

      return {
        name: fileName,
        path: filePath,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        type: this._determineFileType(fileName),
        extension: ext
      };
    } catch (error) {
      handleError(error, 'FileService.getFileInfo');
      throw createFileError(`获取文件信息失败: ${error.message}`);
    }
  }

  /**
   * 根据文件名确定文件类型
   * @param {string} fileName - 文件名
   * @returns {number} 文件类型
   */
  _determineFileType(fileName) {
    const ext = path.extname(fileName).toLowerCase().substring(1); // 移除前导点

    // 检查文件扩展名是否匹配视频类型
    if (FILE_EXTENSIONS.VIDEO.includes(`.${ext}`)) {
      return FileType.VIDEO_2D_PLANE; // 默认为2D平面视频
    }

    // 检查文件扩展名是否匹配图片类型
    if (FILE_EXTENSIONS.IMAGE.includes(`.${ext}`)) {
      return FileType.IMAGE_2D_PLANE; // 默认为2D平面图片
    }

    // 检查文件扩展名是否匹配应用类型
    if (FILE_EXTENSIONS.APP.includes(`.${ext}`)) {
      return FileType.APP;
    }

    // 其他文件类型
    return FileType.OTHER;
  }

  /**
   * 计算文件MD5
   * @private
   * @param {string} filePath - 文件路径
   * @returns {Promise<string>} MD5哈希值
   */
  async _calculateFileMD5(filePath) {
    try {
      return new Promise((resolve, reject) => {
        const hash = crypto.createHash('md5');
        const stream = fsSync.createReadStream(filePath);

        stream.on('data', data => hash.update(data));
        stream.on('end', () => resolve(hash.digest('hex')));
        stream.on('error', error => reject(error));
      });
    } catch (error) {
      logger.error('计算文件MD5失败', error);
      throw error;
    }
  }

  /**
   * 确保目录存在
   * @private
   * @param {string} dirPath - 目录路径
   * @returns {Promise<void>}
   */
  async _ensureDirectoryExists(dirPath) {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      logger.error(`创建目录失败: ${dirPath}`, error);
      throw error;
    }
  }

  /**
   * 检查文件是否存在
   * @private
   * @param {string} filePath - 文件路径
   * @returns {Promise<boolean>} 文件是否存在
   */
  async _exists(filePath) {
    try {
      await fs.access(filePath, fs.constants.F_OK);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 删除目录
   * @private
   * @param {string} dirPath - 目录路径
   * @returns {Promise<void>}
   */
  async _removeDirectory(dirPath) {
    try {
      await fs.rm(dirPath, { recursive: true, force: true });
    } catch (error) {
      logger.error(`删除目录失败: ${dirPath}`, error);
      throw error;
    }
  }

  /**
   * 复制目录
   * @param {string} sourceDir - 源目录路径
   * @param {string} targetDir - 目标目录路径
   * @returns {Promise<boolean>} 是否成功复制
   */
  async copyDirectory(sourceDir, targetDir) {
    try {
      // 确保目标目录存在
      await this._ensureDirectoryExists(targetDir);

      // 读取源目录中的所有条目
      const entries = await fs.readdir(sourceDir, { withFileTypes: true });

      // 遍历所有条目
      for (const entry of entries) {
        const sourcePath = path.join(sourceDir, entry.name);
        const targetPath = path.join(targetDir, entry.name);

        if (entry.isDirectory()) {
          // 如果是目录，递归复制
          await this.copyDirectory(sourcePath, targetPath);
        } else {
          // 如果是文件，直接复制
          await this.copyFile(sourcePath, targetPath);
        }
      }

      logger.debug(`目录复制成功: ${sourceDir} -> ${targetDir}`);
      return true;
    } catch (error) {
      handleError(error, 'FileService.copyDirectory');
      logger.error(`复制目录失败: ${sourceDir} -> ${targetDir}`, error);
      return false;
    }
  }
}

module.exports = FileService;

