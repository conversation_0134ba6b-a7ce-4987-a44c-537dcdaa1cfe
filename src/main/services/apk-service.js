/**
 * APK 服务类
 * 负责解析 APK 文件并提取相关信息
 */

const fs = require('fs').promises;
const path = require('path');
const ApkReader = require('adbkit-apkreader');
const logger = require('../utils/logger');

/**
 * APK 服务类
 * 负责解析 APK 文件并提取相关信息
 */
class ApkService {
  /**
   * 解析 APK 文件
   * @param {string} apkPath - APK 文件路径
   * @returns {Promise<Object>} APK 信息，包括包名、版本等
   */
  async parseApk(apkPath) {
    try {
      logger.debug(`开始解析 APK 文件: ${apkPath}`);
      
      // 检查文件是否存在
      await fs.access(apkPath);
      
      // 使用 ApkReader 解析 APK 文件
      const reader = await ApkReader.open(apkPath);
      const manifest = await reader.readManifest();
      
      // 提取包名和版本信息
      const packageInfo = {
        packageName: manifest.package,
        versionCode: manifest.versionCode,
        versionName: manifest.versionName,
        minSdkVersion: manifest.usesSdk ? manifest.usesSdk.minSdkVersion : null,
        targetSdkVersion: manifest.usesSdk ? manifest.usesSdk.targetSdkVersion : null,
        applicationLabel: manifest.application.label
      };
      
      logger.debug(`APK 解析成功: ${packageInfo.packageName}, 版本: ${packageInfo.versionName}`);
      return packageInfo;
    } catch (error) {
      logger.error(`解析 APK 文件失败: ${apkPath}`, error);
      throw new Error(`解析 APK 文件失败: ${error.message}`);
    }
  }
}

module.exports = new ApkService();
