/**
 * RTSP代理服务
 * 将RTSP流转换为WebSocket流，以便在浏览器中播放
 */

const Stream = require('node-rtsp-stream');
const { EventEmitter } = require('events');
const logger = require('../../utils/logger');
const { getFfmpegPaths } = require('../../utils/ffmpeg-path');

/**
 * RTSP代理服务类
 * @class
 * @extends EventEmitter
 */
class RtspProxyService extends EventEmitter {
  /**
   * RTSP代理服务事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      STREAM_STARTED: 'streamStarted',
      STREAM_STOPPED: 'streamStopped',
      STREAM_ERROR: 'streamError'
    };
  }

  /**
   * 构造函数
   */
  constructor() {
    super();
    this.streams = new Map(); // sn -> Stream实例
    this.portCounter = 9000; // WebSocket端口起始值
    this.initialized = false;
    this.streamLocks = new Map(); // sn -> { timestamp, locked } 用于防止重复启动
    this.ffmpegProcesses = new Map(); // pid -> { sn, startTime, lastChecked }
    this.cleanupInterval = null; // 清理定时器
  }

  /**
   * 初始化服务
   * @returns {Promise<void>}
   */
  async init() {
    try {
      this.initialized = true;

      // 启动定时清理
      this.startCleanupTimer();

      logger.info('RTSP代理服务初始化完成');
    } catch (error) {
      logger.error('RTSP代理服务初始化失败', error);
      throw error;
    }
  }

  /**
   * 启动定时清理器
   * @private
   */
  startCleanupTimer() {
    // 每2分钟检查一次
    const CLEANUP_INTERVAL = 2 * 60 * 1000; // 2分钟

    // 如果已经有定时器，先清除
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(() => {
      this.cleanupUnusedProcesses();
    }, CLEANUP_INTERVAL);

    logger.info(`FFmpeg进程清理定时器已启动，间隔: ${CLEANUP_INTERVAL / 1000}秒`);
  }

  /**
   * 检查是否已初始化
   * @private
   * @throws {Error} 如果服务未初始化
   */
  _checkInitialized() {
    if (!this.initialized) {
      throw new Error('RTSP代理服务未初始化');
    }
  }

  /**
   * 获取可用的WebSocket端口
   * @private
   * @returns {Promise<number>} 可用的WebSocket端口
   */
  async _getAvailablePort() {
    // 使用更健壮的端口分配策略
    const net = require('net');

    // 检查端口是否可用
    const isPortAvailable = (port) => {
      return new Promise((resolve) => {
        const server = net.createServer();
        server.once('error', () => {
          // 端口被占用
          resolve(false);
        });

        server.once('listening', () => {
          // 端口可用，关闭服务器
          server.close();
          resolve(true);
        });

        server.listen(port);
      });
    };

    // 从当前计数器开始，查找可用端口
    let port = this.portCounter;
    const maxAttempts = 100; // 最多尝试100个端口

    for (let i = 0; i < maxAttempts; i++) {
      if (await isPortAvailable(port)) {
        // 找到可用端口，更新计数器
        this.portCounter = port + 1;
        logger.info(`找到可用的WebSocket端口: ${port}`);
        return port;
      }

      // 尝试下一个端口
      port++;
    }

    // 如果所有尝试都失败，重置计数器并抛出错误
    this.portCounter = 9000;
    throw new Error(`无法找到可用的WebSocket端口，尝试了${maxAttempts}个端口`);
  }

  /**
   * 启动RTSP流代理
   * @param {string} sn - 设备序列号
   * @param {string} rtspUrl - RTSP流URL
   * @returns {Promise<Object>} 流信息
   */
  async startStream(sn, rtspUrl) {
    try {
      this._checkInitialized();

      // 检查是否有锁，防止重复启动
      const now = Date.now();
      const lock = this.streamLocks.get(sn);
      if (lock && lock.locked && now - lock.timestamp < 5000) { // 5秒内的锁
        logger.warn(`设备 ${sn} 的流正在启动中，忽略重复请求`);

        // 如果已经有流在运行，直接返回流信息
        if (this.streams.has(sn)) {
          const { config } = this.streams.get(sn);
          return {
            sn,
            wsUrl: `ws://localhost:${config.wsPort}`,
            rtspUrl: config.streamUrl,
            wsPort: config.wsPort
          };
        }

        // 否则抛出错误
        throw new Error(`设备 ${sn} 的流正在启动中，请稍后再试`);
      }

      // 设置锁
      this.streamLocks.set(sn, { timestamp: now, locked: true });

      // 如果已经有该设备的流，先停止
      if (this.streams.has(sn)) {
        logger.info(`设备 ${sn} 已有流正在运行，先停止旧流`);
        await this.stopStream(sn);

        // 等待一小段时间，确保资源被完全释放
        await new Promise(resolve => setTimeout(resolve, 500));

        logger.info(`旧流已停止，准备启动新流: ${sn}`);
      }

      // 获取可用端口
      const wsPort = await this._getAvailablePort();

      // 获取 FFmpeg 路径
      const ffmpegPaths = getFfmpegPaths();

      // 创建流配置
      const streamConfig = {
        name: `stream_${sn}`,
        streamUrl: rtspUrl,  // 这是实际的RTSP URL，会被自动用作输入
        wsPort: wsPort,
        ffmpegPath: ffmpegPaths.ffmpeg, // 设置 FFmpeg 路径
        ffmpegOptions: {
          // 全局选项
          '-loglevel': 'warning',
          '-stats': '',

          // 输入选项
          '-rtsp_transport': 'tcp',  // 使用TCP传输RTSP流

          // 输出选项
          '-q': '5',  // 视频质量
          '-f': 'mpegts',  // 输出格式为MPEG-TS
          '-codec:v': 'mpeg1video',  // 视频编码为MPEG1
          '-b:v': '800k',  // 视频比特率
          '-r': '30',  // 帧率
          '-s': '640x480',  // 分辨率
          '-bf': '0',  // 禁用B帧
          '-an': ''  // 禁用音频
        }
      };

      logger.info(`启动RTSP流代理: ${sn}, URL: ${rtspUrl}, WebSocket端口: ${wsPort}`);

      // 创建流实例
      const stream = new Stream(streamConfig);

      // 记录FFmpeg进程ID
      let ffmpegPid = null;
      if (stream.mpeg1Muxer && stream.mpeg1Muxer.stream) {
        ffmpegPid = stream.mpeg1Muxer.stream.pid;
        logger.info(`FFmpeg进程已启动，PID: ${ffmpegPid}, SN: ${sn}`);

        // 记录FFmpeg进程信息到进程列表
        this.ffmpegProcesses.set(ffmpegPid, {
          sn,
          startTime: Date.now(),
          lastChecked: Date.now()
        });

        logger.info(`FFmpeg进程已记录到进程列表，PID: ${ffmpegPid}, SN: ${sn}`);
      }

      // 保存流实例
      this.streams.set(sn, {
        stream,
        config: streamConfig,
        startTime: Date.now(),
        ffmpegPid
      });

      // 构建WebSocket URL
      const wsUrl = `ws://localhost:${wsPort}`;

      // 触发流启动事件
      this.emit(RtspProxyService.EVENTS.STREAM_STARTED, {
        sn,
        wsUrl,
        rtspUrl,
        wsPort
      });

      return {
        sn,
        wsUrl,
        rtspUrl,
        wsPort
      };
    } catch (error) {
      logger.error(`启动RTSP流代理失败: ${sn}, URL: ${rtspUrl}`, error);
      this.emit(RtspProxyService.EVENTS.STREAM_ERROR, {
        sn,
        rtspUrl,
        error: error.message
      });
      throw error;
    } finally {
      // 释放锁
      if (this.streamLocks.has(sn)) {
        // 设置锁为未锁定状态，但保留时间戳，用于防止短时间内重复启动
        const lock = this.streamLocks.get(sn);
        this.streamLocks.set(sn, { ...lock, locked: false });
        logger.debug(`设备 ${sn} 的流启动锁已释放`);
      }
    }
  }

  /**
   * 停止RTSP流代理
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否成功停止
   */
  async stopStream(sn) {
    try {
      this._checkInitialized();

      // 检查是否有该设备的流
      if (!this.streams.has(sn)) {
        logger.warn(`没有找到设备的流: ${sn}`);
        return false;
      }

      // 获取流实例和FFmpeg进程ID
      const { stream, ffmpegPid } = this.streams.get(sn);

      // 停止流
      if (stream) {
        // 使用进程ID直接杀死FFmpeg进程
        let ffmpegTerminated = false;

        if (ffmpegPid) {
          try {
            logger.info(`正在终止FFmpeg进程，PID: ${ffmpegPid}, SN: ${sn}`);

            // 使用Node.js的child_process模块执行kill命令
            const { execSync } = require('child_process');

            // 在Windows上使用taskkill命令
            if (process.platform === 'win32') {
              try {
                // 使用更强的终止方式，确保进程被终止
                execSync(`taskkill /PID ${ffmpegPid} /F /T`, { timeout: 5000 });
                logger.info(`FFmpeg进程已终止，PID: ${ffmpegPid}, SN: ${sn}`);
                ffmpegTerminated = true;

                // 额外检查进程是否还存在
                try {
                  // 使用 wmic 检查进程是否存在
                  const output = execSync(`wmic process where "ProcessId=${ffmpegPid}" get ProcessId /format:list`, { timeout: 3000 }).toString();
                  if (output.includes(`ProcessId=${ffmpegPid}`)) {
                    logger.warn(`FFmpeg进程仍然存在，尝试再次终止: ${ffmpegPid}`);
                    // 再次尝试终止
                    execSync(`taskkill /PID ${ffmpegPid} /F /T`, { timeout: 5000 });
                  } else {
                    ffmpegTerminated = true;
                  }
                } catch (checkError) {
                  // 如果检查失败，可能是因为进程已经不存在
                  ffmpegTerminated = true;
                }
              } catch (killError) {
                logger.warn(`使用taskkill终止进程失败，尝试其他方法: ${killError.message}`);

                // 尝试使用 PowerShell 终止进程
                try {
                  execSync(`powershell -Command "Stop-Process -Id ${ffmpegPid} -Force"`, { timeout: 5000 });
                  logger.info(`使用PowerShell终止FFmpeg进程成功，PID: ${ffmpegPid}`);
                  ffmpegTerminated = true;
                } catch (psError) {
                  logger.warn(`使用PowerShell终止进程失败: ${psError.message}`);
                }
              }
            }
            // 在Linux/Mac上使用kill命令
            else {
              try {
                execSync(`kill -9 ${ffmpegPid}`, { timeout: 3000 });
                logger.info(`FFmpeg进程已终止，PID: ${ffmpegPid}, SN: ${sn}`);
                ffmpegTerminated = true;

                // 检查进程是否还存在
                try {
                  const output = execSync(`ps -p ${ffmpegPid} -o pid=`, { timeout: 3000 }).toString();
                  if (output.trim()) {
                    logger.warn(`FFmpeg进程仍然存在，尝试再次终止: ${ffmpegPid}`);
                    execSync(`kill -9 ${ffmpegPid}`, { timeout: 3000 });
                  } else {
                    ffmpegTerminated = true;
                  }
                } catch (checkError) {
                  // 如果检查失败，可能是因为进程已经不存在
                  ffmpegTerminated = true;
                }
              } catch (killError) {
                logger.warn(`使用kill终止进程失败: ${killError.message}`);
              }
            }
          } catch (error) {
            logger.error(`终止FFmpeg进程失败，PID: ${ffmpegPid}, SN: ${sn}`, error);
          }
        }

        // 作为备份，也尝试通过Node.js API终止进程
        if (!ffmpegTerminated && stream.mpeg1Muxer && stream.mpeg1Muxer.ffmpegProcess) {
          try {
            logger.info(`尝试通过Node.js API终止FFmpeg进程: ${sn}`);
            // 先尝试正常终止
            stream.mpeg1Muxer.ffmpegProcess.kill();

            // 等待一小段时间，然后检查进程是否还存在
            await new Promise(resolve => setTimeout(resolve, 200));

            // 如果进程还存在，使用强制终止
            if (!stream.mpeg1Muxer.ffmpegProcess.killed) {
              stream.mpeg1Muxer.ffmpegProcess.kill('SIGKILL');
            }

            stream.mpeg1Muxer.ffmpegProcess = null;
            ffmpegTerminated = true;
            logger.info(`通过Node.js API终止FFmpeg进程成功: ${sn}`);
          } catch (killError) {
            logger.warn(`通过Node.js API终止进程失败: ${killError.message}`);
          }
        }

        // 如果所有方法都失败，记录警告
        if (!ffmpegTerminated) {
          logger.warn(`无法确认FFmpeg进程是否已终止，PID: ${ffmpegPid}, SN: ${sn}`);
        }

        // 从进程列表中移除
        if (this.ffmpegProcesses.has(ffmpegPid)) {
          this.ffmpegProcesses.delete(ffmpegPid);
          logger.info(`FFmpeg进程已从进程列表中移除，PID: ${ffmpegPid}, SN: ${sn}`);
        }

        // 使用自定义方法关闭流，而不是调用原始的stop方法
        // 这样可以避免重复关闭WebSocket服务器
        try {
          logger.info(`开始自定义关闭流程: ${sn}`);

          // 1. 先移除camdata事件监听器，防止在WebSocket服务器关闭后仍然触发broadcast
          try {
            logger.info(`1. 移除camdata事件监听器: ${sn}`);

            // 移除所有camdata事件监听器
            stream.removeAllListeners('camdata');

            // 为了更安全，添加一个空的camdata事件监听器，替代原来的监听器
            stream.on('camdata', () => {
              logger.debug(`忽略camdata事件，流正在关闭: ${sn}`);
            });

            // 如果存在mpeg1Muxer，也移除其事件监听器
            if (stream.mpeg1Muxer) {
              try {
                stream.mpeg1Muxer.removeAllListeners('mpeg1data');
              } catch (mpeg1Error) {
                logger.warn(`移除mpeg1data事件监听器失败: ${sn}`, mpeg1Error);
              }
            }
          } catch (removeError) {
            logger.warn(`移除camdata事件监听器失败: ${sn}`, removeError);
          }

          // 2. 安全地关闭WebSocket服务器
          if (stream.wsServer) {
            try {
              logger.info(`2. 正在关闭WebSocket服务器: ${sn}`);

              // 在关闭WebSocket服务器之前，先关闭所有客户端连接
              try {
                if (stream.wsServer.clients) {
                  stream.wsServer.clients.forEach(client => {
                    try {
                      if (client.readyState === 1) { // OPEN
                        client.close(1000, 'Server shutting down');
                      }
                    } catch (clientError) {
                      logger.warn(`关闭WebSocket客户端连接失败: ${sn}`, clientError);
                    }
                  });
                }
              } catch (clientsError) {
                logger.warn(`处理WebSocket客户端集合失败: ${sn}`, clientsError);
              }

              // 完全替换broadcast方法，不再访问clients属性
              if (stream.wsServer) {
                // 创建一个空的broadcast方法，不执行任何操作
                stream.wsServer.broadcast = function(/* data, opts */) {
                  logger.debug(`WebSocket服务器正在关闭，跳过广播: ${sn}`);
                  return [];
                };

                // 保存WebSocket服务器的引用，然后从流对象中分离出来
                const wsServer = stream.wsServer;

                // 清空引用，防止后续访问
                stream.wsServer = null;

                // 关闭WebSocket服务器
                logger.info(`关闭分离后的WebSocket服务器: ${sn}`);
                wsServer.close();
              }
              logger.info(`WebSocket服务器已关闭: ${sn}`);

              // 等待一小段时间，确保WebSocket服务器有时间完全关闭
              await new Promise(resolve => setTimeout(resolve, 100));
            } catch (wsError) {
              logger.error(`关闭WebSocket服务器失败: ${sn}`, wsError.stack || wsError.message || wsError);
            }
          }

          // 3. 不调用原始的stop方法，因为它会尝试再次关闭WebSocket服务器
          // 而是直接设置inputStreamStarted为false
          try {
            logger.info(`3. 设置inputStreamStarted为false: ${sn}`);
            stream.inputStreamStarted = false;
          } catch (flagError) {
            logger.warn(`设置inputStreamStarted失败: ${sn}`, flagError.stack || flagError.message || flagError);
          }

          logger.info(`自定义关闭流程完成: ${sn}`);
        } catch (error) {
          logger.error(`自定义关闭流程失败: ${sn}`, error.stack || error.message || error);
        }
      }

      // 从映射中移除
      this.streams.delete(sn);

      logger.info(`停止RTSP流代理: ${sn}`);

      // 触发流停止事件
      this.emit(RtspProxyService.EVENTS.STREAM_STOPPED, { sn });

      return true;
    } catch (error) {
      logger.error(`停止RTSP流代理失败: ${sn}`, error);
      return false;
    }
  }

  /**
   * 获取流信息
   * @param {string} sn - 设备序列号
   * @returns {Object|null} 流信息
   */
  getStreamInfo(sn) {
    try {
      this._checkInitialized();

      // 检查是否有该设备的流
      if (!this.streams.has(sn)) {
        return null;
      }

      // 获取流信息
      const { config, startTime } = this.streams.get(sn);

      return {
        sn,
        wsUrl: `ws://localhost:${config.wsPort}`,
        rtspUrl: config.streamUrl,
        wsPort: config.wsPort,
        startTime,
        duration: Date.now() - startTime
      };
    } catch (error) {
      logger.error(`获取流信息失败: ${sn}`, error);
      return null;
    }
  }

  /**
   * 获取所有流信息
   * @returns {Array<Object>} 所有流信息
   */
  getAllStreams() {
    try {
      this._checkInitialized();

      const streamInfoList = [];

      // 遍历所有流
      this.streams.forEach((streamData, sn) => {
        const { config, startTime } = streamData;

        streamInfoList.push({
          sn,
          wsUrl: `ws://localhost:${config.wsPort}`,
          rtspUrl: config.streamUrl,
          wsPort: config.wsPort,
          startTime,
          duration: Date.now() - startTime
        });
      });

      return streamInfoList;
    } catch (error) {
      logger.error('获取所有流信息失败', error);
      return [];
    }
  }

  /**
   * 清理未使用的FFmpeg进程
   * @private
   */
  async cleanupUnusedProcesses() {
    try {
      logger.info('开始清理未使用的FFmpeg进程');

      const now = Date.now();
      const maxAge = 15 * 60 * 1000; // 15分钟
      let cleanedCount = 0;

      // 检查每个记录的FFmpeg进程
      for (const [pid, processInfo] of this.ffmpegProcesses.entries()) {
        try {
          // 检查进程是否运行时间过长
          const processAge = now - processInfo.startTime;

          // 检查进程是否仍然需要（设备是否仍在投屏）
          const isStillNeeded = this.streams.has(processInfo.sn);

          // 检查进程是否仍然存在
          const isProcessRunning = await this.isProcessRunning(pid);

          // 如果进程不再需要但仍在运行，或者运行时间过长，则终止它
          if ((!isStillNeeded && isProcessRunning) || (processAge > maxAge && isProcessRunning)) {
            logger.warn(`发现未使用的FFmpeg进程，PID: ${pid}, SN: ${processInfo.sn}, 年龄: ${processAge / 1000}秒`);
            await this.terminateProcess(pid, processInfo.sn);
            cleanedCount++;
          }

          // 如果进程不存在但仍在列表中，从列表中移除
          if (!isProcessRunning) {
            logger.info(`FFmpeg进程已不存在，从列表中移除，PID: ${pid}`);
            this.ffmpegProcesses.delete(pid);
          }
        } catch (error) {
          logger.error(`检查FFmpeg进程时出错，PID: ${pid}`, error);
        }
      }

      logger.info(`FFmpeg进程清理完成，清理了 ${cleanedCount} 个进程，当前进程数: ${this.ffmpegProcesses.size}`);
    } catch (error) {
      logger.error('清理未使用的FFmpeg进程失败', error);
    }
  }

  /**
   * 检查进程是否正在运行
   * @private
   * @param {number} pid - 进程ID
   * @returns {Promise<boolean>} 进程是否正在运行
   */
  async isProcessRunning(pid) {
    try {
      const { execSync } = require('child_process');

      if (process.platform === 'win32') {
        // Windows: 使用tasklist命令检查进程
        const output = execSync(`tasklist /FI "PID eq ${pid}" /NH`, { timeout: 3000 }).toString();
        return output.includes(pid.toString());
      } else {
        // Linux/Mac: 使用ps命令检查进程
        const output = execSync(`ps -p ${pid} -o pid=`, { timeout: 3000 }).toString();
        return output.trim() !== '';
      }
    } catch (error) {
      // 如果命令执行失败，可能是因为进程不存在
      return false;
    }
  }

  /**
   * 终止进程
   * @private
   * @param {number} pid - 进程ID
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否成功终止
   */
  async terminateProcess(pid, sn) {
    try {
      logger.info(`正在终止未使用的FFmpeg进程，PID: ${pid}, SN: ${sn}`);

      const { execSync } = require('child_process');

      // 在Windows上使用taskkill命令
      if (process.platform === 'win32') {
        execSync(`taskkill /PID ${pid} /F /T`, { timeout: 5000 });
      }
      // 在Linux/Mac上使用kill命令
      else {
        execSync(`kill -9 ${pid}`, { timeout: 3000 });
      }

      // 从进程列表中移除
      this.ffmpegProcesses.delete(pid);

      logger.info(`未使用的FFmpeg进程已终止，PID: ${pid}, SN: ${sn}`);
      return true;
    } catch (error) {
      logger.error(`终止未使用的FFmpeg进程失败，PID: ${pid}, SN: ${sn}`, error);
      return false;
    }
  }

  /**
   * 关闭服务
   * @returns {Promise<void>}
   */
  async close() {
    try {
      if (!this.initialized) {
        return;
      }

      // 停止清理定时器
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
        this.cleanupInterval = null;
        logger.info('FFmpeg进程清理定时器已停止');
      }

      // 停止所有流
      const snList = Array.from(this.streams.keys());
      for (const sn of snList) {
        await this.stopStream(sn);
      }

      // 清理所有残留的FFmpeg进程
      try {
        // 复制进程列表，因为在清理过程中会修改原列表
        const processList = Array.from(this.ffmpegProcesses.entries());

        if (processList.length > 0) {
          logger.info(`关闭服务时清理残留的 ${processList.length} 个FFmpeg进程`);

          for (const [pid, processInfo] of processList) {
            try {
              if (await this.isProcessRunning(pid)) {
                await this.terminateProcess(pid, processInfo.sn);
              } else {
                this.ffmpegProcesses.delete(pid);
              }
            } catch (processError) {
              logger.error(`清理FFmpeg进程失败，PID: ${pid}`, processError);
            }
          }
        }
      } catch (cleanupError) {
        logger.error('清理残留FFmpeg进程失败', cleanupError);
      }

      this.initialized = false;
      logger.info('RTSP代理服务已关闭');
    } catch (error) {
      logger.error('关闭RTSP代理服务失败', error);
    }
  }
}

// 创建单例
const rtspProxyService = new RtspProxyService();

// 导出单例
module.exports = rtspProxyService;








