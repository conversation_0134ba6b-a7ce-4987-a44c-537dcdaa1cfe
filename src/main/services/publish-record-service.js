/**
 * 发布记录服务
 * 用于管理设备发布记录
 */
const fs = require('fs').promises;
const logger = require('../utils/logger');
const { fileService } = require('./storage');

// 发布记录文件路径
let recordsFilePath = '';

// 发布记录列表
let records = [];

// 记录ID计数器
let recordIdCounter = 1;

/**
 * 初始化发布记录服务
 */
const init = async () => {
  try {
    // 导入路径配置
    const { paths } = require('../utils/paths');

    // 使用paths.js中定义的发布记录文件路径
    recordsFilePath = paths.publishRecords.records;

    // 确保根目录存在
    await fileService._ensureDirectoryExists(paths.root);

    // 加载记录
    await loadRecords();

    logger.info('发布记录服务初始化成功');
  } catch (error) {
    logger.error('初始化发布记录服务失败:', error);
    throw error;
  }
};

/**
 * 加载发布记录
 */
const loadRecords = async () => {
  try {
    // 检查文件是否存在
    try {
      await fs.access(recordsFilePath);
    } catch (error) {
      // 文件不存在，创建空记录文件
      await fs.writeFile(recordsFilePath, JSON.stringify([], null, 2));
      records = [];
      return;
    }

    // 读取记录文件
    const data = await fs.readFile(recordsFilePath, 'utf8');
    records = JSON.parse(data);

    // 更新ID计数器
    if (records.length > 0) {
      // 找出最大ID
      const maxId = Math.max(...records.map(record => record.id));
      recordIdCounter = maxId + 1;
    }

    logger.debug(`已加载 ${records.length} 条发布记录`);
  } catch (error) {
    logger.error('加载发布记录失败:', error);
    records = [];
  }
};

/**
 * 保存发布记录
 */
const saveRecords = async () => {
  try {
    await fs.writeFile(recordsFilePath, JSON.stringify(records, null, 2));
    logger.debug(`已保存 ${records.length} 条发布记录`);
  } catch (error) {
    logger.error('保存发布记录失败:', error);
    throw error;
  }
};

/**
 * 创建发布记录
 * @param {Object} recordData 记录数据
 * @returns {Object} 创建的记录
 */
const createRecord = async (recordData) => {
  try {
    // 创建记录对象
    const record = {
      id: recordIdCounter++,
      createdAt: new Date().toISOString(),
      status: 'active',
      ...recordData
    };

    // 添加到记录列表
    records.unshift(record); // 添加到列表开头，使最新的记录显示在最前面

    // 保存记录
    await saveRecords();

    logger.info('创建发布记录成功:', record);
    return record;
  } catch (error) {
    logger.error('创建发布记录失败:', error);
    throw error;
  }
};

/**
 * 获取所有发布记录
 * @returns {Array} 发布记录列表
 */
const getRecords = () => {
  return [...records]; // 返回记录副本
};

/**
 * 获取指定ID的发布记录
 * @param {number} id 记录ID
 * @returns {Object|null} 发布记录或null
 */
const getRecordById = (id) => {
  const record = records.find(r => r.id === id);
  return record ? { ...record } : null; // 返回记录副本或null
};

/**
 * 更新发布记录
 * @param {number} id 记录ID
 * @param {Object} updateData 更新数据
 * @returns {Object|null} 更新后的记录或null
 */
const updateRecord = async (id, updateData) => {
  try {
    // 查找记录
    const index = records.findIndex(r => r.id === id);
    if (index === -1) {
      logger.warn(`未找到ID为 ${id} 的发布记录`);
      return null;
    }

    // 更新记录
    records[index] = {
      ...records[index],
      ...updateData,
      updatedAt: new Date().toISOString()
    };

    // 保存记录
    await saveRecords();

    logger.info(`更新ID为 ${id} 的发布记录成功`);
    return { ...records[index] }; // 返回记录副本
  } catch (error) {
    logger.error(`更新ID为 ${id} 的发布记录失败:`, error);
    throw error;
  }
};

/**
 * 停止发布记录
 * @param {number} id 记录ID
 * @returns {Object|null} 更新后的记录或null
 */
const stopRecord = async (id) => {
  return await updateRecord(id, {
    status: 'stopped',
    stoppedAt: new Date().toISOString()
  });
};

/**
 * 激活发布记录（再次发布）
 * @param {number} id 记录ID
 * @returns {Object|null} 更新后的记录或null
 */
const activateRecord = async (id) => {
  return await updateRecord(id, {
    status: 'active',
    stoppedAt: null
  });
};

/**
 * 删除发布记录
 * @param {number} id 记录ID
 * @returns {boolean} 是否删除成功
 */
const deleteRecord = async (id) => {
  try {
    // 查找记录
    const index = records.findIndex(r => r.id === id);
    if (index === -1) {
      logger.warn(`未找到ID为 ${id} 的发布记录`);
      return false;
    }

    // 删除记录
    records.splice(index, 1);

    // 保存记录
    await saveRecords();

    logger.info(`删除ID为 ${id} 的发布记录成功`);
    return true;
  } catch (error) {
    logger.error(`删除ID为 ${id} 的发布记录失败:`, error);
    throw error;
  }
};

/**
 * 清空所有发布记录
 * @returns {boolean} 是否清空成功
 */
const clearRecords = async () => {
  try {
    records = [];
    await saveRecords();
    logger.info('清空所有发布记录成功');
    return true;
  } catch (error) {
    logger.error('清空所有发布记录失败:', error);
    throw error;
  }
};

// 导出服务
module.exports = {
  init,
  createRecord,
  getRecords,
  getRecordById,
  updateRecord,
  stopRecord,
  activateRecord,
  deleteRecord,
  clearRecords
};
