/**
 * 配置服务类
 * 负责管理应用程序配置文件
 */

const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const EventEmitter = require('events');
const { paths } = require('../../utils/paths');
const logger = require('../../utils/logger');
const { CONFIG_TYPES } = require('../../utils/constants');
const { handleError, createConfigError } = require('../../utils/error-handler');
const { getVersionSync } = require('../ipc/app-handlers');

/**
 * 配置服务类
 * @class
 * @extends EventEmitter
 */
class ConfigService extends EventEmitter {
  /**
   * 配置事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      CONFIG_CHANGED: 'configChanged',
      CONFIG_LOADED: 'configLoaded',
      CONFIG_SAVED: 'configSaved',
      CONFIG_DELETED: 'configDeleted',
      CONFIG_ERROR: 'configError'
    };
  }

  /**
   * 构造函数
   */
  constructor() {
    super();

    // 配置文件路径映射
    this.configPaths = {
      resources: paths.resources.config,
      settings: paths.settings.root,
      customData: paths.customData.config,
      devices: {
        history: paths.devices.history,
        blocklist: paths.devices.blocklist
      }
    };

    // 配置缓存 - 存储格式: { data: Object, timestamp: number, version: number }
    this.configCache = new Map();

    // 缓存配置
    this.cacheConfig = {
      // 缓存有效期（毫秒），默认1分钟
      ttl: 60 * 1000,
      // 是否启用缓存
      enabled: true
    };

    // 默认配置
    this.defaultConfigs = {
      resources: {
        UUID: uuidv4(),
        groups: ["默认"],
        list: [],
        name: "播控方案",
        description: "自动创建的默认方案",
        pcVersion: getVersionSync(), // 使用应用程序的实际版本
        port: 50208
      },
      settings: {
        controlledMode: false,
        basic: {
          serverName: '',
          udpPort: 9944,
          wsPort: 50208,
          enableResourceSubcategories: false,
          enableResourcePreview: false,
          resourcesPath: '' // 默认为空，表示使用默认路径
        },
        advanced: {
          // 移除心跳间隔配置，因为现在使用固定值
          // heartbeatInterval: 5000
        }
      },
      customData: {
        appLogo: ''
      },
      devices: {
        history: [],
        blocklist: []
      }
    };

    logger.debug('ConfigService: 初始化完成');
  }



  /**
   * 初始化配置服务
   * @returns {Promise<void>}
   */
  async init() {
    try {
      // 确保配置目录存在
      await this._ensureDirectoriesExist();
      logger.info('ConfigService: 初始化完成');
    } catch (error) {
      handleError(error, 'ConfigService.init');
      throw error;
    }
  }

  /**
   * 确保所有配置目录存在
   * @private
   * @returns {Promise<void>}
   */
  async _ensureDirectoriesExist() {
    try {
      // 确保资源配置目录存在
      await this._ensureDirectoryExists(path.dirname(this.configPaths.resources));

      // 确保设置目录存在
      await this._ensureDirectoryExists(path.dirname(this.configPaths.settings));

      // 确保自定义数据目录存在
      await this._ensureDirectoryExists(path.dirname(this.configPaths.customData));

      // 确保设备历史记录目录存在
      await this._ensureDirectoryExists(path.dirname(this.configPaths.devices.history));

      // 确保设备黑名单目录存在
      await this._ensureDirectoryExists(path.dirname(this.configPaths.devices.blocklist));
    } catch (error) {
      logger.error('确保配置目录存在失败', error);
      throw error;
    }
  }

  /**
   * 读取配置文件
   * @param {string} configType - 配置类型 ('resources', 'settings', 'customData', 'devices.history', 'devices.blocklist')
   * @param {boolean} [useCache=true] - 是否使用缓存
   * @param {boolean} [returnDefault=false] - 如果配置不存在，是否返回默认配置
   * @returns {Promise<Object|null>} 配置对象或null
   */
  async readConfig(configType, useCache = true, returnDefault = false) {
    try {
      // 如果使用缓存且缓存已启用
      if (useCache && this.cacheConfig.enabled) {
        const cachedItem = this.configCache.get(configType);

        // 如果缓存中存在该配置
        if (cachedItem) {
          // 检查缓存是否过期
          const now = Date.now();
          const isExpired = now - cachedItem.timestamp > this.cacheConfig.ttl;

          // 如果缓存未过期，直接返回
          if (!isExpired) {
            logger.debug(`使用缓存的配置: ${configType}`);
            return JSON.parse(JSON.stringify(cachedItem.data));
          }

          logger.debug(`缓存已过期，重新读取配置: ${configType}`);
        }
      }

      // 处理嵌套配置路径，如 'devices.history'
      const configPath = this._getConfigPath(configType);

      if (!configPath) {
        const error = createConfigError(`未知的配置类型: ${configType}`);
        this.emit(ConfigService.EVENTS.CONFIG_ERROR, { type: configType, error });
        throw error;
      }

      // 确保目录存在
      await this._ensureDirectoryExists(path.dirname(configPath));

      // 检查文件是否存在
      let configExists = true;
      try {
        await fs.access(configPath, fs.constants.F_OK);
      } catch (error) {
        configExists = false;
      }

      // 如果文件不存在
      if (!configExists) {
        // 如果需要返回默认配置
        if (returnDefault) {
          const defaultConfig = this.getDefaultConfig(configType);
          logger.info(`配置文件不存在，返回默认配置: ${configType}`);

          // 更新缓存
          if (this.cacheConfig.enabled) {
            this._updateCache(configType, defaultConfig);
          }

          return defaultConfig;
        }

        // 否则返回null
        logger.info(`配置文件不存在，返回null: ${configType}`);
        return null;
      }

      // 读取配置文件
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      // 更新缓存
      if (this.cacheConfig.enabled) {
        this._updateCache(configType, config);
      }

      // 触发配置加载事件
      this.emit(ConfigService.EVENTS.CONFIG_LOADED, { type: configType, config });

      return config;
    } catch (error) {
      handleError(error, `ConfigService.readConfig(${configType})`);
      this.emit(ConfigService.EVENTS.CONFIG_ERROR, { type: configType, error });

      // 如果解析失败且需要返回默认配置
      if (returnDefault) {
        return this.getDefaultConfig(configType);
      }

      // 否则返回null
      return null;
    }
  }

  /**
   * 更新配置缓存
   * @private
   * @param {string} configType - 配置类型
   * @param {Object} data - 配置数据
   */
  _updateCache(configType, data) {
    const cachedItem = this.configCache.get(configType);
    const version = cachedItem ? cachedItem.version + 1 : 1;

    this.configCache.set(configType, {
      data: JSON.parse(JSON.stringify(data)),
      timestamp: Date.now(),
      version
    });

    logger.debug(`更新配置缓存: ${configType}, 版本: ${version}`);
  }

  /**
   * 保存配置文件
   * @param {Object} config - 配置对象
   * @param {string} configType - 配置类型 ('resources', 'settings', 'customData', 'devices.history', 'devices.blocklist')
   * @param {boolean} [updateCache=true] - 是否更新缓存
   * @returns {Promise<boolean>} 是否保存成功
   */
  async saveConfig(config, configType, updateCache = true) {
    try {
      const configPath = this._getConfigPath(configType);

      if (!configPath) {
        const error = createConfigError(`未知的配置类型: ${configType}`);
        this.emit(ConfigService.EVENTS.CONFIG_ERROR, { type: configType, error });
        throw error;
      }

      // 确保目录存在
      await this._ensureDirectoryExists(path.dirname(configPath));

      // 保存配置文件
      await fs.writeFile(configPath, JSON.stringify(config, null, 2), 'utf8');

      // 更新缓存
      if (updateCache && this.cacheConfig.enabled) {
        this._updateCache(configType, config);
      }

      // 触发配置保存事件
      this.emit(ConfigService.EVENTS.CONFIG_SAVED, { type: configType, config });

      // 触发配置变更事件
      this.emit(ConfigService.EVENTS.CONFIG_CHANGED, {
        type: configType,
        config,
        timestamp: Date.now(),
        source: 'save'
      });

      logger.debug(`配置文件保存成功: ${configType}`);
      return true;
    } catch (error) {
      handleError(error, `ConfigService.saveConfig(${configType})`);
      this.emit(ConfigService.EVENTS.CONFIG_ERROR, { type: configType, error });
      return false;
    }
  }

  /**
   * 获取默认配置
   * @param {string} configType - 配置类型 ('resources', 'settings', 'customData', 'devices.history', 'devices.blocklist')
   * @returns {Object} 默认配置对象
   */
  getDefaultConfig(configType) {
    // 处理嵌套配置，如 'devices.history'
    const parts = configType.split('.');
    let defaultConfig = this.defaultConfigs;

    for (const part of parts) {
      if (defaultConfig && defaultConfig[part]) {
        defaultConfig = defaultConfig[part];
      } else {
        logger.warn(`未找到默认配置: ${configType}`);
        return {};
      }
    }

    // 返回深拷贝，避免修改默认配置
    return JSON.parse(JSON.stringify(defaultConfig));
  }

  /**
   * 检查配置文件是否存在
   * @param {string} configType - 配置类型 ('resources', 'settings', 'customData', 'devices.history', 'devices.blocklist')
   * @returns {Promise<boolean>} 配置文件是否存在
   */
  async hasConfig(configType) {
    try {
      // 如果缓存中存在该配置，直接返回true
      if (this.configCache.has(configType)) {
        return true;
      }

      const configPath = this._getConfigPath(configType);

      if (!configPath) {
        return false;
      }

      await fs.access(configPath, fs.constants.F_OK);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 删除配置文件
   * @param {string} configType - 配置类型 ('resources', 'settings', 'customData', 'devices.history', 'devices.blocklist')
   * @returns {Promise<boolean>} 是否成功删除
   */
  async deleteConfig(configType) {
    try {
      const configPath = this._getConfigPath(configType);

      if (!configPath) {
        const error = createConfigError(`未知的配置类型: ${configType}`);
        this.emit(ConfigService.EVENTS.CONFIG_ERROR, { type: configType, error });
        return false;
      }

      // 检查文件是否存在
      try {
        await fs.access(configPath, fs.constants.F_OK);
      } catch (error) {
        logger.info(`配置文件不存在，无需删除: ${configType}`);
        return true;
      }

      // 删除文件
      await fs.unlink(configPath);

      // 从缓存中移除
      this.configCache.delete(configType);

      // 触发配置删除事件
      this.emit(ConfigService.EVENTS.CONFIG_DELETED, { type: configType });

      // 触发配置变更事件
      this.emit(ConfigService.EVENTS.CONFIG_CHANGED, {
        type: configType,
        config: null,
        timestamp: Date.now(),
        source: 'delete'
      });

      logger.info(`配置文件删除成功: ${configType}`);
      return true;
    } catch (error) {
      handleError(error, `ConfigService.deleteConfig(${configType})`);
      this.emit(ConfigService.EVENTS.CONFIG_ERROR, { type: configType, error });
      return false;
    }
  }

  /**
   * 清除配置缓存
   * @param {string} [configType] - 配置类型，如果不提供则清除所有缓存
   * @param {boolean} [reload=false] - 是否重新加载配置
   * @returns {Promise<void>}
   */
  async clearCache(configType, reload = false) {
    if (configType) {
      this.configCache.delete(configType);
      logger.debug(`清除配置缓存: ${configType}`);

      // 如果需要重新加载
      if (reload) {
        try {
          await this.readConfig(configType, false, true);
          logger.debug(`重新加载配置: ${configType}`);
        } catch (error) {
          logger.error(`重新加载配置失败: ${configType}`, error);
        }
      }
    } else {
      this.configCache.clear();
      logger.debug('清除所有配置缓存');

      // 如果需要重新加载所有配置
      if (reload) {
        try {
          // 重新加载常用配置
          await Promise.all([
            this.readConfig('settings', false, true),
            this.readConfig('resources', false, true),
            this.readConfig('devices.history', false, true),
            this.readConfig('devices.blocklist', false, true)
          ]);
          logger.debug('重新加载所有配置完成');
        } catch (error) {
          logger.error('重新加载所有配置失败', error);
        }
      }
    }
  }

  /**
   * 设置缓存配置
   * @param {Object} config - 缓存配置
   * @param {number} [config.ttl] - 缓存有效期（毫秒）
   * @param {boolean} [config.enabled] - 是否启用缓存
   */
  setCacheConfig(config) {
    if (config.ttl !== undefined) {
      this.cacheConfig.ttl = config.ttl;
    }

    if (config.enabled !== undefined) {
      this.cacheConfig.enabled = config.enabled;
    }

    logger.debug(`更新缓存配置: TTL=${this.cacheConfig.ttl}ms, 启用=${this.cacheConfig.enabled}`);
  }

  /**
   * 验证配置是否有效
   * @param {Object} config - 配置对象
   * @param {string} configType - 配置类型
   * @returns {boolean} 配置是否有效
   */
  validateConfig(config, configType) {
    // 根据不同的配置类型进行验证
    switch (configType) {
      case 'resources':
        return this._validateResourcesConfig(config);
      case 'settings':
        return this._validateSettingsConfig(config);
      case 'customData':
        return this._validateCustomDataConfig(config);
      case 'devices.history':
      case 'devices.blocklist':
        return Array.isArray(config);
      default:
        return true; // 默认不进行验证
    }
  }

  /**
   * 验证资源配置
   * @private
   * @param {Object} config - 资源配置对象
   * @returns {boolean} 配置是否有效
   */
  _validateResourcesConfig(config) {
    return (
      config &&
      typeof config === 'object' &&
      typeof config.UUID === 'string' &&
      Array.isArray(config.groups) &&
      Array.isArray(config.list)
    );
  }

  /**
   * 验证设置配置
   * @private
   * @param {Object} config - 设置配置对象
   * @returns {boolean} 配置是否有效
   */
  _validateSettingsConfig(config) {
    return (
      config &&
      typeof config === 'object' &&
      typeof config.controlledMode === 'boolean' &&
      config.basic &&
      typeof config.basic === 'object'
    );
  }

  /**
   * 验证自定义数据配置
   * @private
   * @param {Object} config - 自定义数据配置对象
   * @returns {boolean} 配置是否有效
   */
  _validateCustomDataConfig(config) {
    return (
      config &&
      typeof config === 'object'
    );
  }

  /**
   * 获取配置文件路径
   * @private
   * @param {string} configType - 配置类型
   * @returns {string|null} 配置文件路径
   */
  _getConfigPath(configType) {
    const parts = configType.split('.');
    let configPath = this.configPaths;

    for (const part of parts) {
      if (configPath && configPath[part]) {
        configPath = configPath[part];
      } else {
        return null;
      }
    }

    return configPath;
  }

  /**
   * 确保目录存在
   * @private
   * @param {string} dirPath - 目录路径
   * @returns {Promise<void>}
   */
  async _ensureDirectoryExists(dirPath) {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      logger.error(`创建目录失败: ${dirPath}`, error);
      throw error;
    }
  }

  /**
   * 监听配置变更
   * @param {string} configType - 配置类型
   * @param {Function} callback - 回调函数，接收变更事件
   * @returns {Function} 取消监听的函数
   */
  watchConfig(configType, callback) {
    if (!configType || typeof callback !== 'function') {
      throw new Error('无效的参数');
    }

    // 创建监听器
    const listener = (event) => {
      if (event.type === configType) {
        callback(event);
      }
    };

    // 添加监听器
    this.on(ConfigService.EVENTS.CONFIG_CHANGED, listener);

    // 返回取消监听的函数
    return () => {
      this.off(ConfigService.EVENTS.CONFIG_CHANGED, listener);
    };
  }

  /**
   * 获取配置缓存信息
   * @param {string} [configType] - 配置类型，如果不提供则返回所有缓存信息
   * @returns {Object|null} 缓存信息
   */
  getCacheInfo(configType) {
    if (configType) {
      const cachedItem = this.configCache.get(configType);
      if (!cachedItem) return null;

      return {
        type: configType,
        timestamp: cachedItem.timestamp,
        version: cachedItem.version,
        age: Date.now() - cachedItem.timestamp,
        isExpired: (Date.now() - cachedItem.timestamp) > this.cacheConfig.ttl
      };
    } else {
      const result = {};
      for (const [type, item] of this.configCache.entries()) {
        result[type] = {
          timestamp: item.timestamp,
          version: item.version,
          age: Date.now() - item.timestamp,
          isExpired: (Date.now() - item.timestamp) > this.cacheConfig.ttl
        };
      }
      return result;
    }
  }
}

module.exports = ConfigService;
