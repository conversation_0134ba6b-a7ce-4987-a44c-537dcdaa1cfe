/**
 * 网络控制器类
 * 负责管理网络服务，包括UDP和WebSocket
 * 只处理网络通信，不处理设备业务逻辑
 */

const BaseController = require('./base-controller');
const { wsService, udpService } = require('../services/network');
const configService = require('../services/config');
const logger = require('../utils/logger');
const { createNetworkError } = require('../utils/error-handler');
const { NETWORK } = require('../../shared/constants/network');

/**
 * 网络控制器类
 * @class
 * @extends BaseController
 */
class NetworkController extends BaseController {
  /**
   * 网络事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      // 网络错误事件
      NETWORK_ERROR: 'networkError',
      // 投屏响应事件
      SCREEN_CAST_RESPONSE: 'screenCastResponse',
      // 停止投屏响应事件
      SCREEN_CAST_STOP_RESPONSE: 'screenCastStopResponse'
    };
  }

  /**
   * 构造函数
   */
  constructor() {
    super('NetworkController');
    this.wsPort = NETWORK.DEFAULT_WS_PORT;
    this.udpPort = NETWORK.DEFAULT_UDP_PORT;
    this.serverName = 'PFDMPlayControlServer';
    this.broadcastInterval = NETWORK.BROADCAST_INTERVAL;
  }

  /**
   * 初始化控制器
   * @protected
   * @returns {Promise<void>}
   */
  async _initializeController() {
    try {
      // 读取网络设置
      const settings = await this._loadNetworkSettings();

      // 设置网络参数
      this.wsPort = settings.wsPort || this.wsPort;
      this.udpPort = settings.udpPort || this.udpPort;
      this.serverName = settings.serverName || this.serverName;

      // 注册WebSocket服务错误事件
      wsService.on(wsService.constructor.EVENTS.ERROR, this._handleWebSocketError.bind(this));

      // 注册投屏响应事件
      wsService.on(wsService.constructor.EVENTS.SCREEN_CAST_RESPONSE, this._handleScreenCastResponse.bind(this));

      // 注册停止投屏响应事件
      wsService.on(wsService.constructor.EVENTS.SCREEN_CAST_STOP_RESPONSE, this._handleScreenCastStopResponse.bind(this));

      // 注册UDP服务事件
      udpService.on(udpService.constructor.EVENTS.ERROR, this._handleUDPError.bind(this));
      udpService.on(udpService.constructor.EVENTS.BROADCAST_ERROR, this._handleUDPBroadcastError.bind(this));

      // 初始化网络服务
      await this._initNetworkServices();

      logger.info('NetworkController: 初始化完成');
    } catch (error) {
      this.handleError('initialization', error);
      throw error;
    }
  }

  /**
   * 加载网络设置
   * @private
   * @returns {Promise<Object>} 网络设置
   */
  async _loadNetworkSettings() {
    try {
      const settings = await configService.readConfig('settings', true, true);
      return {
        serverName: settings.basic?.serverName || this.serverName,
        udpPort: parseInt(settings.basic?.udpPort, 10) || this.udpPort,
        wsPort: parseInt(settings.basic?.wsPort, 10) || this.wsPort
      };
    } catch (error) {
      logger.error('加载网络设置失败，使用默认设置', error);
      return {
        serverName: this.serverName,
        udpPort: this.udpPort,
        wsPort: this.wsPort
      };
    }
  }

  /**
   * 初始化网络服务
   * @private
   * @returns {Promise<void>}
   */
  async _initNetworkServices() {
    try {
      // 初始化WebSocket服务
      await wsService.init(this.wsPort);

      // 初始化UDP服务
      const udpConfig = {
        serverName: this.serverName,
        wsPort: this.wsPort,
        broadcastInterval: this.broadcastInterval
      };
      await udpService.init(this.udpPort, udpConfig);

      logger.info(`网络服务初始化完成，WebSocket端口: ${this.wsPort}, UDP端口: ${this.udpPort}`);
    } catch (error) {
      logger.error('初始化网络服务失败', error);
      throw error;
    }
  }

  /**
   * 处理WebSocket错误
   * @private
   * @param {Object} data - 错误数据
   */
  _handleWebSocketError(data) {
    logger.error('WebSocket错误', data);
    this.emit(NetworkController.EVENTS.NETWORK_ERROR, {
      type: 'websocket',
      ...data
    });
  }

  /**
   * 处理UDP错误
   * @private
   * @param {Object} data - 错误数据
   */
  _handleUDPError(data) {
    logger.error('UDP错误', data);
    this.emit(NetworkController.EVENTS.NETWORK_ERROR, {
      type: 'udp',
      ...data
    });
  }

  /**
   * 处理UDP广播错误
   * @private
   * @param {Object} data - 错误数据
   */
  _handleUDPBroadcastError(data) {
    logger.error('UDP广播错误', data);
    this.emit(NetworkController.EVENTS.NETWORK_ERROR, {
      type: 'udp_broadcast',
      ...data
    });
  }

  /**
   * 处理投屏响应
   * @private
   * @param {Object} data - 响应数据
   */
  _handleScreenCastResponse(data) {
    logger.info('收到投屏响应', data);
    this.emit(NetworkController.EVENTS.SCREEN_CAST_RESPONSE, data);
  }

  /**
   * 处理停止投屏响应
   * @private
   * @param {Object} data - 响应数据
   */
  _handleScreenCastStopResponse(data) {
    logger.info('收到停止投屏响应', data);
    this.emit(NetworkController.EVENTS.SCREEN_CAST_STOP_RESPONSE, data);
  }

  /**
   * 发送消息到指定IP的客户端
   * @param {string} ip - 客户端IP
   * @param {Object} message - 消息对象
   * @returns {Promise<boolean>} 是否成功
   */
  async sendToClient(ip, message) {
    try {
      this._checkInitialized();

      if (!ip) {
        throw createNetworkError('无效的IP地址');
      }

      return await wsService.sendToClient(ip, message);
    } catch (error) {
      this.handleError('sendToClient', error);
      throw error;
    }
  }

  /**
   * 广播消息到所有客户端
   * @param {Object} message - 消息对象
   */
  broadcastMessage(message) {
    try {
      this._checkInitialized();
      wsService.broadcastMessage(message);
    } catch (error) {
      this.handleError('broadcastMessage', error);
      throw error;
    }
  }

  /**
   * 关闭网络服务
   * @returns {Promise<void>}
   */
  async closeNetworkServices() {
    try {
      this._checkInitialized();

      // 关闭WebSocket服务
      wsService.close();

      // 关闭UDP服务
      await udpService.close();

      logger.info('网络服务已关闭');
    } catch (error) {
      this.handleError('closeNetworkServices', error);
      throw error;
    }
  }
}

// 创建单例
const networkController = new NetworkController();

// 导出单例
module.exports = networkController;
