const path = require('path');
const fs = require('fs').promises; // 添加 fs 模块的导入
const { v4: uuidv4 } = require('uuid');
const { paths } = require('../utils/paths');
const {
  FileType,
  FileExtensions,
  DirectoryPrefixes,
  createResource,
  isImageType,
  isVideoType,
  isAppType
} = require('../../shared/types/resource.js'); // 导入统一的文件类型枚举和扩展名映射
const BaseController = require('./base-controller');
const configService = require('../services/config');
const { fileService } = require('../services/storage');
const ThumbnailService = require('../utils/thumbnail-service.js');
const apkService = require('../services/apk-service'); // 导入 APK 服务
const logger = require('../utils/logger');
const { _checkDiskSpace } = require('../utils/disk-space'); // 导入磁盘空间检查函数

/**
 * 资源控制器类
 * 负责处理资源的创建、读取、更新和删除操作
 */
class ResourceController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super('ResourceController');
    this.resourceDir = paths.resources.root;
    this.configPath = paths.resources.config;
    this.thumbnailService = new ThumbnailService(fileService);
  }

  /**
   * 初始化控制器
   * @protected
   * @returns {Promise<void>}
   */
  async _initializeController() {
    // 资源目录由fileService负责创建
    // 注册文件服务事件
    fileService.on(fileService.constructor.EVENTS.FILE_DELETED, this._handleFileDeleted.bind(this));
    fileService.on(fileService.constructor.EVENTS.FILE_CREATED, this._handleFileCreated.bind(this));

    logger.info('ResourceController: 初始化完成');
  }

  /**
   * 处理文件删除事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleFileDeleted(data) {
    logger.debug('ResourceController: 文件删除事件', data);
    // 可以在这里添加更多处理逻辑
  }

  /**
   * 处理文件创建事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleFileCreated(data) {
    logger.debug('ResourceController: 文件创建事件', data);
    // 可以在这里添加更多处理逻辑
  }

  /**
   * 计算文件的MD5哈希值
   * @param {string} filePath - 文件路径
   * @returns {Promise<string>} MD5哈希值
   */
  async calculateMD5(filePath) {
    try {
      return await fileService.calculateFileMD5(filePath);
    } catch (error) {
      this.handleError('calculateMD5', error);
      throw error;
    }
  }

  /**
   * 读取配置文件
   * @param {boolean} [useCache=true] - 是否使用缓存
   * @param {boolean} [returnDefault=false] - 如果配置不存在，是否返回默认配置
   * @returns {Promise<Object|null>} 配置对象或null（表示没有方案）
   */
  async readConfig(useCache = true, returnDefault = false) {
    try {
      return await configService.readConfig('resources', useCache, returnDefault);
    } catch (error) {
      logger.error('读取配置文件失败', error);
      return null;
    }
  }

  /**
   * 保存配置文件
   * @param {Object} config - 配置对象
   * @param {boolean} [updateCache=true] - 是否更新缓存
   * @returns {Promise<boolean>} 是否保存成功
   */
  async saveConfig(config, updateCache = true) {
    try {
      // 验证配置是否有效
      if (!config || typeof config !== 'object') {
        logger.error('配置格式无效，无法保存');
        return false;
      }

      // 保存配置
      return await configService.saveConfig(config, 'resources', updateCache);
    } catch (error) {
      logger.error('保存配置文件失败', error);
      return false;
    }
  }

  /**
   * 检查配置文件是否存在
   * @returns {Promise<boolean>} 配置文件是否存在
   */
  async hasConfigFile() {
    try {
      return await fileService.hasConfigFile();
    } catch (error) {
      this.handleError('hasConfigFile', error);
      return false;
    }
  }

  /**
   * 确定文件类型
   * @param {string} fileName - 文件名
   * @param {number} [specifiedType] - 指定的文件类型（如果有）
   * @returns {{type: number, dirPrefix: string}} 文件类型信息
   */
  determineFileType(fileName, specifiedType) {
    // 如果指定了具体类型，优先使用指定的类型
    if (specifiedType !== undefined && specifiedType !== null) {
      // 根据指定的类型确定目录前缀
      let dirPrefix = 'other';

      // 使用 resource.js 中定义的类型判断函数
      if (isImageType(specifiedType)) {
        dirPrefix = 'image';
      }
      // 视频类型
      else if (isVideoType(specifiedType)) {
        dirPrefix = 'video';
      }
      // 应用类型
      else if (specifiedType === FileType.APP) {
        dirPrefix = 'app';
      }

      logger.debug(`使用指定的文件类型: ${specifiedType}, 目录前缀: ${dirPrefix}`);
      return {
        type: specifiedType,
        dirPrefix: dirPrefix
      };
    }

    // 如果没有指定类型，根据扩展名判断
    const ext = path.extname(fileName).toLowerCase().substring(1); // 移除前导点

    // 检查文件扩展名是否匹配视频类型
    if (FileExtensions.VIDEO.includes(ext)) {
      return {
        type: FileType.VIDEO_2D_PLANE, // 默认为2D平面视频
        dirPrefix: 'video'
      };
    }

    // 检查文件扩展名是否匹配图片类型
    if (FileExtensions.IMAGE.includes(ext)) {
      return {
        type: FileType.IMAGE_2D_PLANE, // 默认为2D平面图片
        dirPrefix: 'image'
      };
    }

    // 检查文件扩展名是否匹配应用类型
    if (FileExtensions.APP.includes(ext)) {
      return {
        type: FileType.APP,
        dirPrefix: 'app'
      };
    }

    // 其他文件类型
    return {
      type: FileType.OTHER,
      dirPrefix: 'other'
    };
  }

  /**
   * 创建资源
   * @param {Array<Object>} files - 文件列表
   * @param {Object} formData - 表单数据
   * @returns {Promise<Object>} 创建结果
   */
  async createResource(files, formData) {
    try {
      this._checkInitialized();

      let config = await this.readConfig();

      // 如果配置不存在，创建新的配置
      if (!config) {
        config = {
          UUID: uuidv4(),
          groups: ["默认"],
          list: [],  // 直接初始化为空数组
          name: "播控方案",
          description: "自动创建的默认方案"
        };
      }

      // 获取目标分组
      const groupName = formData.group || "默认";

      // 如果分组不存在，添加新分组
      if (!config.groups.includes(groupName)) {
        config.groups.push(groupName);
      }

      // 获取当前列表中最大的索引
      const maxIndex = config.list.length > 0
        ? Math.max(...config.list.map(item => item.index || 0))
        : -1;

      // 获取已存在的目录列表
      const existingDirs = await fs.readdir(this.resourceDir);
      const existingDirSet = new Set(existingDirs);

      // 计算所有文件的总大小
      let totalSize = 0;
      for (const file of files) {
        try {
          const stats = await fs.stat(file.path);
          totalSize += stats.size;
          logger.info('计算文件大小', {
            filePath: file.path,
            size: `${Math.floor(stats.size / (1024 * 1024))}MB`
          });
        } catch (error) {
          logger.warn(`获取文件大小失败: ${file.path}`, error);
        }
      }

      logger.info('计算完成所有文件总大小', {
        totalSize: `${Math.floor(totalSize / (1024 * 1024))}MB`,
        fileCount: files.length
      });

      // 检查资源存储目录所在磁盘的空间
      const hasEnoughSpace = await _checkDiskSpace(this.resourceDir, totalSize);
      if (!hasEnoughSpace) {
        const requiredSpaceMB = Math.floor(totalSize / (1024 * 1024));
        throw new Error(`资源存储目录所在磁盘空间不足，需要至少 ${requiredSpaceMB}MB 的可用空间`);
      }

      const newFiles = await Promise.all(files.map(async (file, index) => {
        const fileName = path.basename(file.path);

        // 使用指定的类型（如果有）
        let fileType;
        let dirPrefix;

        if (formData.type !== undefined) {
          // 将类型转换为整数
          fileType = parseInt(formData.type);

          // 根据具体类型确定目录前缀
          if (isImageType(fileType)) {
            dirPrefix = 'image';
          } else if (isVideoType(fileType)) {
            dirPrefix = 'video';
          } else if (fileType === FileType.APP) {
            dirPrefix = 'app';
          } else {
            dirPrefix = 'other';
          }

          logger.debug(`使用指定的具体文件类型: ${fileType}, 目录前缀: ${dirPrefix}`);
        } else {
          // 如果没有指定类型，根据扩展名判断
          const ext = path.extname(fileName).toLowerCase().substring(1); // 移除前导点

          if (FileExtensions.VIDEO.includes(ext)) {
            fileType = FileType.VIDEO_2D_PLANE; // 默认为2D平面视频
            dirPrefix = 'video';
          } else if (FileExtensions.IMAGE.includes(ext)) {
            fileType = FileType.IMAGE_2D_PLANE; // 默认为2D平面图片
            dirPrefix = 'image';
          } else if (FileExtensions.APP.includes(ext)) {
            fileType = FileType.APP;
            dirPrefix = 'app';
          } else {
            fileType = FileType.OTHER;
            dirPrefix = 'other';
          }

          logger.debug(`根据扩展名确定文件类型: ${fileType}, 目录前缀: ${dirPrefix}`);
        }

        // 生成唯一的目录名
        let subDir;
        let dirIndex = maxIndex + 1 + index;
        do {
          subDir = `${dirPrefix}${dirIndex}`;
          dirIndex++;
        } while (existingDirSet.has(subDir) || await fs.access(path.join(this.resourceDir, subDir)).then(() => true).catch(() => false));

        // 将新目录名添加到已存在目录集合中
        existingDirSet.add(subDir);

        const subDirPath = path.join(this.resourceDir, subDir);
        await fileService._ensureDirectoryExists(subDirPath);

        // 获取文件扩展名
        const fileExt = path.extname(fileName);

        // 使用 path + 后缀名 作为文件名
        const newFileName = `${subDir.replace('/', '')}${fileExt}`;
        const targetPath = path.join(subDirPath, newFileName);
        const fileCopied = await fileService.copyFile(file.path, targetPath);

        if (!fileCopied) {
          throw new Error(`复制文件失败: ${file.path} -> ${targetPath}`);
        }

        // 特殊处理 m3u8 文件 - 复制相关的 ts 文件夹
        if (fileExt.toLowerCase() === '.m3u8') {
          try {
            // 获取 m3u8 文件所在的目录
            const sourceDir = path.dirname(file.path);
            logger.info(`处理 m3u8 文件: ${file.path}`);
            logger.info(`m3u8 文件所在目录: ${sourceDir}`);

            // 查找同目录下的 ts 文件夹
            const dirEntries = await fs.readdir(sourceDir, { withFileTypes: true });
            logger.info(`目录 ${sourceDir} 中的条目数量: ${dirEntries.length}`);

            // 遍历目录中的所有条目
            for (const entry of dirEntries) {
              // 如果是目录，并且包含 ts 文件，则复制整个目录
              if (entry.isDirectory()) {
                const sourceSubDir = path.join(sourceDir, entry.name);
                try {
                  // 检查目录中是否包含 .ts 文件
                  const subDirFiles = await fs.readdir(sourceSubDir);
                  const tsFiles = subDirFiles.filter(file => file.toLowerCase().endsWith('.ts'));
                  
                  if (tsFiles.length > 0) {
                    logger.info(`发现 ts 文件目录: ${sourceSubDir}，包含 ${tsFiles.length} 个 ts 文件`);
                    
                    // 目标目录 - 直接使用资源目录下的同名目录
                    const targetSubDir = path.join(subDirPath, entry.name);
                    logger.info(`目标目录: ${targetSubDir}`);

                    // 复制整个目录
                    await fileService.copyDirectory(sourceSubDir, targetSubDir);
                    logger.info(`成功复制 ts 文件目录: ${sourceSubDir} -> ${targetSubDir}`);
                  }
                } catch (subDirError) {
                  logger.warn(`检查或复制 ts 文件目录失败: ${sourceSubDir}`, subDirError);
                  // 继续处理其他目录，不中断流程
                }
              }
            }
          } catch (error) {
            logger.warn(`处理 m3u8 相关 ts 文件失败: ${file.path}`, error);
            // 继续处理，不中断流程
          }
        }

        // 处理封面文件
        let posterFileName = '';

        // 使用 path + _poster + 后缀名 作为封面文件名
        const posterFileNameBase = `${subDir.replace('/', '')}_poster`;

        // 如果用户上传了封面，优先使用用户上传的封面
        if (formData.coverPath) {
          const coverExt = path.extname(formData.coverPath);
          posterFileName = `${posterFileNameBase}${coverExt}`;
          const posterTargetPath = path.join(subDirPath, posterFileName);
          const posterCopied = await fileService.copyFile(formData.coverPath, posterTargetPath);

          if (!posterCopied) {
            logger.warn(`复制封面文件失败: ${formData.coverPath} -> ${posterTargetPath}`);
            // 如果复制失败，posterFileName 会保持为空字符串，后续会自动生成缩略图
          }
        }

        // 如果没有用户上传的封面或复制失败，则自动生成缩略图作为封面
        if (!posterFileName) {
          // 使用自定义文件名，添加pfdm_auto标识
          posterFileName = `${posterFileNameBase}_pfdm_auto.jpg`;

          // 使用 thumbnailService 生成缩略图，传入自定义文件名
          const generated = await this.thumbnailService.generateThumbnail(
            file.path,
            subDirPath,
            fileType,
            {
              width: 180,
              height: 120,
              quality: 80,
              fileName: posterFileName
            }
          );

          if (generated) {
            logger.debug(`自动生成缩略图作为封面: ${posterFileName}`);
          } else {
            logger.warn(`自动生成缩略图失败，使用默认海报`);
            // 如果生成失败，使用默认海报
            try {
              const defaultPosterPath = path.join(__dirname, '../../renderer/assets/images/yvr_defualt_app.png');
              await fs.copyFile(defaultPosterPath, path.join(subDirPath, posterFileName));
              logger.debug(`复制默认海报成功: ${posterFileName}`);
            } catch (error) {
              logger.error(`复制默认海报失败:`, error);
              posterFileName = ''; // 如果复制也失败，清空文件名
            }
          }
        }

        const md5 = await this.calculateMD5(file.path);

        logger.debug(`创建资源: 文件名=${fileName}, 类型=${fileType}, 目录前缀=${dirPrefix}`);

        // 如果是 APK 文件，尝试获取包名
        let description = formData.description || fileName;
        let pkg = ''; // 用于存储 APK 包名

        if (fileType === FileType.APP) {
          try {
            // 解析 APK 文件
            const apkInfo = await apkService.parseApk(file.path);

            // 将包名添加到资源描述中
            description = `${description} (${apkInfo.packageName})`;

            // 保存包名
            pkg = apkInfo.packageName;

            logger.debug(`获取 APK 包名成功: ${apkInfo.packageName}, 版本: ${apkInfo.versionName}`);
          } catch (error) {
            logger.warn(`获取 APK 包名失败: ${error.message}`);
            // 如果解析失败，继续创建资源但不包含包名信息
          }
        }

        return createResource({
          MD5: md5,
          describe: description,
          fileName: newFileName,
          groups: [groupName],
          index: maxIndex + 1 + index,
          path: `${subDir}/`,
          poster: posterFileName,
          showName: formData.name || fileName,
          type: fileType,
          pkg: pkg
        });
      }));

      // 将新文件添加到列表中
      config.list.push(...newFiles);

      // 保存更新后的配置
      await this.saveConfig(config);

      logger.info(`创建资源成功，共 ${newFiles.length} 个资源`);
      return { success: true, files: newFiles };
    } catch (error) {
      this.handleError('createResource', error);
      throw error;
    }
  }

  /**
   * 获取资源列表
   * @param {boolean} [useCache=true] - 是否使用缓存
   * @param {boolean} [forceRefresh=false] - 是否强制刷新
   * @returns {Promise<Array>} 包含配置对象的数组
   */
  async getResources(useCache = true, forceRefresh = false) {
    try {
      this._checkInitialized();

      // 如果强制刷新，清除缓存
      if (forceRefresh) {
        configService.clearCache('resources');
        useCache = false;
      }

      // 读取配置，不使用默认配置
      const config = await this.readConfig(useCache, false);

      if (!config) {
        logger.info('没有找到资源配置，返回空数组');
        return [];
      }

      // 确保配置中有list属性且为数组
      if (!config.list || !Array.isArray(config.list)) {
        logger.warn('资源配置格式不正确，list属性不是数组');
        config.list = [];
      }

      logger.debug(`获取资源列表成功，共 ${config.list.length} 个资源`);
      return [config];
    } catch (error) {
      this.handleError('getResources', error);
      return [];
    }
  }

  /**
   * 删除资源
   * @param {string} resourceId - 资源ID或路径
   * @returns {Promise<boolean>} 删除结果
   */
  async deleteResource(resourceId) {
    try {
      this._checkInitialized();

      const config = await this.readConfig();
      if (!config) {
        throw new Error('配置文件不存在');
      }

      // 尝试通过UUID查找资源
      let resourceIndex = config.list.findIndex(item => item.UUID === resourceId);

      // 如果没有找到，尝试通过路径查找
      if (resourceIndex === -1) {
        resourceIndex = config.list.findIndex(item => item.path === resourceId);
      }

      if (resourceIndex === -1) {
        logger.warn(`资源不存在: ${resourceId}`);
        throw new Error('资源不存在');
      }

      const resource = config.list[resourceIndex];
      const fullResourcePath = path.join(this.resourceDir, resource.path);

      logger.debug(`删除资源: ${resource.showName || resource.fileName}`, {
        path: resource.path,
        uuid: resource.UUID
      });

      // 使用fileService删除资源目录
      const deleted = await fileService.removeDirectory(fullResourcePath);
      if (!deleted) {
        throw new Error(`删除资源目录失败: ${fullResourcePath}`);
      }

      // 更新配置
      config.list.splice(resourceIndex, 1);

      // 重新调整剩余资源的索引，保持连续性
      // 从删除位置开始，将后续资源的索引值减1
      for (let i = resourceIndex; i < config.list.length; i++) {
        config.list[i].index = i;
      }

      const saved = await this.saveConfig(config);
      if (!saved) {
        throw new Error('保存配置失败');
      }

      logger.info(`删除资源成功: ${resource.showName || resource.fileName}`);
      return true;
    } catch (error) {
      this.handleError('deleteResource', error);
      throw error;
    }
  }

  /**
   * 删除方案
   * @param {string} [uuid] - 方案UUID（如果不提供，则删除整个方案）
   * @returns {Promise<boolean>} 删除结果
   */
  async deleteSolution(uuid) {
    try {
      this._checkInitialized();

      // 如果提供了UUID，则只删除该方案
      if (uuid) {
        logger.debug(`删除指定方案: ${uuid}`);

        const config = await this.readConfig();
        if (!config) {
          throw new Error('配置文件不存在');
        }

        // 检查UUID是否匹配
        if (config.UUID !== uuid) {
          logger.warn(`方案UUID不匹配: ${uuid} != ${config.UUID}`);
          throw new Error('方案UUID不匹配');
        }
      }

      // 使用fileService删除整个资源目录（包含所有资源和config.dat）
      const deleted = await fileService.removeDirectory(this.resourceDir);
      if (!deleted) {
        throw new Error('删除资源目录失败');
      }

      // 清除配置缓存
      configService.clearCache('resources');

      logger.info(`删除方案成功${uuid ? ': ' + uuid : ''}`);
      return true;
    } catch (error) {
      this.handleError('deleteSolution', error);
      throw error;
    }
  }

  /**
   * 删除资源分组
   * @deprecated 此方法已废弃，请使用 SolutionService.deleteGroup(solutionId, groupName)
   * @param {string} groupName - 要删除的分组名称
   * @returns {Promise<boolean>} 删除结果
   */
  async deleteGroup(groupName) {
    logger.warn('ResourceController.deleteGroup 方法已废弃，请使用 SolutionService.deleteGroup(solutionId, groupName)');
    // 继续执行原有逻辑，但发出警告
    try {
      this._checkInitialized();

      // 不允许删除默认分组
      if (groupName === "默认") {
        throw new Error('不能删除默认分组');
      }

      const config = await this.readConfig();
      if (!config) {
        throw new Error('配置文件不存在');
      }

      // 检查分组是否存在
      const groupIndex = config.groups.indexOf(groupName);
      if (groupIndex === -1) {
        throw new Error('分组不存在');
      }

      // 找出属于该分组的所有资源和不属于该分组的资源
      const resourcesToDelete = [];
      const resourcesToKeep = [];

      config.list.forEach(resource => {
        // 确保 resource.groups 是数组
        if (!Array.isArray(resource.groups)) {
          resource.groups = ['默认'];
        }

        // 如果资源只属于要删除的分组，标记为删除
        if (resource.groups.includes(groupName) && resource.groups.length === 1) {
          // 资源只属于这一个分组，需要删除
          resourcesToDelete.push(resource);
        } else if (resource.groups.includes(groupName)) {
          // 资源属于多个分组，只需要从分组中移除，但保留资源
          const groupIndex = resource.groups.indexOf(groupName);
          resource.groups.splice(groupIndex, 1);
          resourcesToKeep.push(resource);
        } else {
          // 资源不属于该分组，保留
          resourcesToKeep.push(resource);
        }
      });

      logger.info(`删除分组 ${groupName}`, {
        deleteCount: resourcesToDelete.length,
        keepCount: resourcesToKeep.length
      });

      // 删除属于该分组的资源文件和目录
      for (const resource of resourcesToDelete) {
        const fullResourcePath = path.join(this.resourceDir, resource.path);
        logger.debug(`删除资源目录: ${fullResourcePath}`);
        try {
          const deleted = await fileService.removeDirectory(fullResourcePath);
          if (!deleted) {
            logger.warn(`删除资源目录失败: ${fullResourcePath}`);
          }
        } catch (err) {
          logger.error(`删除资源目录失败: ${fullResourcePath}`, err);
          // 继续处理其他资源，不中断流程
        }
      }

      // 更新配置文件中的资源列表，只保留不属于该分组的资源
      config.list = resourcesToKeep;

      // 从分组列表中删除该分组
      config.groups.splice(groupIndex, 1);

      // 保存更新后的配置
      const saved = await this.saveConfig(config);
      if (!saved) {
        throw new Error('保存配置失败');
      }

      logger.info(`删除分组成功: ${groupName}`);
      return true;
    } catch (error) {
      this.handleError('deleteGroup', error);
      throw error;
    }
  }

  /**
   * 更新资源
   * @param {Object} updatedResource - 更新后的资源信息
   * @returns {Promise<boolean>} 更新结果
   */
  async updateResource(updatedResource) {
    try {
      this._checkInitialized();

      const config = await this.readConfig();
      if (!config) {
        throw new Error('配置文件不存在');
      }

      // 如果传入的是方案对象（包含UUID和groups属性，但没有path属性）
      if (updatedResource.UUID && updatedResource.groups && !updatedResource.path) {
        logger.debug('检测到方案更新请求', updatedResource);

        // 更新方案信息
        if (config.UUID === updatedResource.UUID) {
          // 保存原始资源列表
          const originalList = config.list || [];

          // 更新方案的分组
          config.groups = updatedResource.groups;

          // 如果有其他方案属性需要更新，可以在这里添加
          if (updatedResource.name) config.name = updatedResource.name;
          if (updatedResource.description) config.description = updatedResource.description;

          // 确保保留原始资源列表
          config.list = originalList;

          // 添加更新时间
          config.updatedAt = new Date().toISOString();

          // 保存更新后的配置
          const saved = await this.saveConfig(config);
          if (!saved) {
            throw new Error('保存配置失败');
          }

          logger.info('方案更新成功', { uuid: config.UUID });
          return true;
        } else {
          logger.error('方案UUID不匹配', {
            configUUID: config.UUID,
            updatedUUID: updatedResource.UUID
          });
          throw new Error('方案UUID不匹配');
        }
      }

      // 处理普通资源更新
      // 优先使用 index 查找资源，如果没有 index 则使用 path
      let resourceIndex = -1;
      if (typeof updatedResource.index === 'number') {
        // 使用 index 查找
        if (updatedResource.index >= 0 && updatedResource.index < config.list.length) {
          resourceIndex = updatedResource.index;
        }
      } else if (updatedResource.path) {
        // 使用 path 查找
        resourceIndex = config.list.findIndex(item => item.path === updatedResource.path);
      }

      if (resourceIndex === -1) {
        logger.error('资源不存在', {
          index: updatedResource.index,
          path: updatedResource.path
        });
        throw new Error('资源不存在');
      }

      // 记录更新前的资源信息（用于日志）
      const oldResource = config.list[resourceIndex];
      const oldResourcePath = path.join(this.resourceDir, oldResource.path);

      // 处理文件更新
      if (updatedResource.filePath && updatedResource.filePath !== oldResource.path) {
        try {
          // 保持原有的资源目录
          const resourceDir = path.join(this.resourceDir, oldResource.path);

          // 删除旧文件
          const oldFilePath = path.join(resourceDir, oldResource.fileName);
          await fileService.deleteFile(oldFilePath);

          // 使用 path + 时间戳 + 后缀名 作为文件名
          const fileExt = path.extname(oldResource.fileName);
          const timestamp = Date.now();
          const newFileName = `${oldResource.path.replace('/', '')}_${timestamp}${fileExt}`;

          // 复制新文件到资源目录，使用新的文件名
          const newFilePath = path.join(resourceDir, newFileName);
          await fileService.copyFile(updatedResource.filePath, newFilePath);

          // 更新资源对象中的文件名
          oldResource.fileName = newFileName;

          // 如果是APK文件，更新包名
          if (oldResource.type === FileType.APP) {
            try {
              // 解析新的APK文件
              const apkInfo = await apkService.parseApk(newFilePath);
              
              // 更新包名
              oldResource.pkg = apkInfo.packageName;
              
              // 更新描述（如果描述中包含旧的包名，则替换）
              if (oldResource.describe) {
                const oldDescription = oldResource.describe;
                const newDescription = oldDescription.replace(/\([^)]*\)$/, `(${apkInfo.packageName})`);
                oldResource.describe = newDescription;
              }

              logger.debug(`更新APK包名成功: ${apkInfo.packageName}`);
            } catch (error) {
              logger.warn(`更新APK包名失败: ${error.message}`);
              // 如果解析失败，保持原有的包名和描述不变
            }
          }

          // 检查是否需要更新自动生成的封面
          if (oldResource.poster && oldResource.poster.includes('_pfdm_auto')) {
            // 删除旧封面
            const oldPosterPath = path.join(resourceDir, oldResource.poster);
            await fileService.deleteFile(oldPosterPath);

            // 生成新的封面
            const posterFileNameBase = `${oldResource.path.replace('/', '')}_poster`;
            const newPosterFileName = `${posterFileNameBase}_pfdm_auto.jpg`;
            const generated = await this.thumbnailService.generateThumbnail(
              newFilePath,
              resourceDir,
              oldResource.type,
              {
                width: 180,
                height: 120,
                quality: 80,
                fileName: newPosterFileName
              }
            );

            if (generated) {
              oldResource.poster = newPosterFileName;
              logger.debug(`自动更新封面成功: ${newPosterFileName}`);
            } else {
              logger.warn(`自动更新封面失败`);
            }
          }

          logger.info('资源文件更新成功', {
            path: resourceDir,
            oldFileName: oldResource.fileName,
            newFileName: newFileName
          });
        } catch (error) {
          logger.error('更新资源文件失败', error);
          throw new Error(`更新资源文件失败: ${error.message}`);
        }
      }

      // 处理封面更新
      if (updatedResource.coverPath && updatedResource.coverPath !== oldResource.poster) {
        try {
          // 保持原有的资源目录
          const resourceDir = path.join(this.resourceDir, oldResource.path);

          // 删除旧封面文件
          if (oldResource.poster) {
            const oldCoverPath = path.join(resourceDir, oldResource.poster);
            await fileService.deleteFile(oldCoverPath);
          }

          // 使用 path + _poster + 时间戳 + 后缀名 作为封面文件名
          const coverExt = path.extname(updatedResource.coverPath);
          const timestamp = Date.now();
          const coverFileName = `${oldResource.path.replace('/', '')}_poster_${timestamp}${coverExt}`;
          const coverPath = path.join(resourceDir, coverFileName);

          // 复制新封面到资源目录
          await fileService.copyFile(updatedResource.coverPath, coverPath);

          // 更新封面路径
          oldResource.poster = coverFileName;

          logger.info('资源封面更新成功', {
            oldPoster: oldResource.poster,
            newPoster: coverFileName
          });
        } catch (error) {
          logger.error('更新资源封面失败', error);
          throw new Error(`更新资源封面失败: ${error.message}`);
        }
      }

      // 更新其他资源信息
      config.list[resourceIndex] = {
        ...oldResource,
        showName: updatedResource.showName || oldResource.showName,
        describe: updatedResource.describe || oldResource.describe,
        groups: updatedResource.groups || oldResource.groups,
        type: updatedResource.type || oldResource.type,
        updatedAt: new Date().toISOString()
      };

      // 保存更新后的配置
      const saved = await this.saveConfig(config);
      if (!saved) {
        throw new Error('保存配置失败');
      }

      // 记录详细的更新日志
      logger.info('资源更新成功', {
        index: resourceIndex,
        path: oldResource.path,
        name: updatedResource.showName,
        oldType: oldResource.type,
        newType: updatedResource.type,
        groups: updatedResource.groups
      });

      return true;
    } catch (error) {
      this.handleError('updateResource', error);
      throw error;
    }
  }


}

// 创建单例
const resourceController = new ResourceController();

// 导出单例
module.exports = resourceController;
