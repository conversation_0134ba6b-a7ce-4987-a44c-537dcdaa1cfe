/**
 * 基础控制器类
 * 提供控制器通用功能
 */

const EventEmitter = require('events');
const logger = require('../utils/logger');
const { handleError } = require('../utils/error-handler');

/**
 * 基础控制器类
 * @class
 * @extends EventEmitter
 */
class BaseController extends EventEmitter {
  /**
   * 构造函数
   * @param {string} controllerName - 控制器名称
   */
  constructor(controllerName) {
    super();
    this.controllerName = controllerName || this.constructor.name;
    this.initialized = false;
    
    logger.debug(`${this.controllerName} 创建`);
  }
  
  /**
   * 初始化控制器
   * @returns {Promise<void>}
   */
  async init() {
    if (this.initialized) {
      logger.debug(`${this.controllerName} 已经初始化`);
      return;
    }
    
    try {
      logger.debug(`${this.controllerName} 开始初始化`);
      await this._initializeController();
      this.initialized = true;
      logger.info(`${this.controllerName} 初始化完成`);
    } catch (error) {
      this.handleError('initialization', error);
      throw error;
    }
  }
  
  /**
   * 初始化控制器实现
   * 子类应该重写此方法
   * @protected
   * @returns {Promise<void>}
   */
  async _initializeController() {
    // 子类实现
  }
  
  /**
   * 处理错误
   * @param {string} operation - 操作名称
   * @param {Error} error - 错误对象
   */
  handleError(operation, error) {
    handleError(error, `${this.controllerName}.${operation}`);
    this.emit('error', { operation, error });
  }
  
  /**
   * 检查初始化状态
   * @protected
   * @throws {Error} 如果控制器未初始化
   */
  _checkInitialized() {
    if (!this.initialized) {
      const error = new Error(`${this.controllerName} 未初始化`);
      this.handleError('checkInitialized', error);
      throw error;
    }
  }
}

module.exports = BaseController;
