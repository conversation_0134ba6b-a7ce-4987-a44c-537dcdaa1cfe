/**
 * 升级控制器类
 * 负责检查更新和执行升级
 */

const { app } = require('electron');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const OSS = require('ali-oss');
const yaml = require('js-yaml');
const { spawn } = require('child_process');
const logger = require('../utils/logger');
const BaseController = require('./base-controller');
const { getAppVersion } = require('../services/ipc/app-handlers');
const ossConfig = require('../config/oss-config');

class UpdateController extends BaseController {
  /**
   * 事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      // 检查更新事件
      CHECK_UPDATE: 'checkUpdate',
      // 下载进度事件
      DOWNLOAD_PROGRESS: 'downloadProgress',
      // 下载完成事件
      DOWNLOAD_COMPLETE: 'downloadComplete',
      // 更新错误事件
      UPDATE_ERROR: 'updateError'
    };
  }

  constructor() {
    super('UpdateController');
    
    // Initialize OSS client with config
    this.ossClient = new OSS({
      region: ossConfig.region,
      accessKeyId: ossConfig.accessKeyId,
      accessKeySecret: ossConfig.accessKeySecret,
      bucket: ossConfig.bucket
    });
    
    this.updateInfo = null;
    this.downloadPath = null;
  }

  /**
   * 初始化控制器
   * @protected
   * @returns {Promise<void>}
   */
  async _initializeController() {
    if (!ossConfig.accessKeyId || !ossConfig.accessKeySecret) {
      logger.error('OSS credentials not found in environment variables', {
        accessKeyId: ossConfig.accessKeyId ? '已设置' : '未设置',
        accessKeySecret: ossConfig.accessKeySecret ? '已设置' : '未设置',
        envKeys: Object.keys(process.env)
      });
      throw new Error('OSS credentials not configured');
    }
    logger.info('OSS credentials loaded successfully');
    logger.debug('UpdateController: 初始化完成');
  }

  /**
   * 比较版本号
   * @private
   * @param {string} v1 - 版本号1
   * @param {string} v2 - 版本号2
   * @returns {number} 如果v1>v2返回1,v1<v2返回-1,相等返回0
   */
  _compareVersions(v1, v2) {
    const v1Parts = v1.split('.').map(Number);
    const v2Parts = v2.split('.').map(Number);

    for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
      const v1Part = v1Parts[i] || 0;
      const v2Part = v2Parts[i] || 0;
      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }
    return 0;
  }

  /**
   * 检查更新
   * @returns {Promise<Object|null>} 更新信息，如果没有更新则返回null
   */
  async checkUpdate() {
    try {
      this._checkInitialized();

      // 获取当前版本
      const currentVersion = await getAppVersion();
      logger.info('开始检查更新', { currentVersion: currentVersion.version });

      try {
        // 使用OSS客户端获取latest.yml
        const result = await this.ossClient.get(ossConfig.paths.latestYml);
        
        // 使用 js-yaml 解析 YAML 内容
        const updateInfo = yaml.load(result.content.toString());
        
        logger.debug('获取到的更新信息', updateInfo);

        // 解析版本信息
        const latestVersion = updateInfo.version;

        if (!latestVersion) {
          throw new Error('无法从更新信息中获取版本号');
        }

        // 比较版本
        if (this._compareVersions(latestVersion, currentVersion.version) > 0) {
          // 生成签名URL，有效期1小时
          const installerPath = ossConfig.paths.getInstallerPath(latestVersion);
          const downloadUrl = await this.ossClient.signatureUrl(installerPath, {
            expires: 3600
          });

          this.updateInfo = {
            version: latestVersion,
            description: updateInfo.releaseNotes || '暂无更新说明',
            releaseDate: new Date().toLocaleDateString(),
            downloadUrl: downloadUrl,
            hasUpdate: true
          };

          logger.info('发现新版本', this.updateInfo);
          return this.updateInfo;
        }

        logger.info('当前已是最新版本');
        return null;
      } catch (ossError) {
        logger.error('获取更新信息失败', {
          error: ossError,
          content: ossError.content ? ossError.content.toString() : '无内容'
        });
        throw ossError;
      }
    } catch (error) {
      this.handleError('checkUpdate', error);
      throw error;
    }
  }

  /**
   * 下载更新
   * @returns {Promise<string>} 下载文件的路径
   */
  async downloadUpdate() {
    try {
      this._checkInitialized();

      if (!this.updateInfo) {
        throw new Error('没有可用的更新信息，请先检查更新');
      }

      // 创建临时下载目录
      const downloadDir = path.join(app.getPath('temp'), 'app-updates');
      await fs.mkdir(downloadDir, { recursive: true });

      // 设置下载路径
      this.downloadPath = path.join(downloadDir, `update-${this.updateInfo.version}.exe`);

      // 下载文件
      const writer = require('fs').createWriteStream(this.downloadPath);
      const response = await axios({
        url: this.updateInfo.downloadUrl,
        method: 'GET',
        responseType: 'stream',
        onDownloadProgress: (progressEvent) => {
          const progress = progressEvent.loaded / progressEvent.total;
          this.emit(UpdateController.EVENTS.DOWNLOAD_PROGRESS, {
            progress,
            loaded: progressEvent.loaded,
            total: progressEvent.total
          });
        }
      });

      // 将响应流写入文件
      await new Promise((resolve, reject) => {
        response.data.pipe(writer);
        writer.on('finish', resolve);
        writer.on('error', reject);
      });

      logger.info('更新包下载完成', { path: this.downloadPath });
      this.emit(UpdateController.EVENTS.DOWNLOAD_COMPLETE, { path: this.downloadPath });

      return this.downloadPath;
    } catch (error) {
      this.handleError('downloadUpdate', error);
      throw error;
    }
  }

  /**
   * 安装更新
   * @returns {Promise<void>}
   */
  async installUpdate() {
    try {
      this._checkInitialized();

      if (!this.downloadPath) {
        throw new Error('没有可用的更新包，请先下载更新');
      }

      // 检查文件是否存在
      await fs.access(this.downloadPath);

      // 启动安装程序
      const args = ['--updated-from-version', app.getVersion()];
      
      // 在 Windows 上使用 spawn 启动安装程序
      if (process.platform === 'win32') {
        spawn(this.downloadPath, args, {
          detached: true,
          stdio: 'ignore'
        }).unref();
      }

      // 退出应用
      app.quit();
    } catch (error) {
      this.handleError('installUpdate', error);
      throw error;
    }
  }
}

// 创建单例
const updateController = new UpdateController();

// 初始化控制器
updateController.init().catch(error => {
  logger.error('UpdateController初始化失败', error);
});

// 导出单例
module.exports = updateController; 