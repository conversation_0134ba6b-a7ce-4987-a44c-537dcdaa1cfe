/**
 * 方案控制器类
 * 负责方案的创建、部署和导出
 */

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const { pipeline } = require('stream/promises');
const { dialog } = require('electron');
const os = require('os');
const archiver = require('archiver');
const BaseController = require('./base-controller');
const { solutionService } = require('../services/storage');
const { wsService } = require('../services/network');
const deviceController = require('./device-controller');
const { paths } = require('../utils/paths');
const logger = require('../utils/logger');
const { BATCH } = require('../utils/constants');
const { createDeployCommand } = require('../../shared/constants/commands');
const { CommandType, createFetchConfigCommand } = require('../../shared/constants/command-types');
const { _checkDiskSpace, checkConfigFolderSize } = require('../utils/disk-space');

/**
 * 获取文件大小
 * @private
 * @param {string} filePath - 文件路径
 * @returns {Promise<number>} 文件大小（字节）
 */
async function _getFileSize(filePath) {
  try {
    const stats = await fs.stat(filePath);
    const sizeMB = Math.floor(stats.size / (1024 * 1024));
    logger.info('获取文件大小', {
      filePath,
      size: `${sizeMB}MB`
    });
    return stats.size;
  } catch (error) {
    logger.error('获取文件大小失败', error);
    throw error;
  }
}

/**
 * 方案控制器类
 * @class
 * @extends BaseController
 */
class SolutionController extends BaseController {
  /**
   * 事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      // 传输进度事件
      TRANSFER_PROGRESS: 'transferProgress',
      // 部署错误事件
      DEPLOY_ERROR: 'deployError',
      // 部署取消事件
      DEPLOY_CANCELLED: 'deployCancelled'
    };
  }

  /**
   * 构造函数
   */
  constructor() {
    super('SolutionController');
    this.BATCH_SIZE = BATCH?.DEFAULT_SIZE || 5;
    this.CHUNK_SIZE = BATCH?.DEFAULT_CHUNK_SIZE || 1024 * 1024;
    this.fileReceivers = new Map();
    this.cancelledDevices = new Set();
    this.activeTransfers = new Map(); // 添加活动传输的Map
  }

  /**
   * 初始化控制器
   * @protected
   * @returns {Promise<void>}
   */
  async _initializeController() {
    // 目前不需要特殊初始化
    logger.debug('SolutionController: 初始化完成');
  }

  /**
   * 创建方案
   * @param {Array<Object>} files - 文件列表
   * @param {string} name - 方案名称
   * @param {string} description - 方案描述
   * @param {string} pcVersion - PC端版本号
   * @returns {Promise<Object>} 创建的方案对象
   */
  async createSolution(files, name, description, pcVersion) {
    try {
      this._checkInitialized();

      logger.info('开始创建方案', { name, fileCount: files.length, pcVersion });

      // 计算所有文件的总大小
      let totalSize = 0;
      for (const file of files) {
        try {
          const fileSize = await _getFileSize(file.path);
          totalSize += fileSize;
        } catch (error) {
          logger.warn(`获取文件大小失败: ${file.path}`, error);
        }
      }

      logger.info('计算完成所有文件总大小', {
        totalSize: `${Math.floor(totalSize / (1024 * 1024))}MB`,
        fileCount: files.length
      });

      // 检查资源存储目录所在磁盘的空间
      const hasEnoughSpace = await _checkDiskSpace(paths.resources.root, totalSize);
      if (!hasEnoughSpace) {
        const requiredSpaceMB = Math.floor(totalSize / (1024 * 1024));
        throw new Error(`资源存储目录所在磁盘空间不足，需要至少 ${requiredSpaceMB}MB 的可用空间`);
      }

      const solution = await solutionService.createSolution(files, name, description, pcVersion);
      logger.info('方案创建成功', { uuid: solution.UUID });

      return solution;
    } catch (error) {
      this.handleError('createSolution', error);
      throw error;
    }
  }

  /**
   * 从设备获取配置文件
   * @private
   * @param {string} deviceSN - 设备序列号
   * @returns {Promise<Object>} 设备上的配置数据
   */
  async _fetchDeviceConfig(deviceSN) {
    const maxRetries = 2;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // 创建获取配置命令
        const fetchConfigCommand = {
          type: CommandType.FETCH_CONFIG,
          data: { id: Date.now() }
        };

        // 等待配置响应
        const configPromise = new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            deviceController.removeListener(deviceController.constructor.EVENTS.CONFIG_RESPONSE, handleResponse);
            reject(new Error('获取设备配置超时'));
          }, 10000); // 10秒超时

          const handleResponse = (data) => {
            if (data.sn === deviceSN) {
              clearTimeout(timeout);
              deviceController.removeListener(deviceController.constructor.EVENTS.CONFIG_RESPONSE, handleResponse);
              resolve(data.config);
            }
          };

          // 监听取消事件
          const handleCancel = () => {
            clearTimeout(timeout);
            deviceController.removeListener(deviceController.constructor.EVENTS.CONFIG_RESPONSE, handleResponse);
            reject(new Error('部署已取消'));
          };

          // 如果设备已取消部署，立即拒绝
          if (this._isDeployCancelled(deviceSN)) {
            handleCancel();
            return;
          }

          // 监听取消事件
          this.once(SolutionController.EVENTS.DEPLOY_CANCELLED, (data) => {
            if (data.deviceSN === deviceSN) {
              handleCancel();
            }
          });

          deviceController.on(deviceController.constructor.EVENTS.CONFIG_RESPONSE, handleResponse);
        });

        // 发送命令
        await deviceController.sendCommand(deviceSN, fetchConfigCommand);

        // 等待响应
        const config = await configPromise;
        if (!config) {
          throw new Error('获取设备配置失败：无效的响应数据');
        }

        // 检查UUID是否匹配
        if (config.UUID !== this.currentUUID) {
          // UUID不匹配，设置为全量部署模式
          this.incremental = false;
          logger.info(`设备 ${deviceSN} 的UUID不匹配，切换为全量部署模式`);
          throw new Error('设备UUID不匹配，需要全量部署');
        }

        return config;
      } catch (error) {
        retryCount++;
        if (retryCount === maxRetries) {
          logger.error(`获取设备 ${deviceSN} 的配置失败，已重试${maxRetries}次`, error);
          this.incremental = false; // 只在最终失败时设置为 false
          throw error;
        }
        logger.warn(`获取设备 ${deviceSN} 的配置失败，正在进行第${retryCount}次重试`, error);
        // 等待1秒后重试
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  /**
   * 部署方案到设备
   * @param {Object} data - 部署数据
   * @param {Array<string>} data.devices - 设备序列号列表
   * @param {string} data.uuid - 方案UUID
   * @param {boolean} [data.incremental=true] - 是否使用增量部署
   * @returns {Promise<Object>} 部署结果
   */
  async deploySolution(data) {
    try {
      this._checkInitialized();

      const { devices: selectedDevices, uuid, incremental = true } = data;

      // 设置增量部署标记，默认为 true
      this.incremental = incremental;
      // 保存当前UUID用于比较
      this.currentUUID = uuid;

      // 清理之前取消的设备标记
      for (const deviceSN of selectedDevices) {
        this.cancelledDevices.delete(deviceSN);
      }

      if (!selectedDevices || !Array.isArray(selectedDevices)) {
        throw new Error('无效的设备列表');
      }

      logger.info('开始部署方案', {
        deviceCount: selectedDevices.length,
        uuid,
        incremental: this.incremental
      });

      // 获取所有连接的设备
      const allDevices = deviceController.getConnectedDevices();
      const devices = allDevices.filter(device => {
        const deviceSN = device.sn;
        const isSelected = selectedDevices.includes(deviceSN);
        const isOnline = device.status?.deviceStatus === 1;
        
        // 如果设备不在线，记录日志
        if (isSelected && !isOnline) {
          logger.warn(`设备 ${deviceSN} 不在线，将被排除`);
        }
        
        return isSelected && isOnline;
      });

      if (devices.length === 0) {
        logger.warn('没有找到选中的在线设备');
        throw new Error('没有找到选中的在线设备');
      }

      // 检查是否有设备被排除
      const excludedDevices = selectedDevices.filter(deviceSN => {
        const device = allDevices.find(d => d.sn === deviceSN);
        return !device || device.status?.deviceStatus !== 1;
      });

      if (excludedDevices.length > 0) {
        logger.warn('以下设备不在线，将被排除', { excludedDevices });
        // 发送错误事件，通知前端有设备被排除
        for (const deviceSN of excludedDevices) {
          this._sendErrorEvent({
            message: '设备不在线，无法部署',
            deviceSN: deviceSN
          });
        }
      }

      // 读取并发送 config.dat
      const configPath = paths.resources.config;
      const resourceDir = paths.resources.root;

      try {
        await fs.access(configPath);
      } catch (error) {
        logger.error('方案配置文件不存在');
        throw new Error('请先创建方案后再进行部署');
      }

      const configContent = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configContent);
      const batches = this._splitIntoBatches(devices, this.BATCH_SIZE);
      const results = {
        successful: [],
        failed: [],
        totalDevices: devices.length
      };

      // 获取设备配置
      let deviceConfig = null;
      let skipLogo = false;
      let skipBackground = false;
      try {
        deviceConfig = await this._fetchDeviceConfig(devices[0].sn);
        logger.info(`获取设备 ${devices[0].sn} 配置成功:`, deviceConfig);
        
        // 如果是增量部署，检查logo和background是否需要更新
        if (this.incremental && deviceConfig) {
          // 检查logo
          if (config.logo && deviceConfig.logo === config.logo) {
            skipLogo = true;
            logger.info(`设备 ${devices[0].sn} 的logo未变更，跳过部署`);
          }
          
          // 检查background
          if (config.background && deviceConfig.background === config.background) {
            skipBackground = true;
            logger.info(`设备 ${devices[0].sn} 的background未变更，跳过部署`);
          }
        }
      } catch (error) {
        logger.error(`获取设备 ${devices[0].sn} 配置失败:`, error);
        // 如果获取配置失败，设置为非增量部署
        this.incremental = false;
        logger.warn(`设备 ${devices[0].sn} 获取配置失败，将使用全量部署`);
      }

      // 发送开始部署事件
      this._sendProgressEvent({
        message: `开始${this.incremental ? '增量' : '全量'}部署`,
        deviceSN: devices[0].sn,
        progress: 0,
        fileName: '',
        fileIndex: 0,
        totalFiles: 0
      });

      // 如果是增量部署，从每个设备获取配置并比较差异
      if (this.incremental) {
        logger.info('使用增量部署模式，从设备获取当前配置');
        
        // 为每个设备创建独立的部署任务
        const deviceTasks = devices.map(async (device) => {
          try {
            // 检查设备是否在线
            if (device.status?.deviceStatus !== 1) {
              logger.warn(`设备 ${device.sn} 不在线，将使用全量部署`);
              // 直接开始全量部署
              await this._deployDeviceFiles(device, configContent, config.list, config, false, false, false);
              return;
            }

            try {
              // 获取设备配置
              const deviceConfig = await this._fetchDeviceConfig(device.sn);
              logger.debug(`成功获取设备 ${device.sn} 的配置`);

              // 检查logo和background是否需要更新
              let skipLogo = false;
              let skipBackground = false;
              if (deviceConfig) {
                if (config.logo && deviceConfig.logo === config.logo) {
                  skipLogo = true;
                  logger.info(`设备 ${device.sn} 的logo未变更，跳过部署`);
                }
                if (config.background && deviceConfig.background === config.background) {
                  skipBackground = true;
                  logger.info(`设备 ${device.sn} 的background未变更，跳过部署`);
                }
              }

              // 分析资源差异
              const deviceResources = deviceConfig.list || [];
              const resourcesToUpdate = config.list.filter(newResource => {
                const existingResource = deviceResources.find(r => 
                  r.path === newResource.path && 
                  r.fileName === newResource.fileName
                );

                if (!existingResource) {
                  return true;
                }

                return this._checkResourceNeedsUpdate(newResource, existingResource).needsUpdate;
              });

              logger.debug(`设备 ${device.sn} 需要更新 ${resourcesToUpdate.length} 个资源`);
              
              // 开始增量部署
              await this._deployDeviceFiles(device, configContent, resourcesToUpdate, config, true, skipLogo, skipBackground);
            } catch (error) {
              logger.warn(`获取设备 ${device.sn} 配置失败，将使用全量部署`, error);
              // 配置获取失败，使用全量部署
              await this._deployDeviceFiles(device, configContent, config.list, config, false, false, false);
            }
          } catch (error) {
            logger.error(`设备 ${device.sn} 部署失败`, error);
            results.failed.push(device.ip);
          }
        });

        // 等待所有设备部署完成
        await Promise.allSettled(deviceTasks);
      } else {
        // 全量部署模式
        const deviceTasks = devices.map(async (device) => {
          try {
            await this._deployDeviceFiles(device, configContent, config.list, config, false, false, false);
          } catch (error) {
            logger.error(`设备 ${device.sn} 部署失败`, error);
            results.failed.push(device.ip);
          }
        });

        // 等待所有设备部署完成
        await Promise.allSettled(deviceTasks);
      }

      // 计算成功部署的设备数量
      const uniqueFailedDevices = new Set(results.failed);
      const successfulDevices = devices
        .map(d => d.ip)
        .filter(ip => !uniqueFailedDevices.has(ip));

      results.successful = successfulDevices;

      logger.info('方案部署完成', {
        successCount: results.successful.length,
        failCount: uniqueFailedDevices.size,
        totalCount: results.totalDevices,
        incremental: this.incremental
      });

      return results;
    } catch (error) {
      this.handleError('deploySolution', error);
      throw error;
    }
  }

  /**
   * 导出方案
   * @param {string} solutionId - 方案ID
   * @returns {Promise<string>} 导出路径
   */
  async exportSolution(solutionId) {
    try {
      this._checkInitialized();

      logger.info('开始导出方案', { solutionId });

      // 读取配置文件
      const configPath = paths.resources.config;
      const resourceDir = paths.resources.root;

      try {
        await fs.access(configPath);
      } catch (error) {
        logger.error('方案配置文件不存在');
        throw new Error('请先创建方案后再进行导出');
      }

      const configContent = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configContent);

      // 选择导出目录
      const result = await dialog.showSaveDialog({
        title: '导出方案',
        defaultPath: path.join(os.homedir(), 'Desktop', `方案_${Date.now()}.zip`),
        filters: [
          { name: '压缩文件', extensions: ['zip'] }
        ]
      });

      if (result.canceled) {
        logger.info('用户取消了导出操作');
        throw new Error('用户取消了导出操作');
      }

      const exportPath = result.filePath;

      // 创建临时目录
      const tempDir = path.join(os.tmpdir(), `solution_export_${Date.now()}`);
      await fs.mkdir(tempDir, { recursive: true });

      // 复制配置文件
      await fs.copyFile(configPath, path.join(tempDir, 'config.dat'));
      logger.debug('配置文件已复制到临时目录');

      // 复制资源文件
      if (config.list && Array.isArray(config.list)) {
        for (const item of config.list) {
          const targetDir = path.join(tempDir, item.path);

          // 确保目标目录存在
          await fs.mkdir(targetDir, { recursive: true });

          // 复制主资源文件
          const sourceFilePath = path.join(resourceDir, item.path, item.fileName);
          const targetFilePath = path.join(targetDir, item.fileName);
          try {
            await fs.copyFile(sourceFilePath, targetFilePath);
            logger.debug(`资源文件已复制: ${item.fileName}`);

            // 特殊处理 m3u8 文件 - 复制相关的 ts 文件夹
            if (item.fileName.toLowerCase().endsWith('.m3u8')) {
              const resourceDirPath = path.join(resourceDir, item.path);

              try {
                // 查找资源目录下的所有条目
                const dirEntries = await fs.readdir(resourceDirPath, { withFileTypes: true });

                // 遍历目录中的所有条目
                for (const entry of dirEntries) {
                  // 如果是目录，并且包含 ts 文件，则复制整个目录
                  if (entry.isDirectory()) {
                    const subDirPath = path.join(resourceDirPath, entry.name);

                    try {
                      // 检查目录中是否包含 .ts 文件
                      const subDirFiles = await fs.readdir(subDirPath);
                      const hasTsFiles = subDirFiles.some(file => file.toLowerCase().endsWith('.ts'));

                      if (hasTsFiles) {
                        logger.debug(`发现 ts 文件目录: ${subDirPath}，开始复制...`);

                        // 目标目录
                        const targetSubDir = path.join(targetDir, entry.name);

                        // 复制整个目录
                        await fs.mkdir(targetSubDir, { recursive: true });

                        // 复制所有 ts 文件
                        for (const file of subDirFiles) {
                          if (file.toLowerCase().endsWith('.ts')) {
                            const tsSourcePath = path.join(subDirPath, file);
                            const tsTargetPath = path.join(targetSubDir, file);

                            try {
                              await fs.copyFile(tsSourcePath, tsTargetPath);
                              logger.debug(`ts 文件已复制: ${file}`);
                            } catch (tsError) {
                              logger.warn(`复制 ts 文件失败: ${file}`, tsError);
                              // 继续复制其他 ts 文件，不中断流程
                            }
                          }
                        }
                      }
                    } catch (subDirError) {
                      logger.warn(`检查 ts 文件目录失败: ${subDirPath}`, subDirError);
                      // 继续处理其他目录，不中断流程
                    }
                  }
                }
              } catch (error) {
                logger.warn(`处理 m3u8 相关 ts 文件目录失败: ${resourceDirPath}`, error);
                // 继续处理，不中断流程
              }
            }
          } catch (error) {
            logger.error(`复制资源文件失败: ${item.fileName}`, error);
          }

          // 复制封面/缩略图文件
          if (item.poster) {
            const posterSourcePath = path.join(resourceDir, item.path, item.poster);
            const posterTargetPath = path.join(targetDir, item.poster);
            try {
              await fs.copyFile(posterSourcePath, posterTargetPath);
              logger.debug(`封面/缩略图文件已复制: ${item.poster}`);
            } catch (error) {
              logger.warn(`复制封面/缩略图文件失败: ${item.poster}`, error);
            }
          }

          // 向后兼容：检查旧版缩略图是否变更（如果与poster不同）
          if (item.thumbnail &&
              item.thumbnail !== item.poster &&
              item.thumbnail !== lastResource.thumbnail) {
            const thumbnailSourcePath = path.join(resourceDir, item.path, item.thumbnail);
            const thumbnailTargetPath = path.join(targetDir, item.thumbnail);
            try {
              await fs.copyFile(thumbnailSourcePath, thumbnailTargetPath);
              logger.debug(`旧版缩略图文件已复制: ${item.thumbnail}`);
            } catch (error) {
              logger.warn(`复制旧版缩略图文件失败: ${item.thumbnail}`, error);
            }
          }
        }
      }

      // 如果存在logo，复制logo文件
      if (config.logo) {
        const logoPath = path.join(resourceDir, config.logo);
        await fs.copyFile(logoPath, path.join(tempDir, config.logo));
        logger.debug(`Logo文件已复制: ${config.logo}`);
      }

      // 压缩文件
      const output = fsSync.createWriteStream(exportPath);
      const archive = archiver('zip', {
        zlib: { level: 9 } // 最高压缩级别
      });

      // 设置压缩事件
      const archivePromise = new Promise((resolve, reject) => {
        output.on('close', () => {
          logger.info('方案导出完成', { size: archive.pointer() });
          resolve();
        });

        archive.on('error', (err) => {
          logger.error('压缩文件失败', err);
          reject(err);
        });
      });

      // 将输出流连接到压缩器
      archive.pipe(output);

      // 添加临时目录中的所有文件到压缩包
      archive.directory(tempDir, false);

      // 完成压缩
      await archive.finalize();
      await archivePromise;

      // 清理临时目录
      setTimeout(async () => {
        try {
          await fs.rm(tempDir, { recursive: true, force: true });
          logger.debug('临时目录已清理');
        } catch (error) {
          logger.error('清理临时目录失败', error);
        }
      }, 5000);

      return exportPath;
    } catch (error) {
      this.handleError('exportSolution', error);
      throw error;
    }
  }

  /**
   * 获取方案配置
   * @private
   * @param {string} solutionId - 方案ID
   * @returns {Promise<Object>} 方案配置
   */
  async _getSolutionConfig(solutionId) {
    const solution = await solutionService.getSolution(solutionId);
    if (!solution) {
      throw new Error('方案不存在');
    }
    return solution;
  }

  /**
   * 将设备分成批次
   * @private
   * @param {Array<Object>} devices - 设备列表
   * @param {number} batchSize - 批次大小
   * @returns {Array<Array<Object>>} 批次列表
   */
  _splitIntoBatches(devices, batchSize) {
    const batches = [];
    for (let i = 0; i < devices.length; i += batchSize) {
      batches.push(devices.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 部署配置文件到设备
   * @private
   * @param {Array<Object>} batch - 设备批次
   * @param {string} configContent - 配置文件内容
   * @param {boolean} [isConfigOnly=false] - 是否只发送配置文件
   * @returns {Promise<Object>} 部署结果
   */
  async _deployConfig(batch, configContent, isConfigOnly = false) {
    // 使用 createDeployCommand 函数创建部署命令
    const configMessage = createDeployCommand(null, this.incremental);
    
    // 解析配置内容
    const config = JSON.parse(configContent);
    
    // 重新序列化配置，保留所有字段（包括background）
    const deployConfigContent = JSON.stringify(config);
    
    // 添加特定的部署配置数据
    configMessage.data.fileName = 'config.dat';
    configMessage.data.content = deployConfigContent;
    configMessage.data.isConfig = true;

    const failedDevices = [];
    const sendPromises = batch.map(async (device) => {
      try {
        // 发送进度事件 - 开始部署配置文件（每个设备单独发送）
        this._sendProgressEvent({
          type: 'config',
          progress: 0,
          fileName: 'config.dat',
          deviceSN: device.sn
        });

        await wsService.sendToClient(device.ip, configMessage);

        // 发送进度事件 - 配置文件部署完成（每个设备单独发送）
        this._sendProgressEvent({
          type: 'config',
          progress: 1,
          fileName: 'config.dat',
          deviceSN: device.sn
        });

        // 如果只发送配置文件，则在配置文件发送完成后立即发送部署完成指令
        if (isConfigOnly) {
          // 等待一小段时间确保文件处理完成
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // 检查设备是否已取消部署
          if (!this._isDeployCancelled(device.sn)) {
            const deployEndMessage = {
              type: CommandType.DEPLOY_END,
              data: {
                id: Date.now()
              }
            };
            
            await wsService.sendToClient(device.ip, deployEndMessage);
            logger.debug(`发送部署结束命令到设备 ${device.ip} 成功`);
          } else {
            logger.debug(`设备 ${device.sn} 已取消部署，跳过发送部署结束命令`);
          }
        }

        return null;
      } catch (error) {
        logger.error(`发送配置到设备 ${device.ip} 失败`, error);
        this._sendErrorEvent({
          message: `发送配置到设备 ${device.ip} 失败: ${error.message || '未知错误'}`,
          deviceSN: device.sn
        });
        return device.ip;
      }
    });

    const results = await Promise.all(sendPromises);
    failedDevices.push(...results.filter(ip => ip !== null));

    return { failedDevices };
  }

  /**
   * 部署文件到设备批次
   * @private
   * @param {Array<Object>} batch - 设备批次
   * @param {Object} file - 文件信息
   * @param {number} fileIndex - 文件索引
   * @param {number} totalFiles - 总文件数
   * @returns {Promise<Object>} 部署结果
   */
  async _deployFileToBatch(batch, file, fileIndex = 0, totalFiles = 1) {
    logger.debug(`开始部署文件: ${file.fileName} 到 ${file.targetDir}`);

    // 创建设备IP到SN的映射
    const deviceMap = new Map();
    batch.forEach(device => {
      deviceMap.set(device.ip, device.sn);
      // 记录活动传输
      this.activeTransfers.set(device.sn, {
        fileName: file.fileName,
        fileIndex,
        totalFiles
      });
    });

    const activeDevices = new Set(batch.map(device => device.ip));
    const failedDevices = new Set();

    try {
      // 发送开始标记
      const startMessage = createDeployCommand(null, this.incremental);
      startMessage.data.fileName = file.fileName;
      startMessage.data.targetDir = file.targetDir;
      startMessage.data.isStart = true;

      for (const device of batch) {
        // 检查设备是否已取消部署
        if (this._isDeployCancelled(device.sn)) {
          logger.info(`设备 ${device.sn} 已取消部署，跳过文件 ${file.fileName}`);
          activeDevices.delete(device.ip);
          failedDevices.add(device.ip);
          this.activeTransfers.delete(device.sn);
          continue;
        }

        // 检查设备是否在线
        if (!deviceController.isDeviceOnline(device.sn)) {
          logger.warn(`设备 ${device.sn} 离线，跳过文件 ${file.fileName}`);
          activeDevices.delete(device.ip);
          failedDevices.add(device.ip);
          this.activeTransfers.delete(device.sn);
          this._sendErrorEvent({
            message: '设备离线，部署已中断',
            deviceSN: device.sn
          });
          continue;
        }

        try {
          this._sendProgressEvent({
            type: 'file',
            progress: 0,
            fileName: file.fileName,
            fileIndex: fileIndex,
            totalFiles: totalFiles,
            deviceSN: device.sn
          });

          await wsService.sendToClient(device.ip, startMessage);
        } catch (error) {
          logger.error(`发送开始标记到 ${device.ip} 失败`, error);
          this._sendErrorEvent({
            message: `发送开始标记到 ${device.ip} 失败: ${error.message || '未知错误'}`,
            deviceSN: device.sn
          });
          activeDevices.delete(device.ip);
          failedDevices.add(device.ip);
          this.activeTransfers.delete(device.sn);
        }
      }

      if (activeDevices.size === 0) {
        return { failedDevices: Array.from(failedDevices) };
      }

      // 使用流式读取文件
      const fileStream = fsSync.createReadStream(file.path, { highWaterMark: this.CHUNK_SIZE });
      let chunkIndex = 0;
      const deviceLastProgress = new Map();
      const progressThreshold = 0.1;

      for await (const chunk of fileStream) {
        // 检查是否有设备被取消
        for (const ip of activeDevices) {
          const deviceSN = deviceMap.get(ip);
          if (this._isDeployCancelled(deviceSN)) {
            logger.info(`设备 ${deviceSN} 已取消部署，跳过剩余块`);
            activeDevices.delete(ip);
            failedDevices.add(ip);
            this.activeTransfers.delete(deviceSN);
            continue;
          }

          // 检查设备是否在线
          if (!deviceController.isDeviceOnline(deviceSN)) {
            logger.warn(`设备 ${deviceSN} 离线，跳过剩余块`);
            activeDevices.delete(ip);
            failedDevices.add(ip);
            this.activeTransfers.delete(deviceSN);
            this._sendErrorEvent({
              message: '设备离线，部署已中断',
              deviceSN: deviceSN
            });
            continue;
          }
        }

        if (activeDevices.size === 0) {
          break;
        }

        const chunkMessage = createDeployCommand(null, this.incremental);
        chunkMessage.data.fileName = file.fileName;
        chunkMessage.data.targetDir = file.targetDir;
        chunkMessage.data.content = chunk.toString('base64');
        chunkMessage.data.index = chunkIndex;

        for (const ip of activeDevices) {
          const deviceSN = deviceMap.get(ip);
          
          try {
            await wsService.sendToClient(ip, chunkMessage);

            // 计算进度
            const fileSize = fsSync.statSync(file.path).size;
            const currentProgress = (chunkIndex * this.CHUNK_SIZE + chunk.length) / fileSize;
            const lastProgress = deviceLastProgress.get(deviceSN) || 0;

            if (currentProgress - lastProgress >= progressThreshold || chunkIndex === 0) {
              this._sendProgressEvent({
                type: 'file',
                progress: currentProgress,
                fileName: file.fileName,
                fileIndex: fileIndex,
                totalFiles: totalFiles,
                deviceSN: deviceSN
              });

              deviceLastProgress.set(deviceSN, currentProgress);
            }
          } catch (error) {
            logger.error(`发送块 ${chunkIndex} 到 ${ip} 失败`, error);
            activeDevices.delete(ip);
            failedDevices.add(ip);
            this.activeTransfers.delete(deviceSN);
            this._sendErrorEvent({
              message: `发送文件块失败: ${error.message || '未知错误'}`,
              deviceSN: deviceSN
            });
          }
        }

        chunkIndex++;
      }

      // 发送结束标记
      if (activeDevices.size > 0) {
        const endMessage = createDeployCommand(null, this.incremental);
        endMessage.data.fileName = file.fileName;
        endMessage.data.targetDir = file.targetDir;
        endMessage.data.isEnd = true;

        for (const ip of activeDevices) {
          const deviceSN = deviceMap.get(ip);
          
          // 检查设备是否已取消部署
          if (this._isDeployCancelled(deviceSN)) {
            logger.info(`设备 ${deviceSN} 已取消部署，跳过结束标记`);
            activeDevices.delete(ip);
            failedDevices.add(ip);
            this.activeTransfers.delete(deviceSN);
            continue;
          }

          try {
            await wsService.sendToClient(ip, endMessage);

            this._sendProgressEvent({
              type: 'file',
              progress: 1,
              fileName: file.fileName,
              fileIndex: fileIndex,
              totalFiles: totalFiles,
              deviceSN: deviceSN
            });

            if (fileIndex === totalFiles - 1) {
              await new Promise(resolve => setTimeout(resolve, 1000));
              
              // 检查设备是否已取消部署
              if (!this._isDeployCancelled(deviceSN)) {
                const deployEndMessage = {
                  type: CommandType.DEPLOY_END,
                  data: {
                    id: Date.now()
                  }
                };
                
                await wsService.sendToClient(ip, deployEndMessage);
                logger.debug(`发送部署结束命令到设备 ${ip} 成功`);
              } else {
                logger.debug(`设备 ${deviceSN} 已取消部署，跳过发送部署结束命令`);
              }
            }
          } catch (error) {
            logger.error(`发送结束标记到 ${ip} 失败`, error);
            activeDevices.delete(ip);
            failedDevices.add(ip);
            this.activeTransfers.delete(deviceSN);
          }
        }
      }

      // 清理活动传输记录
      for (const device of batch) {
        this.activeTransfers.delete(device.sn);
      }

      return {
        failedDevices: Array.from(failedDevices)
      };
    } catch (error) {
      logger.error('部署文件批次失败', error);

      for (const device of batch) {
        this._sendErrorEvent({
          message: `部署文件 ${file.fileName} 失败: ${error.message || '未知错误'}`,
          deviceSN: device.sn
        });
        this.activeTransfers.delete(device.sn);
      }

      return {
        failedDevices: [...batch.map(d => d.ip)]
      };
    }
  }

  /**
   * 发送传输进度事件
   * @private
   * @param {Object} progressData - 进度数据
   */
  _sendProgressEvent(progressData) {
    try {
      // 确保进度数据包含设备SN
      if (!progressData.deviceSN) {
        logger.warn('进度数据缺少设备SN', progressData);

        // 尝试从调用栈中获取更多信息
        const stack = new Error().stack;
        logger.warn('调用栈信息', { stack });

        return;
      }

      // 使用 EventEmitter 发送事件
      logger.info('发送进度事件', {
        type: progressData.type,
        progress: progressData.progress,
        deviceSN: progressData.deviceSN,
        fileName: progressData.fileName,
        fileIndex: progressData.fileIndex,
        totalFiles: progressData.totalFiles
      });

      this.emit(SolutionController.EVENTS.TRANSFER_PROGRESS, progressData);
    } catch (error) {
      logger.error('发送进度事件失败', error);
    }
  }

  /**
   * 发送部署错误事件
   * @private
   * @param {Object} errorData - 错误数据
   */
  _sendErrorEvent(errorData) {
    try {
      // 记录错误事件
      logger.error('部署错误', {
        message: errorData.message,
        deviceSN: errorData.deviceSN
      });

      // 使用 EventEmitter 发送事件
      this.emit(SolutionController.EVENTS.DEPLOY_ERROR, errorData);
    } catch (error) {
      logger.error('发送错误事件失败', error);
    }
  }

  /**
   * 将缓冲区分割成块
   * @private
   * @param {Buffer} buffer - 缓冲区
   * @param {number} chunkSize - 块大小
   * @returns {Array<Buffer>} 块列表
   */
  _splitIntoChunks(buffer, chunkSize) {
    const chunks = [];
    for (let i = 0; i < buffer.length; i += chunkSize) {
      // 使用Buffer.subarray代替slice，避免警告
      chunks.push(buffer.subarray(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 获取上一次部署的资源状态
   * @private
   * @returns {Promise<Object>} 上一次部署的资源状态
   */
  async _getLastDeployState() {
    try {
      const statePath = path.join(paths.resources.root, 'deploy_state.json');

      // 检查文件是否存在
      try {
        await fs.access(statePath);
      } catch (error) {
        logger.debug('部署状态文件不存在');
        return null;
      }

      // 读取文件内容
      const stateContent = await fs.readFile(statePath, 'utf8');
      const state = JSON.parse(stateContent);

      logger.debug('成功读取部署状态文件', { resourceCount: state.resources?.length || 0 });
      return state;
    } catch (error) {
      logger.error('读取部署状态文件失败', error);
      return null;
    }
  }

  /**
   * 保存当前部署状态
   * @private
   * @param {Object} config - 当前配置
   * @returns {Promise<void>}
   */
  async _saveDeployState(config) {
    try {
      const statePath = path.join(paths.resources.root, 'deploy_state.json');

      // 创建状态对象
      const state = {
        timestamp: Date.now(),
        resources: config.list || []
      };

      // 写入文件
      await fs.writeFile(statePath, JSON.stringify(state, null, 2));

      logger.debug('成功保存部署状态文件', { resourceCount: state.resources.length });
    } catch (error) {
      logger.error('保存部署状态文件失败', error);
    }
  }

  /**
   * 检查资源是否需要更新
   * @private
   * @param {Object} currentResource - 当前资源
   * @param {Object} lastResource - 上一次部署的资源
   * @returns {Object} 检查结果，包含是否需要更新及原因
   */
  _checkResourceNeedsUpdate(currentResource, lastResource) {
    // 如果上一次部署没有这个资源，需要更新
    if (!lastResource) {
      return { needsUpdate: true, reason: '新增资源' };
    }

    // 检查主文件是否变更
    if (currentResource.MD5 !== lastResource.MD5) {
      return { needsUpdate: true, reason: '主文件MD5变更' };
    }

    // 检查封面/缩略图是否变更
    if (currentResource.poster !== lastResource.poster) {
      return { needsUpdate: true, reason: '封面/缩略图变更' };
    }

    // 向后兼容：检查旧版缩略图是否变更（如果与poster不同）
    if (currentResource.thumbnail &&
        currentResource.thumbnail !== currentResource.poster &&
        currentResource.thumbnail !== lastResource.thumbnail) {
      return { needsUpdate: true, reason: '旧版缩略图变更' };
    }

    // 检查背景图是否变更
    if (currentResource.background !== lastResource.background) {
      return { needsUpdate: true, reason: '背景图变更' };
    }

    // 没有变更，不需要更新
    return { needsUpdate: false };
  }

  // 修改 _deployDeviceFiles 方法，添加 skipLogo 和 skipBackground 参数
  async _deployDeviceFiles(device, configContent, resourcesToUpdate, config, isIncremental, skipLogo = false, skipBackground = false) {
    try {
      // 先发送配置文件
      logger.debug(`开始部署配置文件到设备 ${device.sn}`);
      const configResult = await this._deployConfig([device], configContent, resourcesToUpdate.length === 0 && !config.logo && !config.background);
      
      if (configResult.failedDevices.includes(device.ip)) {
        throw new Error('配置文件部署失败');
      }

      // 等待设备准备就绪
      logger.info(`等待 2 秒确保目录创建完成`);
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 计算总文件数
      let totalFiles = 0;
      
      // 计算主资源文件数量
      totalFiles += resourcesToUpdate ? resourcesToUpdate.length : 0;
      
      // 计算封面/缩略图文件数量
      const thumbnailCount = resourcesToUpdate ? resourcesToUpdate.filter(item => item.thumbnail && item.thumbnail !== item.poster).length : 0;
      totalFiles += thumbnailCount;
      
      const posterCount = resourcesToUpdate ? resourcesToUpdate.filter(item => item.poster).length : 0;
      totalFiles += posterCount;
      
      // 计算m3u8相关ts文件数量
      let tsFileCount = 0;
      for (const item of resourcesToUpdate || []) {
        if (item.fileName.toLowerCase().endsWith('.m3u8')) {
          const resourceDirPath = path.join(paths.resources.root, item.path);
          try {
            const dirEntries = await fs.readdir(resourceDirPath, { withFileTypes: true });
            for (const entry of dirEntries) {
              if (entry.isDirectory()) {
                const subDirPath = path.join(resourceDirPath, entry.name);
                try {
                  const subDirFiles = await fs.readdir(subDirPath);
                  tsFileCount += subDirFiles.filter(file => file.toLowerCase().endsWith('.ts')).length;
                } catch (error) {
                  logger.warn(`计算ts文件数量失败: ${subDirPath}`, error);
                }
              }
            }
          } catch (error) {
            logger.warn(`计算m3u8相关文件数量失败: ${resourceDirPath}`, error);
          }
        }
      }
      totalFiles += tsFileCount;
      
      // 添加logo和background文件数量
      if (config.logo && !skipLogo) totalFiles++;
      if (config.background && !skipBackground) totalFiles++;

      logger.debug(`设备 ${device.sn} 需要部署的总文件数: ${totalFiles}`, {
        mainFiles: resourcesToUpdate?.length || 0,
        thumbnails: thumbnailCount,
        posters: posterCount,
        tsFiles: tsFileCount,
        logo: config.logo && !skipLogo ? 1 : 0,
        background: config.background && !skipBackground ? 1 : 0
      });

      let fileIndex = 0;

      // 部署logo文件
      if (config.logo && !skipLogo) {
        const logoPath = path.join(paths.resources.root, config.logo);
        try {
          logger.debug(`开始部署Logo文件: ${config.logo} (${fileIndex + 1}/${totalFiles})`);
          const logoResult = await this._deployFileToBatch(
            [{ ip: device.ip, sn: device.sn }],
            {
              fileName: config.logo,
              path: logoPath,
              targetDir: ''
            },
            fileIndex,
            totalFiles
          );
          fileIndex++;

          if (logoResult.failedDevices.includes(device.ip)) {
            throw new Error('Logo文件部署失败');
          }
        } catch (error) {
          logger.error(`部署Logo文件失败`, error);
          throw error;
        }
      }

      // 部署背景图文件
      if (config.background && !skipBackground) {
        const bgPath = path.join(paths.resources.root, config.background);
        try {
          logger.debug(`开始部署背景图文件: ${config.background} (${fileIndex + 1}/${totalFiles})`);
          const bgResult = await this._deployFileToBatch(
            [{ ip: device.ip, sn: device.sn }],
            {
              fileName: config.background,
              path: bgPath,
              targetDir: ''
            },
            fileIndex,
            totalFiles
          );
          fileIndex++;

          if (bgResult.failedDevices.includes(device.ip)) {
            throw new Error('背景图文件部署失败');
          }
        } catch (error) {
          logger.error(`部署背景图文件失败`, error);
          throw error;
        }
      }

      // 部署资源文件
      for (const item of resourcesToUpdate) {
        if (fileIndex > 0) {
          logger.info(`文件间延时 200 毫秒，减少事件阻塞`);
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        const filePath = path.join(paths.resources.root, item.path, item.fileName);
        try {
          logger.debug(`开始部署主资源文件: ${item.fileName} 到 ${item.path} (${fileIndex + 1}/${totalFiles})`);
          const fileResult = await this._deployFileToBatch(
            [{ ip: device.ip, sn: device.sn }],
            {
              fileName: item.fileName,
              path: filePath,
              targetDir: item.path
            },
            fileIndex,
            totalFiles
          );
          fileIndex++;

          if (fileResult.failedDevices.includes(device.ip)) {
            throw new Error(`文件 ${item.fileName} 部署失败`);
          }

          // 处理m3u8相关文件
          if (item.fileName.toLowerCase().endsWith('.m3u8')) {
            fileIndex = await this._deployM3u8RelatedFiles(device, item, fileIndex, totalFiles);
          }

          // 部署封面/缩略图文件
          if (item.poster) {
            fileIndex = await this._deployPosterFile(device, item, fileIndex, totalFiles);
          }

          // 部署旧版缩略图文件
          if (item.thumbnail && item.thumbnail !== item.poster) {
            fileIndex = await this._deployThumbnailFile(device, item, fileIndex, totalFiles);
          }
        } catch (error) {
          logger.error(`部署文件 ${item.fileName} 失败`, error);
          throw error;
        }
      }
    } catch (error) {
      logger.error(`设备 ${device.sn} 文件部署失败`, error);
      throw error;
    }
  }

  // 添加辅助方法处理m3u8相关文件
  async _deployM3u8RelatedFiles(device, item, fileIndex, totalFiles) {
    const resourceDirPath = path.join(paths.resources.root, item.path);
    try {
      const dirEntries = await fs.readdir(resourceDirPath, { withFileTypes: true });
      for (const entry of dirEntries) {
        if (entry.isDirectory()) {
          const subDirPath = path.join(resourceDirPath, entry.name);
          try {
            const subDirFiles = await fs.readdir(subDirPath);
            const hasTsFiles = subDirFiles.some(file => file.toLowerCase().endsWith('.ts'));
            if (hasTsFiles) {
              for (const file of subDirFiles) {
                if (file.toLowerCase().endsWith('.ts')) {
                  const tsFilePath = path.join(subDirPath, file);
                  const targetDir = path.join(item.path, entry.name);
                  const tsResult = await this._deployFileToBatch(
                    [{ ip: device.ip, sn: device.sn }],
                    {
                      fileName: file,
                      path: tsFilePath,
                      targetDir: targetDir
                    },
                    fileIndex,
                    totalFiles
                  );
                  fileIndex++;
                }
              }
            }
          } catch (error) {
            logger.warn(`处理ts文件目录失败: ${subDirPath}`, error);
          }
        }
      }
    } catch (error) {
      logger.warn(`处理m3u8相关文件失败: ${resourceDirPath}`, error);
    }
    return fileIndex;
  }

  // 添加辅助方法处理封面/缩略图文件
  async _deployPosterFile(device, item, fileIndex, totalFiles) {
    const posterPath = path.join(paths.resources.root, item.path, item.poster);
    try {
      await fs.access(posterPath);
      const posterResult = await this._deployFileToBatch(
        [{ ip: device.ip, sn: device.sn }],
        {
          fileName: item.poster,
          path: posterPath,
          targetDir: item.path
        },
        fileIndex,
        totalFiles
      );
    } catch (error) {
      logger.warn(`部署封面/缩略图文件失败: ${item.poster}`, error);
    }
    return fileIndex + 1;
  }

  // 添加辅助方法处理旧版缩略图文件
  async _deployThumbnailFile(device, item, fileIndex, totalFiles) {
    const thumbnailPath = path.join(paths.resources.root, item.path, item.thumbnail);
    try {
      await fs.access(thumbnailPath);
      const thumbnailResult = await this._deployFileToBatch(
        [{ ip: device.ip, sn: device.sn }],
        {
          fileName: item.thumbnail,
          path: thumbnailPath,
          targetDir: item.path
        },
        fileIndex,
        totalFiles
      );
    } catch (error) {
      logger.warn(`部署旧版缩略图文件失败: ${item.thumbnail}`, error);
    }
    return fileIndex + 1;
  }

  /**
   * 取消设备部署
   * @param {string} deviceSN - 设备序列号
   */
  async cancelDeploy(deviceSN) {
    try {
      // 将设备添加到取消集合中
      this.cancelledDevices.add(deviceSN);
      
      // 发送取消事件
      this.emit(SolutionController.EVENTS.DEPLOY_CANCELLED, {
        deviceSN,
        message: '部署已取消'
      });

      // 发送错误事件，通知前端部署已取消
      this._sendErrorEvent({
        message: '部署已取消',
        deviceSN: deviceSN
      });

      // 清理该设备的活动传输
      this.activeTransfers.delete(deviceSN);

      // 获取设备IP
      const device = deviceController.getConnectedDevices().find(d => d.sn === deviceSN);
      if (device) {
        // 发送取消部署命令
        const cancelMessage = {
          type: CommandType.DEPLOY_CANCEL,
          data: {
            id: Date.now()
          }
        };
        
        await wsService.sendToClient(device.ip, cancelMessage);
        logger.debug(`发送部署取消命令到设备 ${device.ip} 成功`);
      }
      
      logger.info(`设备 ${deviceSN} 部署已取消`);

      // 返回失败结果
      return {
        successful: [],
        failed: [deviceSN],
        totalDevices: 1
      };
    } catch (error) {
      logger.error(`取消设备 ${deviceSN} 部署失败`, error);
      throw error;
    }
  }

  /**
   * 检查设备是否已取消部署
   * @private
   * @param {string} deviceSN - 设备序列号
   * @returns {boolean} 是否已取消
   */
  _isDeployCancelled(deviceSN) {
    return this.cancelledDevices.has(deviceSN);
  }
}

// 创建单例
const solutionController = new SolutionController();

// 导出单例
module.exports = solutionController;
