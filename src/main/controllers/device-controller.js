/**
 * 设备控制器类
 * 负责设备管理和控制
 */

const fs = require('fs').promises;
const path = require('path');
const BaseController = require('./base-controller');
const networkController = require('./network-controller');
const { wsService } = require('../services/network');
const configService = require('../services/config');
const { paths } = require('../utils/paths');
const logger = require('../utils/logger');
const { DEVICE_STATUS } = require('../utils/constants');
const { createNotFoundError, createValidationError } = require('../utils/error-handler');
// 使用相对路径导入
const { CommandType } = require('../../shared/constants/commands.js');

/**
 * 设备控制器类
 * @class
 * @extends BaseController
 */
class DeviceController extends BaseController {
  /**
   * 构造函数
   */
  constructor() {
    super('DeviceController');
    this.deviceHistory = new Map(); // SN -> { id, addedAt }
    this.deviceBlocklist = new Set(); // 存储被删除设备的SN
    this.availableIds = new Set(); // 存储可复用的ID
    this.maxUsedId = 0; // 追踪最大使用过的ID
    this.deviceGroups = []; // 设备组列表
  }

  /**
   * 初始化控制器
   * @protected
   * @returns {Promise<void>}
   */
  async _initializeController() {
    try {
      // 加载设备历史记录和黑名单
      await this._loadDeviceHistory();
      await this._loadDeviceBlocklist();

      // 加载设备组
      await this._loadDeviceGroups();

      // 监听WebSocket服务的设备事件
      this._setupEventListeners();

      logger.info('DeviceController: 初始化完成');
    } catch (error) {
      this.handleError('_initializeController', error);
      throw error;
    }
  }

  /**
   * 设备事件类型
   * @readonly
   * @enum {string}
   */
  static get EVENTS() {
    return {
      DEVICE_CONNECTED: 'deviceConnected',
      DEVICE_DISCONNECTED: 'deviceDisconnected',
      DEVICE_STATUS_UPDATE: 'deviceStatusUpdate',
      DEVICE_ADDED: 'deviceAdded',
      DEVICE_DELETED: 'deviceDeleted',

      // 设备组相关事件
      DEVICE_GROUP_CREATED: 'deviceGroupCreated',
      DEVICE_GROUP_UPDATED: 'deviceGroupUpdated',
      DEVICE_GROUP_DELETED: 'deviceGroupDeleted',
      DEVICE_ADDED_TO_GROUP: 'deviceAddedToGroup',
      DEVICE_REMOVED_FROM_GROUP: 'deviceRemovedFromGroup',

      // 音量相关事件
      VOLUME_RESPONSE: 'volumeResponse',
      SET_VOLUME_RESPONSE: 'setVolumeResponse',

      // 屏幕控制相关事件
      SCREEN_ON_RESPONSE: 'screenOnResponse',
      SCREEN_OFF_RESPONSE: 'screenOffResponse',

      // 定位相关事件
      LOCATE_START_RESPONSE: 'locateStartResponse',
      LOCATE_STOP_RESPONSE: 'locateStopResponse',

      // 视野控制相关事件
      RESET_VIEW_RESPONSE: 'resetViewResponse',

      // 应用播控相关事件
      APP_CONTROL_RESPONSE: 'appControlResponse',

      // 快捷入口相关事件
      QUICK_ACCESS_RESPONSE: 'quickAccessResponse',

      // 配置响应事件
      CONFIG_RESPONSE: 'configResponse'
    };
  }

  /**
   * 设置事件监听器
   * @private
   */
  _setupEventListeners() {
    // 直接监听WebSocket服务的设备连接事件
    wsService.on(wsService.constructor.EVENTS.DEVICE_CONNECTED, this._handleDeviceConnected.bind(this));

    // 直接监听WebSocket服务的设备断开连接事件
    wsService.on(wsService.constructor.EVENTS.DEVICE_DISCONNECTED, this._handleDeviceDisconnected.bind(this));

    // 直接监听WebSocket服务的设备状态更新事件
    wsService.on(wsService.constructor.EVENTS.DEVICE_STATUS_UPDATE, this._handleDeviceStatusUpdate.bind(this));

    // 监听网络控制器的网络错误事件
    networkController.on(networkController.constructor.EVENTS.NETWORK_ERROR, this._handleNetworkError.bind(this));

    // 监听音量响应事件
    wsService.on(wsService.constructor.EVENTS.VOLUME_RESPONSE, this._handleVolumeResponse.bind(this));

    // 监听设置音量响应事件
    wsService.on(wsService.constructor.EVENTS.SET_VOLUME_RESPONSE, this._handleSetVolumeResponse.bind(this));

    // 监听点亮屏幕响应事件
    wsService.on(wsService.constructor.EVENTS.SCREEN_ON_RESPONSE, this._handleScreenOnResponse.bind(this));

    // 监听关闭屏幕响应事件
    wsService.on(wsService.constructor.EVENTS.SCREEN_OFF_RESPONSE, this._handleScreenOffResponse.bind(this));

    // 监听开启定位响应事件
    wsService.on(wsService.constructor.EVENTS.LOCATE_START_RESPONSE, this._handleLocateStartResponse.bind(this));

    // 监听停止定位响应事件
    wsService.on(wsService.constructor.EVENTS.LOCATE_STOP_RESPONSE, this._handleLocateStopResponse.bind(this));

    // 监听重置视野响应事件
    wsService.on(wsService.constructor.EVENTS.RESET_VIEW_RESPONSE, this._handleResetViewResponse.bind(this));

    // 监听应用播控响应事件
    wsService.on(wsService.constructor.EVENTS.APP_CONTROL_RESPONSE, this._handleAppControlResponse.bind(this));

    // 监听快捷入口响应事件
    wsService.on(wsService.constructor.EVENTS.QUICK_ACCESS_RESPONSE, this._handleQuickAccessResponse.bind(this));

    // 监听配置响应事件
    wsService.on(wsService.constructor.EVENTS.CONFIG_RESPONSE, this._handleConfigResponse.bind(this));

    logger.debug('DeviceController: 事件监听器设置完成');
  }

  /**
   * 处理设备连接事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleDeviceConnected(data) {
    try {
      const { ip, status } = data;
      const sn = status.sn;

      // 检查设备是否已添加
      const isAdded = this.isDeviceAdded(sn);
      const isBlocked = this.isBlocked(sn);

      if (isBlocked) {
        logger.info(`已删除的设备连接: ${sn} (${ip})`);
      } else if (isAdded) {
        logger.info(`已添加设备连接: ${sn} (${ip})`);
      } else {
        logger.info(`新设备连接，未添加到系统: ${sn} (${ip})`);
      }

      // 获取设备历史数据
      const historyData = this.deviceHistory.get(sn);

      // 如果设备已添加且未被删除，将设备ID添加到状态对象中
      const updatedStatus = { ...status };
      if (historyData) {
        updatedStatus.deviceId = historyData.id;
      }

      // 创建设备信息对象
      const deviceInfo = {
        ip,
        sn,
        id: historyData?.id,
        isAdded,
        isBlocked,
        status: updatedStatus
      };

      // 发出设备连接业务事件
      this.emit(DeviceController.EVENTS.DEVICE_CONNECTED, deviceInfo);

      // 如果设备已添加且未被删除，发送控制命令设置设备ID
      if (isAdded && historyData) {
        this._sendDeviceIdCommand(sn, historyData.id);
      }
    } catch (error) {
      this.handleError('_handleDeviceConnected', error);
    }
  }

  /**
   * 处理设备断开连接事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleDeviceDisconnected(data) {
    try {
      const { ip, sn } = data;
      logger.info(`设备断开连接: ${sn} (${ip})`);

      // 发出设备断开连接业务事件
      this.emit(DeviceController.EVENTS.DEVICE_DISCONNECTED, { ip, sn });
    } catch (error) {
      this.handleError('_handleDeviceDisconnected', error);
    }
  }

  /**
   * 处理设备状态更新事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleDeviceStatusUpdate(data) {
    try {
      const { ip, status } = data;
      const sn = status.sn;

      // 获取设备历史数据
      const historyData = this.deviceHistory.get(sn);

      // 创建设备状态信息对象
      const deviceStatusInfo = {
        ip,
        sn,
        id: historyData?.id,
        status: {
          ...status,
          deviceId: historyData?.id
        }
      };

      // 发出设备状态更新业务事件
      this.emit(DeviceController.EVENTS.DEVICE_STATUS_UPDATE, deviceStatusInfo);

      logger.debug(`设备状态更新: ${sn} (${ip})`, status);
    } catch (error) {
      this.handleError('_handleDeviceStatusUpdate', error);
    }
  }

  /**
   * 发送设备ID命令
   * @private
   * @param {string} sn - 设备序列号
   * @param {number} deviceId - 设备ID
   */
  async _sendDeviceIdCommand(sn, deviceId) {
    try {
      // 检查设备是否在线
      if (!this._isDeviceOnline(sn)) {
        logger.warn(`设备不在线，无法发送ID命令: ${sn}`);
        return;
      }

      // 从设置中读取受控模式的值，不使用缓存，确保获取最新的值
      const settings = await configService.readConfig('settings', false, true);
      const isControlled = settings.controlledMode ?? false;

      // 创建控制命令
      const command = {
        type: CommandType.CONTROL,
        data: {
          controlled: isControlled, // 根据设置决定是否为受控模式
          id: deviceId
        }
      };

      // 发送命令
      await this.sendToDevice(sn, command);
      logger.debug(`发送设备ID命令成功: ${sn}, ID: ${deviceId}, controlled: ${isControlled}`);
    } catch (error) {
      // 提供更详细的错误信息
      const errorMessage = error.message || '未知错误';
      const errorStack = error.stack || '';
      logger.error(`发送设备ID命令失败: ${sn}, 错误: ${errorMessage}`, {
        error,
        stack: errorStack,
        deviceId,
        isOnline: this._isDeviceOnline(sn),
        deviceIP: this.getDeviceIP(sn)
      });
    }
  }

  /**
   * 处理网络错误事件
   * @private
   * @param {Object} data - 错误数据
   */
  _handleNetworkError(data) {
    try {
      const { type, message, details } = data;
      logger.error(`网络错误: ${type}`, { message, details });

      // 这里可以添加特定的错误处理逻辑，例如尝试重新连接、通知用户等
    } catch (error) {
      this.handleError('_handleNetworkError', error);
    }
  }

  /**
   * 处理音量响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleVolumeResponse(data) {
    try {
      const { sn, volume } = data;
      logger.info(`收到设备 ${sn} 的音量响应: ${volume}`);

      // 发出音量响应事件
      this.emit(DeviceController.EVENTS.VOLUME_RESPONSE, { sn, volume });
    } catch (error) {
      this.handleError('_handleVolumeResponse', error);
    }
  }

  /**
   * 处理设置音量响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleSetVolumeResponse(data) {
    try {
      const { sn, code } = data;
      const success = code === 0;
      logger.info(`收到设备 ${sn} 的设置音量响应: ${success ? '成功' : '失败'}`);

      // 发出设置音量响应事件
      this.emit(DeviceController.EVENTS.SET_VOLUME_RESPONSE, { sn, success, code });
    } catch (error) {
      this.handleError('_handleSetVolumeResponse', error);
    }
  }

  /**
   * 处理点亮屏幕响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleScreenOnResponse(data) {
    try {
      const { sn, code } = data;
      const success = code === 0;
      logger.info(`收到设备 ${sn} 的点亮屏幕响应: ${success ? '成功' : '失败'}`);

      // 发出点亮屏幕响应事件
      this.emit(DeviceController.EVENTS.SCREEN_ON_RESPONSE, { sn, success, code });
    } catch (error) {
      this.handleError('_handleScreenOnResponse', error);
    }
  }

  /**
   * 处理关闭屏幕响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleScreenOffResponse(data) {
    try {
      const { sn, code } = data;
      const success = code === 0;
      logger.info(`收到设备 ${sn} 的关闭屏幕响应: ${success ? '成功' : '失败'}`);

      // 发出关闭屏幕响应事件
      this.emit(DeviceController.EVENTS.SCREEN_OFF_RESPONSE, { sn, success, code });
    } catch (error) {
      this.handleError('_handleScreenOffResponse', error);
    }
  }

  /**
   * 处理开启定位响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleLocateStartResponse(data) {
    try {
      const { sn, code } = data;
      const success = code === 0;
      logger.info(`收到设备 ${sn} 的开启定位响应: ${success ? '成功' : '失败'}`);

      // 发出开启定位响应事件
      this.emit(DeviceController.EVENTS.LOCATE_START_RESPONSE, { sn, success, code });
    } catch (error) {
      this.handleError('_handleLocateStartResponse', error);
    }
  }

  /**
   * 处理停止定位响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleLocateStopResponse(data) {
    try {
      const { sn, code } = data;
      const success = code === 0;
      logger.info(`收到设备 ${sn} 的停止定位响应: ${success ? '成功' : '失败'}`);

      // 发出停止定位响应事件
      this.emit(DeviceController.EVENTS.LOCATE_STOP_RESPONSE, { sn, success, code });
    } catch (error) {
      this.handleError('_handleLocateStopResponse', error);
    }
  }

  /**
   * 处理重置视野响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleResetViewResponse(data) {
    try {
      const { sn, code } = data;
      const success = code === 0;
      logger.info(`收到设备 ${sn} 的重置视野响应: ${success ? '成功' : '失败'}`);

      // 发出重置视野响应事件
      this.emit(DeviceController.EVENTS.RESET_VIEW_RESPONSE, { sn, success, code });
    } catch (error) {
      this.handleError('_handleResetViewResponse', error);
    }
  }

  /**
   * 处理应用播控响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleAppControlResponse(data) {
    try {
      const { sn, code, pkg } = data;
      const success = code === 0;
      logger.info(`收到设备 ${sn} 的应用播控响应: ${success ? '成功' : '失败'}, 包名: ${pkg || '未知'}`);

      // 发出应用播控响应事件
      this.emit(DeviceController.EVENTS.APP_CONTROL_RESPONSE, { sn, success, code, pkg });
    } catch (error) {
      this.handleError('_handleAppControlResponse', error);
    }
  }

  /**
   * 处理快捷入口响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleQuickAccessResponse(data) {
    try {
      const { sn, code } = data;
      const success = code === 0;
      logger.info(`收到设备 ${sn} 的快捷入口响应: ${success ? '成功' : '失败'}`);

      // 发出快捷入口响应事件
      this.emit(DeviceController.EVENTS.QUICK_ACCESS_RESPONSE, { sn, success, code });
    } catch (error) {
      this.handleError('_handleQuickAccessResponse', error);
    }
  }

  /**
   * 处理配置响应事件
   * @private
   * @param {Object} data - 事件数据
   */
  _handleConfigResponse(data) {
    try {
      const { sn, config } = data;
      logger.info(`收到设备 ${sn} 的配置响应`);

      // 发出配置响应事件
      this.emit(DeviceController.EVENTS.CONFIG_RESPONSE, { sn, config });
    } catch (error) {
      this.handleError('_handleConfigResponse', error);
    }
  }

  /**
   * 加载设备历史记录
   * @private
   * @returns {Promise<void>}
   */
  async _loadDeviceHistory() {
    try {
      const data = await fs.readFile(paths.devices.history, 'utf8');
      const history = JSON.parse(data);
      this.deviceHistory = new Map(history.map(item => [item.sn, {
        id: item.id,
        addedAt: item.addedAt
      }]));

      // 更新最大ID
      this.maxUsedId = Math.max(...history.map(item => item.id), 0);

      // 生成可用ID列表（1到最大ID之间的空闲ID）
      const usedIds = new Set(history.map(item => item.id));
      this.availableIds = new Set(
        [...Array(this.maxUsedId).keys()]
          .map(i => i + 1)
          .filter(id => !usedIds.has(id))
      );

      logger.debug(`加载设备历史记录成功，共 ${history.length} 条记录`);
    } catch (error) {
      logger.warn('加载设备历史记录失败，使用空记录', error);
      this.deviceHistory = new Map();
      this.maxUsedId = 0;
      this.availableIds = new Set();
    }
  }

  /**
   * 保存设备历史记录
   * @private
   * @returns {Promise<void>}
   */
  async _saveDeviceHistory() {
    try {
      const historyArray = Array.from(this.deviceHistory.entries()).map(([sn, data]) => ({
        sn,
        id: data.id,
        addedAt: data.addedAt
      }));
      await fs.writeFile(
        paths.devices.history,
        JSON.stringify(historyArray, null, 2),
        'utf8'
      );
      logger.debug(`保存设备历史记录成功，共 ${historyArray.length} 条记录`);
    } catch (error) {
      this.handleError('_saveDeviceHistory', error);
    }
  }

  /**
   * 加载设备黑名单
   * @private
   * @returns {Promise<void>}
   */
  async _loadDeviceBlocklist() {
    try {
      const data = await fs.readFile(paths.devices.blocklist, 'utf8');
      const blocklist = JSON.parse(data);
      this.deviceBlocklist = new Set(blocklist);
      logger.debug(`加载设备黑名单成功，共 ${blocklist.length} 条记录`);
    } catch (error) {
      logger.warn('加载设备黑名单失败，使用空记录', error);
      this.deviceBlocklist = new Set();
    }
  }

  /**
   * 保存设备黑名单
   * @private
   * @returns {Promise<void>}
   */
  async _saveDeviceBlocklist() {
    try {
      const blocklistArray = Array.from(this.deviceBlocklist);
      await fs.writeFile(
        paths.devices.blocklist,
        JSON.stringify(blocklistArray, null, 2),
        'utf8'
      );
      logger.debug(`保存设备黑名单成功，共 ${blocklistArray.length} 条记录`);
    } catch (error) {
      this.handleError('_saveDeviceBlocklist', error);
    }
  }

  /**
   * 添加设备到黑名单
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否成功添加
   */
  async addToBlocklist(sn) {
    try {
      this._checkInitialized();

      this.deviceBlocklist.add(sn);
      await this._saveDeviceBlocklist();
      logger.info(`添加设备到黑名单: ${sn}`);
      return true;
    } catch (error) {
      this.handleError('addToBlocklist', error);
      return false;
    }
  }

  /**
   * 从黑名单中移除设备
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否成功移除
   */
  async removeFromBlocklist(sn) {
    try {
      this._checkInitialized();

      if (!this.deviceBlocklist.has(sn)) {
        logger.warn(`设备不在黑名单中: ${sn}`);
        return false;
      }

      this.deviceBlocklist.delete(sn);
      await this._saveDeviceBlocklist();
      logger.info(`从黑名单中移除设备: ${sn}`);
      return true;
    } catch (error) {
      this.handleError('removeFromBlocklist', error);
      return false;
    }
  }

  /**
   * 检查设备是否已被删除
   * @param {string} sn - 设备序列号
   * @returns {boolean} 是否已被删除（在blocklist中）
   */
  isBlocked(sn) {
    return this.deviceBlocklist.has(sn);
  }

  /**
   * 添加设备
   * @param {string} sn - 设备序列号
   * @returns {Promise<Object>} 添加的设备信息
   */
  async addDevice(sn) {
    try {
      this._checkInitialized();

      if (this.deviceHistory.has(sn)) {
        logger.warn(`设备已存在: ${sn}`);
        throw createValidationError('设备已存在');
      }

      // 分配ID：优先使用回收的ID，如果没有可用ID则递增
      let newId;
      if (this.availableIds.size > 0) {
        newId = Math.min(...this.availableIds);
        this.availableIds.delete(newId);
      } else {
        newId = this.maxUsedId + 1;
        this.maxUsedId = newId;
      }

      const deviceData = {
        id: newId,
        addedAt: Date.now()
      };

      // 添加到历史记录
      this.deviceHistory.set(sn, deviceData);
      await this._saveDeviceHistory();

      // 从黑名单中移除
      if (this.deviceBlocklist.has(sn)) {
        this.deviceBlocklist.delete(sn);
        await this._saveDeviceBlocklist();
      }

      // 创建设备信息对象
      const deviceInfo = {
        sn,
        id: newId,
        isOnline: this._isDeviceOnline(sn),
        status: this._getDeviceStatus(sn),
        addedAt: deviceData.addedAt
      };

      // 发出设备添加事件
      this.emit(DeviceController.EVENTS.DEVICE_ADDED, deviceInfo);

      logger.info(`添加设备成功: ${sn}, ID: ${newId}`);

      // 如果设备在线，发送设备ID命令
      if (this._isDeviceOnline(sn)) {
        await this._sendDeviceIdCommand(sn, newId);
      }

      // 返回设备信息
      return deviceInfo;
    } catch (error) {
      logger.error(`添加设备失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 更新设备ID
   * @param {string} sn - 设备序列号
   * @param {string|number} id - 新的设备ID
   * @param {boolean} [forceSwap=false] - 是否强制交换ID（当ID已被其他设备使用时）
   * @returns {Promise<Object>} 更新后的设备信息
   */
  async updateDeviceId(sn, id, forceSwap = false) {
    try {
      this._checkInitialized();

      // 验证设备是否存在
      if (!this.deviceHistory.has(sn)) {
        logger.warn(`设备不存在: ${sn}`);
        throw createNotFoundError('设备不存在');
      }

      // 确保ID是数字
      const newId = parseInt(id, 10);
      if (isNaN(newId) || newId <= 0) {
        throw createValidationError('设备ID必须是正整数');
      }

      // 获取当前设备数据
      const deviceData = this.deviceHistory.get(sn);
      const oldId = deviceData.id;

      // 如果ID没有变化，直接返回
      if (oldId === newId) {
        logger.debug(`设备ID未变化: ${sn}, ID: ${newId}`);
        return {
          sn,
          id: newId,
          isOnline: this._isDeviceOnline(sn),
          status: this._getDeviceStatus(sn),
          addedAt: deviceData.addedAt
        };
      }

      // 检查新ID是否已被其他设备使用
      let conflictSn = null;
      let conflictData = null;

      for (const [otherSn, otherData] of this.deviceHistory.entries()) {
        if (otherSn !== sn && otherData.id === newId) {
          conflictSn = otherSn;
          conflictData = otherData;
          break;
        }
      }

      // 如果有冲突且不允许强制交换，则抛出错误
      if (conflictSn && !forceSwap) {
        logger.warn(`设备ID已被使用: ${newId}, 设备: ${conflictSn}`);
        throw createValidationError(`设备ID ${newId} 已被设备 ${conflictSn} 使用`);
      }

      // 如果有冲突且允许强制交换，则交换两个设备的ID
      if (conflictSn && forceSwap) {
        logger.info(`强制交换设备ID: ${sn}(${oldId}) <-> ${conflictSn}(${newId})`);

        // 交换ID
        conflictData.id = oldId;
        deviceData.id = newId;

        // 更新设备历史记录
        this.deviceHistory.set(conflictSn, conflictData);
        this.deviceHistory.set(sn, deviceData);

        // 保存设备历史记录
        await this._saveDeviceHistory();

        // 如果冲突设备在线，发送设备ID命令
        if (this._isDeviceOnline(conflictSn)) {
          await this._sendDeviceIdCommand(conflictSn, oldId);
        }

        // 如果当前设备在线，发送设备ID命令
        if (this._isDeviceOnline(sn)) {
          await this._sendDeviceIdCommand(sn, newId);
        }

        // 创建设备信息对象
        const deviceInfo = {
          sn,
          id: newId,
          isOnline: this._isDeviceOnline(sn),
          status: this._getDeviceStatus(sn),
          addedAt: deviceData.addedAt,
          swappedWith: {
            sn: conflictSn,
            id: oldId
          }
        };

        logger.info(`交换设备ID成功: ${sn}(${oldId} -> ${newId}) <-> ${conflictSn}(${newId} -> ${oldId})`);

        // 返回设备信息
        return deviceInfo;
      }

      // 没有冲突，正常更新设备ID
      deviceData.id = newId;
      this.deviceHistory.set(sn, deviceData);

      // 更新可用ID列表
      this.availableIds.add(oldId);
      this.availableIds.delete(newId);

      // 更新最大ID
      if (newId > this.maxUsedId) {
        this.maxUsedId = newId;
      }

      // 保存设备历史记录
      await this._saveDeviceHistory();

      // 如果设备在线，发送设备ID命令
      if (this._isDeviceOnline(sn)) {
        await this._sendDeviceIdCommand(sn, newId);
      }

      // 创建设备信息对象
      const deviceInfo = {
        sn,
        id: newId,
        isOnline: this._isDeviceOnline(sn),
        status: this._getDeviceStatus(sn),
        addedAt: deviceData.addedAt
      };

      logger.info(`更新设备ID成功: ${sn}, 旧ID: ${oldId}, 新ID: ${newId}`);

      // 返回设备信息
      return deviceInfo;
    } catch (error) {
      logger.error(`更新设备ID失败: ${sn}, ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除设备
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否成功删除
   */
  async deleteDevice(sn) {
    try {
      this._checkInitialized();

      const deviceData = this.deviceHistory.get(sn);
      if (!deviceData) {
        logger.warn(`设备不存在: ${sn}`);
        throw createNotFoundError('设备不存在');
      }

      // 创建设备信息对象（用于事件）
      const deviceInfo = {
        sn,
        id: deviceData.id,
        addedAt: deviceData.addedAt
      };

      // 回收ID
      this.availableIds.add(deviceData.id);

      // 添加到黑名单
      this.deviceBlocklist.add(sn);
      await this._saveDeviceBlocklist();

      // 从所有设备组中移除该设备
      let groupsUpdated = false;
      for (let i = 0; i < this.deviceGroups.length; i++) {
        const group = this.deviceGroups[i];
        const deviceIndex = group.devices.indexOf(sn);
        if (deviceIndex !== -1) {
          // 从组中移除设备
          group.devices.splice(deviceIndex, 1);
          group.updatedAt = Date.now();
          this.deviceGroups[i] = group;
          groupsUpdated = true;

          // 发出设备从组中移除事件
          this.emit(DeviceController.EVENTS.DEVICE_REMOVED_FROM_GROUP, {
            groupId: group.id,
            sn,
            group: { ...group }
          });

          logger.info(`从组 ${group.id} 中移除已删除的设备: ${sn}`);
        }
      }

      // 如果有组被更新，保存设备组
      if (groupsUpdated) {
        await this._saveDeviceGroups();
      }

      // 从历史记录中删除
      this.deviceHistory.delete(sn);
      await this._saveDeviceHistory();

      // 发出设备删除事件
      this.emit(DeviceController.EVENTS.DEVICE_DELETED, deviceInfo);

      logger.info(`删除设备成功: ${sn}`);
      return true;
    } catch (error) {
      logger.error(`删除设备失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 获取设备历史记录
   * @param {boolean} [includeDeleted=false] - 是否包含已删除的设备
   * @returns {Array<Object>} 设备历史记录
   */
  getDeviceHistory(includeDeleted = false) {
    try {
      this._checkInitialized();

      let entries = Array.from(this.deviceHistory.entries());

      // 如果不包含已删除的设备，则过滤掉
      if (!includeDeleted) {
        entries = entries.filter(([sn]) => !this.isBlocked(sn));
      }

      return entries.map(([sn, data]) => {
        const isOnline = this._isDeviceOnline(sn);
        const deviceStatus = this._getDeviceStatus(sn);
        const isBlocked = this.isBlocked(sn);

        return {
          sn,
          id: data.id,
          isOnline,
          isBlocked,
          status: {
            ...deviceStatus,
            deviceId: data.id
          },
          addedAt: data.addedAt
        };
      });
    } catch (error) {
      logger.error('获取设备历史记录失败', error);
      return [];
    }
  }

  /**
   * 获取所有设备
   * @returns {Array<Object>} 所有设备
   */
  getAllDevices() {
    try {
      this._checkInitialized();

      return Array.from(this.deviceHistory.entries()).map(([sn, data]) => {
        const isOnline = this._isDeviceOnline(sn);
        return {
          sn,
          id: data.id,
          isBlocked: this.isBlocked(sn),
          isOnline,
          status: this._getDeviceStatus(sn),
          addedAt: data.addedAt
        };
      });
    } catch (error) {
      logger.error('获取所有设备失败', error);
      return [];
    }
  }

  /**
   * 获取当前连接的设备
   * @returns {Array<Object>} 连接的设备
   */
  getConnectedDevices() {
    try {
      this._checkInitialized();

      // 直接从WebSocket服务获取连接的设备
      const connectedDevices = wsService.getConnectedDevices();

      // 格式化设备信息，不过滤掉已删除的设备
      return connectedDevices.map(device => {
        const sn = device.status.sn;
        const historyData = this.deviceHistory.get(sn);
        const isBlocked = this.isBlocked(sn);

        return {
          ip: device.ip,
          sn,
          id: historyData?.id,
          isAdded: !!historyData && !isBlocked,
          isBlocked, // 标记设备是否已删除
          status: {
            ...device.status,
            deviceId: historyData?.id
          }
        };
      });
    } catch (error) {
      logger.error('获取连接设备失败', error);
      return [];
    }
  }

  /**
   * 检查设备是否在线
   * @param {string} sn - 设备序列号
   * @returns {boolean} 是否在线
   */
  isDeviceOnline(sn) {
    return this._isDeviceOnline(sn);
  }

  /**
   * 内部方法：检查设备是否在线
   * @private
   * @param {string} sn - 设备序列号
   * @returns {boolean} 是否在线
   */
  _isDeviceOnline(sn) {
    return wsService.isDeviceOnline(sn);
  }

  /**
   * 检查设备是否已添加
   * @param {string} sn - 设备序列号
   * @returns {boolean} 是否已添加（在历史记录中且未被删除）
   */
  isDeviceAdded(sn) {
    return this.deviceHistory.has(sn) && !this.isBlocked(sn);
  }

  /**
   * 获取设备IP
   * @param {string} sn - 设备序列号
   * @returns {string|undefined} 设备IP
   */
  getDeviceIP(sn) {
    return wsService.getDeviceIP(sn);
  }

  /**
   * 获取设备状态
   * @private
   * @param {string} sn - 设备序列号
   * @returns {Object} 设备状态
   */
  _getDeviceStatus(sn) {
    // 直接从WebSocket服务获取设备状态
    const status = wsService.getDeviceStatus(sn);
    if (status) {
      return status;
    }

    // 设备离线状态
    return {
      sn,
      deviceStatus: DEVICE_STATUS.OFFLINE,
      battery: '-',
      onuse: 0,
      playStatus: '-',
      controled: 0,
      timeStamp: Date.now()
    };
  }

  /**
   * 通过设备序列号发送消息
   * @param {string} sn - 设备序列号
   * @param {Object} message - 消息对象
   * @returns {Promise<boolean>} 是否成功
   */
  async sendToDevice(sn, message) {
    try {
      this._checkInitialized();

      if (!sn) {
        const error = new Error('无效的设备序列号');
        error.code = 'INVALID_DEVICE_SN';
        throw error;
      }

      const ip = this.getDeviceIP(sn);
      if (!ip) {
        const error = new Error(`设备不在线: ${sn}`);
        error.code = 'DEVICE_OFFLINE';
        throw error;
      }

      return await networkController.sendToClient(ip, message);
    } catch (error) {
      // 提供更详细的错误信息
      const errorMessage = error.message || '未知错误';
      const errorStack = error.stack || '';
      logger.error(`发送消息到设备失败: ${sn}, 错误: ${errorMessage}`, {
        error,
        stack: errorStack,
        message: JSON.stringify(message),
        deviceIP: this.getDeviceIP(sn),
        isOnline: this.isDeviceOnline(sn)
      });

      this.handleError('sendToDevice', error);
      throw error;
    }
  }

  /**
   * 发送命令到设备
   * @param {string} sn - 设备序列号
   * @param {Object} command - 命令对象
   * @returns {Promise<boolean>} 是否发送成功
   */
  async sendCommand(sn, command) {
    try {
      this._checkInitialized();

      // 检查设备是否已添加
      if (!this.isDeviceAdded(sn)) {
        const error = new Error(`设备未添加: ${sn}`);
        error.code = 'DEVICE_NOT_ADDED';
        throw error;
      }

      // 使用sendToDevice方法发送命令
      return await this.sendToDevice(sn, command);
    } catch (error) {
      logger.error(`发送命令失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 播放资源
   * @param {string} sn - 设备序列号
   * @param {number} index - 资源索引
   * @param {boolean} [loopPlay=false] - 是否循环播放
   * @returns {Promise<boolean>} 是否发送成功
   */
  async playResource(sn, index, loopPlay = false) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.PLAY,
        data: {
          index,
          loopPlay: !!loopPlay
        }
      };
      logger.debug(`发送播放资源命令: ${sn}, 索引: ${index}, 循环播放: ${loopPlay}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`播放资源失败: ${sn}, 索引: ${index}`, error);
      throw error;
    }
  }

  /**
   * 停止播放
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否发送成功
   */
  async stopPlay(sn) {
    try {
      this._checkInitialized();

      // 使用 STOP 命令类型 (2003)，确保设备能正确识别停止命令
      const command = {
        type: CommandType.STOP,
        data: { id: Date.now() }
      };

      logger.debug(`发送停止播放命令: ${sn}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`停止播放失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 重启设备
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否发送成功
   */
  async rebootDevice(sn) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.RESTART,
        data: { id: Date.now() }
      };

      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`重启设备失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 关闭设备
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否发送成功
   */
  async shutdownDevice(sn) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.SHUTDOWN,
        data: { id: Date.now() }
      };

      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`关闭设备失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 获取设备音量
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否发送成功
   */
  async getDeviceVolume(sn) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.GET_VOLUME,
        data: { id: Date.now() }
      };

      logger.debug(`发送获取音量命令: ${sn}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`获取设备音量失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 设置设备音量
   * @param {string} sn - 设备序列号
   * @param {number} volume - 音量值（0-100）
   * @returns {Promise<boolean>} 是否发送成功
   */
  async setDeviceVolume(sn, volume) {
    try {
      this._checkInitialized();

      // 确保音量在有效范围内
      const validVolume = Math.max(0, Math.min(100, volume));

      const command = {
        type: CommandType.SET_VOLUME,
        data: {
          volume: validVolume,
          id: Date.now()
        }
      };

      logger.debug(`发送设置音量命令: ${sn}, 音量: ${validVolume}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`设置设备音量失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 点亮设备屏幕
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否发送成功
   */
  async turnScreenOn(sn) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.SCREEN_ON,
        data: { id: Date.now() }
      };

      logger.debug(`发送点亮屏幕命令: ${sn}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`点亮设备屏幕失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 关闭设备屏幕
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否发送成功
   */
  async turnScreenOff(sn) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.SCREEN_OFF,
        data: { id: Date.now() }
      };

      logger.debug(`发送关闭屏幕命令: ${sn}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`关闭设备屏幕失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 快捷入口
   * @param {string} sn - 设备序列号
   * @param {boolean} [isShow=true] - 是否显示
   * @returns {Promise<boolean>} 是否发送成功
   */
  async quickAccess(sn, isShow) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.QUICK_ACCESS,
        data: {
          show: isShow,
          id: Date.now()
        }
      };

      logger.debug(`发送设置快捷入口命令: ${sn}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`设置快捷入口失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 开启设备定位
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否发送成功
   */
  async startLocateDevice(sn) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.LOCATE_START,
        data: { id: Date.now() }
      };

      logger.debug(`发送开启定位命令: ${sn}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`开启设备定位失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 停止设备定位
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否发送成功
   */
  async stopLocateDevice(sn) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.LOCATE_STOP,
        data: { id: Date.now() }
      };

      logger.debug(`发送停止定位命令: ${sn}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`停止设备定位失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 重置设备视野
   * @param {string} sn - 设备序列号
   * @returns {Promise<boolean>} 是否发送成功
   */
  async resetDeviceView(sn) {
    try {
      this._checkInitialized();

      const command = {
        type: CommandType.RESET_VIEW,
        data: { id: Date.now() }
      };

      logger.debug(`发送重置视野命令: ${sn}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`重置设备视野失败: ${sn}`, error);
      throw error;
    }
  }

  /**
   * 控制应用
   * @param {string} sn - 设备序列号
   * @param {string} packageName - 应用包名
   * @param {boolean} open - 是否打开应用，true表示打开，false表示关闭
   * @param {number} resourceIndex - 资源索引
   * @returns {Promise<boolean>} 是否发送成功
   */
  async controlApp(sn, packageName, open, resourceIndex) {
    try {
      this._checkInitialized();

      if (!packageName) {
        throw new Error('应用包名不能为空');
      }

      const command = {
        type: CommandType.APP_CONTROL,
        data: {
          index: resourceIndex,  // 资源索引
          pkg: packageName,      // 应用包名
          open: !!open,          // 确保是布尔值
          id: Date.now()
        }
      };

      logger.debug(`发送应用播控命令: ${sn}, 包名: ${packageName}, 操作: ${open ? '打开' : '关闭'}, 资源索引: ${resourceIndex}`);
      return await this.sendCommand(sn, command);
    } catch (error) {
      logger.error(`控制应用失败: ${sn}, 包名: ${packageName}`, error);
      throw error;
    }
  }

  /**
   * 批量操作设备
   * @param {Array<string>} deviceList - 设备序列号列表
   * @param {Function} operation - 操作函数
   * @returns {Promise<Object>} 操作结果
   */
  async batchOperation(deviceList, operation) {
    try {
      this._checkInitialized();

      if (!deviceList || !Array.isArray(deviceList) || deviceList.length === 0) {
        throw createValidationError('无效的设备列表');
      }

      // 确保设备列表中的每个元素都是字符串
      const cleanDeviceList = deviceList.map(sn => String(sn));

      const results = await Promise.allSettled(
        cleanDeviceList.map(sn => operation(sn))
      );

      // 处理结果
      const successful = [];
      const failed = [];

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          successful.push(cleanDeviceList[index]);
        } else {
          failed.push({
            sn: cleanDeviceList[index],
            error: result.reason?.message || '未知错误'
          });
        }
      });

      return { successful, failed };
    } catch (error) {
      logger.error('批量操作设备失败', error);
      throw error;
    }
  }

  /**
   * 批量播放资源
   * @param {Array<string>} deviceList - 设备序列号列表
   * @param {number} index - 资源索引
   * @param {boolean} [loopPlay=false] - 是否循环播放
   * @returns {Promise<Object>} 操作结果
   */
  async batchPlayResource(deviceList, index, loopPlay = false) {
    try {
      this._checkInitialized();

      return await this.batchOperation(
        deviceList,
        sn => this.playResource(sn, index, loopPlay)
      );
    } catch (error) {
      logger.error('批量播放资源失败', error);
      throw error;
    }
  }

  /**
   * 批量停止播放
   * @param {Array<string>} deviceList - 设备序列号列表
   * @returns {Promise<Object>} 操作结果
   */
  async batchStopPlay(deviceList) {
    try {
      this._checkInitialized();

      return await this.batchOperation(
        deviceList,
        sn => this.stopPlay(sn)
      );
    } catch (error) {
      logger.error('批量停止播放失败', error);
      throw error;
    }
  }

  /**
   * 批量控制应用
   * @param {Array<string>} deviceList - 设备序列号列表
   * @param {string} packageName - 应用包名
   * @param {boolean} open - 是否打开应用
   * @param {number} resourceIndex - 资源索引
   * @returns {Promise<Object>} 操作结果
   */
  async batchControlApp(deviceList, packageName, open, resourceIndex) {
    try {
      this._checkInitialized();

      if (!packageName) {
        throw new Error('应用包名不能为空');
      }

      return await this.batchOperation(
        deviceList,
        sn => this.controlApp(sn, packageName, open, resourceIndex)
      );
    } catch (error) {
      logger.error('批量控制应用失败', error);
      throw error;
    }
  }

  /**
   * 批量重启设备
   * @param {Array<string>} deviceList - 设备序列号列表
   * @returns {Promise<Object>} 操作结果
   */
  async batchRebootDevice(deviceList) {
    try {
      this._checkInitialized();

      return await this.batchOperation(
        deviceList,
        sn => this.rebootDevice(sn)
      );
    } catch (error) {
      logger.error('批量重启设备失败', error);
      throw error;
    }
  }

  /**
   * 批量关闭设备
   * @param {Array<string>} deviceList - 设备序列号列表
   * @returns {Promise<Object>} 操作结果
   */
  async batchShutdownDevice(deviceList) {
    try {
      this._checkInitialized();

      return await this.batchOperation(
        deviceList,
        sn => this.shutdownDevice(sn)
      );
    } catch (error) {
      logger.error('批量关闭设备失败', error);
      throw error;
    }
  }

  /**
   * 加载设备组
   * @private
   * @returns {Promise<void>}
   */
  async _loadDeviceGroups() {
    try {
      // 检查文件是否存在
      try {
        await fs.access(paths.devices.groups);
      } catch (error) {
        // 文件不存在，创建空文件
        await fs.writeFile(paths.devices.groups, JSON.stringify([]), 'utf8');
        this.deviceGroups = [];
        logger.debug('设备组文件不存在，已创建空文件');
        return;
      }

      // 读取设备组文件
      const data = await fs.readFile(paths.devices.groups, 'utf8');
      this.deviceGroups = JSON.parse(data);
      logger.debug(`加载设备组成功，共 ${this.deviceGroups.length} 个组`);
    } catch (error) {
      logger.warn('加载设备组失败，使用空列表', error);
      this.deviceGroups = [];
    }
  }

  /**
   * 保存设备组
   * @private
   * @returns {Promise<void>}
   */
  async _saveDeviceGroups() {
    try {
      await fs.writeFile(
        paths.devices.groups,
        JSON.stringify(this.deviceGroups, null, 2),
        'utf8'
      );
      logger.debug(`保存设备组成功，共 ${this.deviceGroups.length} 个组`);
    } catch (error) {
      this.handleError('_saveDeviceGroups', error);
    }
  }

  /**
   * 获取所有设备组
   * @returns {Array<Object>} 设备组列表
   */
  getDeviceGroups() {
    try {
      this._checkInitialized();
      return [...this.deviceGroups];
    } catch (error) {
      logger.error('获取设备组失败', error);
      return [];
    }
  }

  /**
   * 获取特定设备组
   * @param {string} groupId - 组ID
   * @returns {Object|null} 设备组对象，如果不存在则返回null
   */
  getDeviceGroup(groupId) {
    try {
      this._checkInitialized();
      const group = this.deviceGroups.find(group => group.id === groupId);
      return group ? { ...group } : null;
    } catch (error) {
      logger.error(`获取设备组失败: ${groupId}`, error);
      return null;
    }
  }

  /**
   * 创建设备组
   * @param {string} name - 组名称
   * @param {string} description - 组描述
   * @returns {Promise<Object>} 创建的设备组
   */
  async createDeviceGroup(name, description) {
    try {
      this._checkInitialized();

      if (!name) {
        throw createValidationError('组名称不能为空');
      }

      // 生成唯一ID
      const id = Date.now().toString(36) + Math.random().toString(36).substr(2, 5);

      const newGroup = {
        id,
        name,
        description: description || '',
        devices: [],
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      this.deviceGroups.push(newGroup);
      await this._saveDeviceGroups();

      // 发出设备组创建事件
      this.emit(DeviceController.EVENTS.DEVICE_GROUP_CREATED, { ...newGroup });

      logger.info(`创建设备组成功: ${name} (${id})`);
      return { ...newGroup };
    } catch (error) {
      logger.error(`创建设备组失败: ${name}`, error);
      throw error;
    }
  }

  /**
   * 更新设备组
   * @param {string} groupId - 组ID
   * @param {Object} data - 更新数据
   * @returns {Promise<Object>} 更新后的设备组
   */
  async updateDeviceGroup(groupId, data) {
    try {
      this._checkInitialized();

      const groupIndex = this.deviceGroups.findIndex(group => group.id === groupId);
      if (groupIndex === -1) {
        throw createNotFoundError('设备组不存在');
      }

      const group = this.deviceGroups[groupIndex];
      const updatedGroup = {
        ...group,
        name: data.name || group.name,
        description: data.description !== undefined ? data.description : group.description,
        updatedAt: Date.now()
      };

      this.deviceGroups[groupIndex] = updatedGroup;
      await this._saveDeviceGroups();

      // 发出设备组更新事件
      this.emit(DeviceController.EVENTS.DEVICE_GROUP_UPDATED, { ...updatedGroup });

      logger.info(`更新设备组成功: ${updatedGroup.name} (${groupId})`);
      return { ...updatedGroup };
    } catch (error) {
      logger.error(`更新设备组失败: ${groupId}`, error);
      throw error;
    }
  }

  /**
   * 删除设备组
   * @param {string} groupId - 组ID
   * @returns {Promise<boolean>} 是否成功删除
   */
  async deleteDeviceGroup(groupId) {
    try {
      this._checkInitialized();

      const groupIndex = this.deviceGroups.findIndex(group => group.id === groupId);
      if (groupIndex === -1) {
        throw createNotFoundError('设备组不存在');
      }

      const deletedGroup = this.deviceGroups[groupIndex];
      this.deviceGroups.splice(groupIndex, 1);
      await this._saveDeviceGroups();

      // 发出设备组删除事件
      this.emit(DeviceController.EVENTS.DEVICE_GROUP_DELETED, { ...deletedGroup });

      logger.info(`删除设备组成功: ${deletedGroup.name} (${groupId})`);
      return true;
    } catch (error) {
      logger.error(`删除设备组失败: ${groupId}`, error);
      throw error;
    }
  }

  /**
   * 将设备添加到组
   * @param {string} groupId - 组ID
   * @param {string} sn - 设备序列号
   * @returns {Promise<Object>} 更新后的设备组
   */
  async addDeviceToGroup(groupId, sn) {
    try {
      this._checkInitialized();

      // 检查设备是否已添加
      if (!this.isDeviceAdded(sn)) {
        throw createValidationError('设备未添加到系统');
      }

      const groupIndex = this.deviceGroups.findIndex(group => group.id === groupId);
      if (groupIndex === -1) {
        throw createNotFoundError('设备组不存在');
      }

      const group = this.deviceGroups[groupIndex];

      // 检查设备是否已在组中
      if (group.devices.includes(sn)) {
        logger.warn(`设备已在组中: ${sn} (${groupId})`);
        return { ...group };
      }

      // 检查设备是否在其他组中，如果是，则从其他组中移除
      let oldGroupId = null;
      for (let i = 0; i < this.deviceGroups.length; i++) {
        if (i !== groupIndex && this.deviceGroups[i].devices.includes(sn)) {
          oldGroupId = this.deviceGroups[i].id;
          const deviceIndex = this.deviceGroups[i].devices.indexOf(sn);
          this.deviceGroups[i].devices.splice(deviceIndex, 1);
          this.deviceGroups[i].updatedAt = Date.now();

          // 发出设备从组中移除事件
          this.emit(DeviceController.EVENTS.DEVICE_REMOVED_FROM_GROUP, {
            groupId: oldGroupId,
            sn,
            group: { ...this.deviceGroups[i] }
          });

          logger.info(`设备已从旧组中移除: ${sn} (${oldGroupId})`);
          break;
        }
      }

      // 添加设备到新组
      group.devices.push(sn);
      group.updatedAt = Date.now();

      this.deviceGroups[groupIndex] = group;
      await this._saveDeviceGroups();

      // 发出设备添加到组事件
      this.emit(DeviceController.EVENTS.DEVICE_ADDED_TO_GROUP, {
        groupId,
        sn,
        group: { ...group }
      });

      logger.info(`添加设备到组成功: ${sn} (${groupId})${oldGroupId ? `, 已从组 ${oldGroupId} 中移除` : ''}`);
      return { ...group };
    } catch (error) {
      logger.error(`添加设备到组失败: ${sn} (${groupId})`, error);
      throw error;
    }
  }

  /**
   * 从组中移除设备
   * @param {string} groupId - 组ID
   * @param {string} sn - 设备序列号
   * @returns {Promise<Object>} 更新后的设备组
   */
  async removeDeviceFromGroup(groupId, sn) {
    try {
      this._checkInitialized();

      const groupIndex = this.deviceGroups.findIndex(group => group.id === groupId);
      if (groupIndex === -1) {
        throw createNotFoundError('设备组不存在');
      }

      const group = this.deviceGroups[groupIndex];

      // 检查设备是否在组中
      const deviceIndex = group.devices.indexOf(sn);
      if (deviceIndex === -1) {
        logger.warn(`设备不在组中: ${sn} (${groupId})`);
        return { ...group };
      }

      // 从组中移除设备
      group.devices.splice(deviceIndex, 1);
      group.updatedAt = Date.now();

      this.deviceGroups[groupIndex] = group;
      await this._saveDeviceGroups();

      // 发出设备从组中移除事件
      this.emit(DeviceController.EVENTS.DEVICE_REMOVED_FROM_GROUP, {
        groupId,
        sn,
        group: { ...group }
      });

      logger.info(`从组中移除设备成功: ${sn} (${groupId})`);
      return { ...group };
    } catch (error) {
      logger.error(`从组中移除设备失败: ${sn} (${groupId})`, error);
      throw error;
    }
  }

  /**
   * 获取组内所有设备
   * @param {string} groupId - 组ID
   * @returns {Promise<Array<Object>>} 设备列表
   */
  async getDevicesByGroup(groupId) {
    try {
      this._checkInitialized();

      const group = this.deviceGroups.find(group => group.id === groupId);
      if (!group) {
        throw createNotFoundError('设备组不存在');
      }

      // 获取组内所有设备的详细信息
      const devices = [];
      for (const sn of group.devices) {
        // 检查设备是否仍然存在（未被删除）
        if (this.isDeviceAdded(sn)) {
          const historyData = this.deviceHistory.get(sn);
          const isOnline = this._isDeviceOnline(sn);

          devices.push({
            sn,
            id: historyData?.id,
            isOnline,
            status: this._getDeviceStatus(sn),
            addedAt: historyData?.addedAt
          });
        }
      }

      return devices;
    } catch (error) {
      logger.error(`获取组内设备失败: ${groupId}`, error);
      throw error;
    }
  }

  /**
   * 批量将设备添加到组
   * @param {string} groupId - 组ID
   * @param {Array<string>} deviceList - 设备序列号列表
   * @returns {Promise<Object>} 操作结果
   */
  async batchAddDevicesToGroup(groupId, deviceList) {
    try {
      this._checkInitialized();

      // 使用 addDeviceToGroup 方法，它会自动处理从旧组中移除设备
      return await this.batchOperation(
        deviceList,
        sn => this.addDeviceToGroup(groupId, sn)
      );
    } catch (error) {
      logger.error(`批量添加设备到组失败: ${groupId}`, error);
      throw error;
    }
  }

  /**
   * 批量从组中移除设备
   * @param {string} groupId - 组ID
   * @param {Array<string>} deviceList - 设备序列号列表
   * @returns {Promise<Object>} 操作结果
   */
  async batchRemoveDevicesFromGroup(groupId, deviceList) {
    try {
      this._checkInitialized();

      return await this.batchOperation(
        deviceList,
        sn => this.removeDeviceFromGroup(groupId, sn)
      );
    } catch (error) {
      logger.error(`批量从组中移除设备失败: ${groupId}`, error);
      throw error;
    }
  }

  /**
   * 更新所有在线设备的受控模式状态
   * @param {boolean} isControlled - 是否为受控模式
   * @returns {Promise<Object>} 更新结果
   */
  async updateAllDevicesControlledMode(isControlled) {
    try {
      this._checkInitialized();

      // 获取所有在线设备
      const onlineDevices = this.getConnectedDevices();
      if (!onlineDevices || onlineDevices.length === 0) {
        logger.info('没有在线设备，无需更新受控模式状态');
        return { success: true, updatedCount: 0, totalCount: 0 };
      }

      logger.info(`开始更新 ${onlineDevices.length} 台设备的受控模式状态为: ${isControlled}`);

      // 记录成功和失败的设备数量
      let successCount = 0;
      let failCount = 0;

      // 为每个在线设备发送ID命令，这将同时更新受控模式状态
      for (const device of onlineDevices) {
        try {
          const deviceData = this.deviceHistory.get(device.sn);
          if (deviceData && deviceData.id) {
            // 发送设备ID命令，同时更新受控模式状态
            await this._sendDeviceIdCommand(device.sn, deviceData.id);
            successCount++;
          } else {
            logger.warn(`设备没有ID，无法更新受控模式状态: ${device.sn}`);
            failCount++;
          }
        } catch (error) {
          logger.error(`更新设备受控模式状态失败: ${device.sn}`, error);
          failCount++;
        }
      }

      logger.info(`更新设备受控模式状态完成: 成功 ${successCount} 台，失败 ${failCount} 台`);

      return {
        success: true,
        updatedCount: successCount,
        failedCount: failCount,
        totalCount: onlineDevices.length
      };
    } catch (error) {
      logger.error('更新所有设备受控模式状态失败', error);
      throw error;
    }
  }

  /**
   * 批量控制设备音量
   * @param {Array<string>} deviceList - 设备序列号列表
   * @param {number} volume - 音量值（0-100）
   * @returns {Promise<Object>} 操作结果
   */
  async batchControlVolume(deviceList, volume) {
    try {
      this._checkInitialized();

      if (typeof volume !== 'number' || volume < 0 || volume > 100) {
        throw createValidationError('无效的音量值');
      }

      return await this.batchOperation(
        deviceList,
        sn => this.setDeviceVolume(sn, volume)
      );
    } catch (error) {
      logger.error('批量控制音量失败', error);
      throw error;
    }
  }

  /**
   * 批量控制设备屏幕
   * @param {Array<string>} deviceList - 设备序列号列表
   * @param {boolean} turnOn - 是否开启屏幕
   * @returns {Promise<Object>} 操作结果
   */
  async batchControlScreen(deviceList, turnOn) {
    try {
      this._checkInitialized();

      return await this.batchOperation(
        deviceList,
        sn => turnOn ? this.turnScreenOn(sn) : this.turnScreenOff(sn)
      );
    } catch (error) {
      logger.error('批量控制屏幕失败', error);
      throw error;
    }
  }

  /**
   * 批量控制设备快捷入口
   * @param {Array<string>} deviceList - 设备序列号列表
   * @param {boolean} show - 是否显示快捷入口
   * @returns {Promise<Object>} 操作结果
   */
  async batchControlShortcut(deviceList, show) {
    try {
      this._checkInitialized();

      return await this.batchOperation(
        deviceList,
        sn => this.controlShortcut(sn, show)
      );
    } catch (error) {
      logger.error('批量控制快捷入口失败', error);
      throw error;
    }
  }

  /**
   * 控制设备快捷入口
   * @param {string} sn - 设备序列号
   * @param {boolean} show - 是否显示快捷入口
   * @returns {Promise<void>}
   */
  async controlShortcut(sn, show) {
    try {
      this._checkInitialized();

      // 创建快捷入口控制命令
      const command = {
        type: CommandType.QUICK_ACCESS,
        data: {
          show: show,
          id: Date.now()
        }
      };

      // 发送命令
      await this.sendCommand(sn, command);
      logger.info(`设备 ${sn} 快捷入口${show ? '显示' : '隐藏'}命令已发送`);
    } catch (error) {
      logger.error(`控制设备 ${sn} 快捷入口失败`, error);
      throw error;
    }
  }
}

// 创建单例
const deviceController = new DeviceController();

// 导出单例
module.exports = deviceController;
