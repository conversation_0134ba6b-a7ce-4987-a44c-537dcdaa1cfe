/**
 * 应用常量定义
 * 集中管理应用中使用的常量
 *
 * 注意：这个文件主要是为了向后兼容，新代码应该直接使用shared目录中的常量
 */

// 导入共享类型定义
const { FileType, FileExtensions, DottedFileExtensions } = require('../../shared/types/resource');
const { DeviceStatus } = require('../../shared/types/device');
const { NETWORK, COMMAND_TYPES, BATCH } = require('../../shared/constants/network');
const { CONFIG_TYPES } = require('../../shared/constants/config');

// 为了向后兼容，保留旧的常量名称
const FILE_TYPES = FileType;
const FILE_EXTENSIONS = DottedFileExtensions;
const DEVICE_STATUS = DeviceStatus;

module.exports = {
  // 导出旧的常量名称（向后兼容）
  FILE_TYPES,
  FILE_EXTENSIONS,
  DEVICE_STATUS,
  NETWORK,
  CONFIG_TYPES,
  BATCH,
  COMMAND_TYPES,

  // 导出新的常量名称（鼓励使用）
  FileType,
  FileExtensions,
  DeviceStatus
};
