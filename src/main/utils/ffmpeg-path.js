/**
 * FFmpeg 路径解析工具
 * 用于在开发环境和生产环境中获取 FFmpeg 二进制文件的路径
 */

const path = require('path');
const fs = require('fs');
const { app } = require('electron');
const logger = require('./logger');

/**
 * 获取 FFmpeg 二进制文件的路径
 * @returns {Object} FFmpeg 和 FFprobe 的路径
 */
function getFfmpegPaths() {
  // 判断是否是开发环境
  const isDev = process.env.NODE_ENV === 'development' || process.argv.includes('--dev');
  
  // 判断操作系统
  const isWin = process.platform === 'win32';
  const isMac = process.platform === 'darwin';
  const isLinux = process.platform === 'linux';
  
  let ffmpegPath;
  let ffprobePath;
  
  if (isDev) {
    // 开发环境，使用系统安装的 FFmpeg
    ffmpegPath = 'ffmpeg';
    ffprobePath = 'ffprobe';
    
    logger.info('开发环境，使用系统安装的 FFmpeg');
  } else {
    // 生产环境，使用打包的 FFmpeg
    try {
      // 获取应用资源目录
      const resourcesPath = process.resourcesPath;
      const ffmpegBinariesPath = path.join(resourcesPath, 'ffmpeg-binaries');
      
      if (isWin) {
        ffmpegPath = path.join(ffmpegBinariesPath, 'ffmpeg.exe');
        ffprobePath = path.join(ffmpegBinariesPath, 'ffprobe.exe');
      } else if (isMac) {
        ffmpegPath = path.join(ffmpegBinariesPath, 'ffmpeg');
        ffprobePath = path.join(ffmpegBinariesPath, 'ffprobe');
      } else if (isLinux) {
        ffmpegPath = path.join(ffmpegBinariesPath, 'ffmpeg');
        ffprobePath = path.join(ffmpegBinariesPath, 'ffprobe');
      }
      
      // 检查文件是否存在
      if (ffmpegPath && fs.existsSync(ffmpegPath)) {
        logger.info(`使用打包的 FFmpeg: ${ffmpegPath}`);
      } else {
        logger.warn(`打包的 FFmpeg 不存在: ${ffmpegPath}，将使用系统安装的 FFmpeg`);
        ffmpegPath = 'ffmpeg';
        ffprobePath = 'ffprobe';
      }
    } catch (error) {
      logger.error('获取 FFmpeg 路径失败，将使用系统安装的 FFmpeg', error);
      ffmpegPath = 'ffmpeg';
      ffprobePath = 'ffprobe';
    }
  }
  
  return {
    ffmpeg: ffmpegPath,
    ffprobe: ffprobePath
  };
}

module.exports = {
  getFfmpegPaths
};
