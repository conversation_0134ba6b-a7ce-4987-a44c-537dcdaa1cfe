/**
 * 缩略图服务类
 * 负责生成各种类型资源的缩略图
 */

const fs = require('fs').promises;
const path = require('path');
const sharp = require('sharp'); // 需要安装 sharp 包用于图片处理
const ffmpeg = require('fluent-ffmpeg'); // 需要安装 fluent-ffmpeg 包用于视频处理
const { FileType, isImageType, isVideoType } = require('../../shared/types/resource.js');
const logger = require('./logger.js');
const { getFfmpegPaths } = require('./ffmpeg-path.js');

/**
 * 缩略图服务类
 */
class ThumbnailService {
  /**
   * 构造函数
   * @param {Object} fileService - 文件服务实例
   */
  constructor(fileService) {
    this.fileService = fileService;

    // 设置 FFmpeg 路径
    const ffmpegPaths = getFfmpegPaths();
    ffmpeg.setFfmpegPath(ffmpegPaths.ffmpeg);
    ffmpeg.setFfprobePath(ffmpegPaths.ffprobe);

    logger.debug('ThumbnailService: 初始化完成，FFmpeg 路径已设置');
  }

  /**
   * 生成缩略图
   * @param {string} sourcePath - 源文件路径
   * @param {string} targetDir - 目标目录
   * @param {number} type - 资源类型
   * @param {Object} [options] - 缩略图选项
   * @param {number} [options.width=180] - 缩略图宽度
   * @param {number} [options.height=120] - 缩略图高度
   * @param {number} [options.quality=80] - 缩略图质量
   * @param {string} [options.fileName] - 指定的文件名（如果不提供，则自动生成）
   * @returns {Promise<string>} 缩略图文件名
   */
  async generateThumbnail(sourcePath, targetDir, type, options = {}) {
    try {
      // 设置默认选项
      const thumbnailOptions = {
        width: options.width || 180,
        height: options.height || 120,
        quality: options.quality || 80
      };

      // 确保目标目录存在
      await this.fileService._ensureDirectoryExists(targetDir);

      // 生成缩略图文件名（如果没有指定）
      const timestamp = Date.now();
      const thumbnailFileName = options.fileName || `thumbnail_${timestamp}.jpg`;
      const thumbnailPath = path.join(targetDir, thumbnailFileName);

      let success = false;

      // 根据资源类型选择不同的缩略图生成方法
      // 图片类型
      if (isImageType(type)) {
        success = await this.generateImageThumbnail(sourcePath, thumbnailPath, thumbnailOptions);
      }
      // 视频类型，包括2D视频和3D视频
      else if (isVideoType(type)) {
        success = await this.generateVideoThumbnail(sourcePath, thumbnailPath, thumbnailOptions);
      }
      // 应用类型和其他类型
      else if (type === FileType.APP) {
        // 对于应用类型，复制默认海报
        try {
          const defaultPosterPath = path.join(__dirname, '../../renderer/assets/images/yvr_defualt_app.png');
          await fs.copyFile(defaultPosterPath, thumbnailPath);
          success = true;
          logger.debug(`使用默认海报作为应用缩略图: ${thumbnailPath}`);
        } catch (error) {
          logger.error('复制默认海报失败:', error);
          success = false;
        }
      } else {
        // 对于其他类型的文件，不生成缩略图
        logger.warn(`不支持的文件类型: ${type}，无法生成缩略图`);
        return '';
      }

      if (success) {
        logger.debug(`生成缩略图成功: ${thumbnailPath}`);
        return thumbnailFileName;
      } else {
        logger.warn(`生成缩略图失败: ${thumbnailPath}`);
        return '';
      }
    } catch (error) {
      logger.error('生成缩略图失败:', error);
      return '';
    }
  }

  /**
   * 生成图片缩略图
   * @param {string} sourcePath - 源文件路径
   * @param {string} targetPath - 目标文件路径
   * @param {Object} options - 缩略图选项
   * @returns {Promise<boolean>} 是否成功生成缩略图
   */
  async generateImageThumbnail(sourcePath, targetPath, options) {
    try {
      await sharp(sourcePath)
        .resize(options.width, options.height, {
          fit: 'cover',
          position: 'centre'
        })
        .jpeg({
          quality: options.quality
        })
        .toFile(targetPath);

      logger.debug(`生成图片缩略图成功: ${targetPath}`);
      return true;
    } catch (error) {
      logger.error(`生成图片缩略图失败: ${sourcePath}`, error);
      return false;
    }
  }

  /**
   * 生成视频缩略图
   * @param {string} sourcePath - 源文件路径
   * @param {string} targetPath - 目标文件路径（完整路径，包含文件名）
   * @param {Object} options - 缩略图选项
   * @returns {Promise<boolean>} 是否成功生成缩略图
   */
  async generateVideoThumbnail(sourcePath, targetPath, options) {
    try {
      const targetDir = path.dirname(targetPath);
      const fileName = path.basename(targetPath);

      await new Promise((resolve, reject) => {
        ffmpeg(sourcePath)
          .screenshots({
            timestamps: ['1%'], // 从视频开始1%处截图
            filename: fileName,
            folder: targetDir,
            size: `${options.width}x${options.height}`
          })
          .on('end', resolve)
          .on('error', reject);
      });

      logger.debug(`生成视频缩略图成功: ${targetPath}`);
      return true;
    } catch (error) {
      logger.error(`生成视频缩略图失败: ${sourcePath}`, error);
      return false;
    }
  }

  /**
   * 生成视频缩略图（旧版本，保持向后兼容）
   * @private
   * @param {string} sourcePath - 源文件路径
   * @param {string} targetDir - 目标目录
   * @param {string} fileName - 文件名
   * @param {Object} options - 缩略图选项
   * @returns {Promise<void>}
   * @deprecated 使用 generateVideoThumbnail(sourcePath, targetPath, options) 代替
   */
  async _generateVideoThumbnailLegacy(sourcePath, targetDir, fileName, options) {
    return this.generateVideoThumbnail(sourcePath, path.join(targetDir, fileName), options);
  }
}

module.exports = ThumbnailService;
