const path = require('path');
const { app } = require('electron');

/**
 * 应用程序路径配置管理
 */

/**
 * 获取应用数据根目录
 * Windows: %APPDATA%/device-manager
 * macOS: $HOME/device-manager
 * Linux: $HOME/device-manager
 * @returns {string} 应用数据根目录的完整路径
 */
const getAppDataRoot = () => {
  const rootDir = process.env.APPDATA || process.env.HOME;
  return path.join(rootDir, 'device-manager');
};

/**
 * 获取资源存储根目录
 * 如果设置了自定义路径，则使用自定义路径
 * 否则使用默认路径：
 * Windows: %USERPROFILE%/Documents/DeviceManagerResources
 * macOS: $HOME/Documents/DeviceManagerResources
 * Linux: $HOME/Documents/DeviceManagerResources
 * @param {string} [customPath=''] - 自定义路径
 * @returns {string} 资源存储根目录的完整路径
 */
const getResourcesRoot = (customPath = '') => {
  // 如果提供了自定义路径且不为空，则使用自定义路径
  if (customPath && typeof customPath === 'string' && customPath.trim() !== '') {
    return customPath.trim();
  }

  // 否则使用默认路径
  const documentsDir = app.getPath('documents');
  return path.join(documentsDir, 'DeviceManagerResources');
};

/**
 * 获取设置中的自定义资源路径
 * @returns {string} 自定义资源路径
 */
const getCustomResourcesPath = () => {
  try {
    // 读取设置文件
    const settingsPath = path.join(getAppDataRoot(), 'pdm_settings.json');
    if (require('fs').existsSync(settingsPath)) {
      const settings = JSON.parse(require('fs').readFileSync(settingsPath, 'utf8'));
      return settings?.basic?.resourcesPath || '';
    }
  } catch (error) {
    // 如果读取失败，返回空字符串
    return '';
  }
  return '';
};

/**
 * 应用程序所有路径配置
 * @constant {Object} paths
 */
const paths = {
  // 应用根目录
  root: getAppDataRoot(),

  // 资源相关路径
  resources: {
    // 资源根目录
    root: path.join(getResourcesRoot(getCustomResourcesPath()), 'config'),
    // 资源配置文件路径 - 统一使用 Documents 目录下的 config.dat
    config: path.join(getResourcesRoot(getCustomResourcesPath()), 'config', 'config.dat'),
  },

  // 应用程序设置
  settings: {
    // 设置文件路径
    root: path.join(getAppDataRoot(), 'pdm_settings.json'),
  },

  // 自定义配置目录
  customData: {
    root: path.join(getAppDataRoot(), 'custom-data'),
    config: path.join(getAppDataRoot(), 'custom-data', 'custom-config.json'),
  },

  // 设备信息存储
  devices: {
    // 设备信息文件路径
    history: path.join(getAppDataRoot(), 'device_history.json'),
    blocklist: path.join(getAppDataRoot(), 'device_blocklist.json'),
    // 设备组信息文件路径
    groups: path.join(getAppDataRoot(), 'device_groups.json'),
  },

  // 发布记录存储
  publishRecords: {
    // 发布记录文件路径
    records: path.join(getAppDataRoot(), 'publish_records.json'),
  },
};

/**
 * 确保所有必要的目录都存在
 * @async
 * @returns {Promise<void>}
 */
const ensureDirectories = async () => {
  const fs = require('fs').promises;
  const directories = [
    paths.root,
    paths.resources.root,
    path.dirname(paths.settings.root),
    paths.customData.root,
    // 确保资源根目录存在
    path.dirname(paths.resources.root)
  ];

  for (const dir of directories) {
    await fs.mkdir(dir, { recursive: true });
  }
};

/**
 * 获取文件的相对路径
 * @param {string} absolutePath - 文件的绝对路径
 * @param {string} basePath - 基准路径
 * @returns {string} 相对路径
 */
const getRelativePath = (absolutePath, basePath) => {
  return path.relative(basePath, absolutePath);
};

/**
 * 更新资源路径
 * 当用户修改资源路径时，需要更新 paths 对象中的资源路径
 * @param {string} newPath - 新的资源路径
 * @returns {Object} 更新后的资源路径对象
 */
const updateResourcesPath = (newPath) => {
  // 更新 paths 对象中的资源路径
  paths.resources.root = path.join(getResourcesRoot(newPath), 'config');
  paths.resources.config = path.join(getResourcesRoot(newPath), 'config', 'config.dat');

  // 确保目录存在
  const fs = require('fs').promises;
  fs.mkdir(path.dirname(paths.resources.root), { recursive: true }).catch(err => {
    console.error('创建资源目录失败:', err);
  });

  return {
    root: paths.resources.root,
    config: paths.resources.config
  };
};

module.exports = {
  paths,
  ensureDirectories,
  getRelativePath,
  getAppDataRoot,
  getResourcesRoot,
  updateResourcesPath
};
