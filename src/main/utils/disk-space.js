const fs = require('fs').promises;
const path = require('path');
const logger = require('./logger');
const { CONFIG_FOLDER_MAX_SIZE_GB } = require('../../shared/constants/config');

// 添加模拟标志
let simulateLowSpace = false;
let simulatedAvailableSpace = 1024 * 1024 * 10; // 10MB

/**
 * 自定义错误类
 */
class DiskSpaceError extends Error {
  constructor(message, code, details) {
    super(message);
    this.name = 'DiskSpaceError';
    this.code = code;
    this.details = details;
    // 确保错误对象可以被正确序列化
    Object.defineProperty(this, 'message', {
      enumerable: true,
      value: message
    });
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      details: this.details
    };
  }
}

/**
 * 设置模拟磁盘空间状态
 * @param {boolean} enabled - 是否启用模拟
 * @param {number} [availableSpace] - 模拟的可用空间（字节）
 */
function setSimulateLowSpace(enabled, availableSpace) {
  simulateLowSpace = enabled;
  if (availableSpace !== undefined) {
    simulatedAvailableSpace = availableSpace;
  }
  logger.info('设置模拟磁盘空间状态', {
    enabled: simulateLowSpace,
    availableSpace: `${Math.floor(simulatedAvailableSpace / (1024 * 1024))}MB`
  });
}

/**
 * 检查磁盘剩余空间
 * @param {string} filePath - 文件路径
 * @param {number} requiredSize - 所需空间大小（字节）
 * @returns {Promise<boolean>} 是否有足够空间
 */
async function _checkDiskSpace(filePath, requiredSize) {
  try {
    // 获取文件所在磁盘的路径
    const diskPath = path.parse(filePath).root;
    
    logger.info('开始检查磁盘空间', {
      diskPath,
      filePath,
      requiredSize: `${Math.floor(requiredSize / (1024 * 1024))}MB`
    });
    
    // 如果启用了模拟，使用模拟的可用空间
    let availableSpace;
    if (simulateLowSpace) {
      availableSpace = simulatedAvailableSpace;
      logger.info('使用模拟的磁盘空间', {
        availableSpace: `${Math.floor(availableSpace / (1024 * 1024))}MB`
      });
    } else {
      // 获取磁盘信息
      const stats = await fs.statfs(diskPath);
      availableSpace = stats.bfree * stats.bsize;
    }
    
    const availableSpaceMB = Math.floor(availableSpace / (1024 * 1024));
    const requiredSizeMB = Math.floor(requiredSize / (1024 * 1024));
    const bufferSizeMB = Math.floor(requiredSize * 0.05 / (1024 * 1024));
    const totalRequiredMB = requiredSizeMB + bufferSizeMB;
    
    logger.info('磁盘空间检查结果', {
      diskPath,
      availableSpace: `${availableSpaceMB}MB`,
      requiredSize: `${requiredSizeMB}MB`,
      bufferSize: `${bufferSizeMB}MB`,
      totalRequired: `${totalRequiredMB}MB`
    });
    
    // 检查是否有足够空间（预留5%的缓冲空间）
    const hasEnoughSpace = availableSpace > (requiredSize * 1.05);
    
    if (!hasEnoughSpace) {
      throw `需要 ${totalRequiredMB}MB 空间，当前可用 ${availableSpaceMB}MB`;
    } else {
      logger.info('磁盘空间充足', {
        diskPath,
        availableSpace: `${availableSpaceMB}MB`,
        requiredSize: `${requiredSizeMB}MB`,
        bufferSize: `${bufferSizeMB}MB`,
        totalRequired: `${totalRequiredMB}MB`
      });
    }
    
    return true;
  } catch (error) {
    if (typeof error === 'string') {
      throw error;
    }
    // 其他错误默认返回true，避免阻止上传
    return true;
  }
}

/**
 * 获取文件夹大小
 * @param {string} dirPath - 文件夹路径
 * @returns {Promise<number>} 文件夹大小（字节）
 */
async function _getFolderSize(dirPath) {
  try {
    let totalSize = 0;
    const files = await fs.readdir(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stats = await fs.stat(filePath);
      
      if (stats.isDirectory()) {
        totalSize += await _getFolderSize(filePath);
      } else {
        totalSize += stats.size;
      }
    }
    
    return totalSize;
  } catch (error) {
    logger.error('获取文件夹大小失败', error);
    throw error;
  }
}

/**
 * 检查config文件夹大小是否超过限制
 * @param {string} configPath - config文件夹路径
 * @param {number} [maxSizeGB] - 最大允许大小（GB），默认使用CONFIG_FOLDER_MAX_SIZE_GB
 * @returns {Promise<{isOverLimit: boolean, size: number, maxSize: number}>}
 */
async function checkConfigFolderSize(configPath, maxSizeGB = CONFIG_FOLDER_MAX_SIZE_GB) {
  try {
    const size = await _getFolderSize(configPath);
    const sizeGB = size / (1024 * 1024 * 1024);
    const maxSize = maxSizeGB * 1024 * 1024 * 1024;
    
    logger.info('检查config文件夹大小', {
      path: configPath,
      size: `${sizeGB.toFixed(2)}GB`,
      maxSize: `${maxSizeGB}GB`
    });
    
    return {
      isOverLimit: size > maxSize,
      size: size,
      maxSize: maxSize
    };
  } catch (error) {
    logger.error('检查config文件夹大小失败', error);
    throw error;
  }
}

module.exports = {
  _checkDiskSpace,
  setSimulateLowSpace,
  DiskSpaceError,
  checkConfigFolderSize
}; 