/**
 * 日志工具
 * 提供统一的日志记录功能
 */

const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const { format } = require('util');
const zlib = require('zlib');

// 日志配置
const LOG_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 单个日志文件最大大小（10MB）
  MAX_FILES: 30, // 最大保留天数
  COMPRESS_AFTER_DAYS: 7, // 超过多少天后压缩
};

// 日志级别
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
};

// 日志级别名称
const LOG_LEVEL_NAMES = {
  [LOG_LEVELS.DEBUG]: 'DEBUG',
  [LOG_LEVELS.INFO]: 'INFO',
  [LOG_LEVELS.WARN]: 'WARN',
  [LOG_LEVELS.ERROR]: 'ERROR'
};

// 日志颜色（控制台输出）
const LOG_COLORS = {
  [LOG_LEVELS.DEBUG]: '\x1b[36m', // 青色
  [LOG_LEVELS.INFO]: '\x1b[32m',  // 绿色
  [LOG_LEVELS.WARN]: '\x1b[33m',  // 黄色
  [LOG_LEVELS.ERROR]: '\x1b[31m', // 红色
  RESET: '\x1b[0m'                // 重置
};

/**
 * 日志类
 */
class Logger {
  /**
   * 构造函数
   */
  constructor() {
    // 默认日志级别
    this.level = LOG_LEVELS.INFO;
    
    // 是否启用控制台输出
    this.enableConsole = true;
    
    // 是否启用文件输出
    this.enableFile = true;
    
    // 日志文件路径
    this.logDir = this._getLogDirectory();
    
    // 日志文件流
    this.logStream = null;
    
    // 当前日志文件大小
    this.currentFileSize = 0;
    
    // 初始化日志文件
    this._initLogFile();
    
    // 清理旧日志文件
    this._cleanOldLogs();
  }
  
  /**
   * 获取日志目录
   * @private
   * @returns {string} 日志目录路径
   */
  _getLogDirectory() {
    // 在开发环境中，使用当前目录下的logs文件夹
    // 在生产环境中，使用应用数据目录下的logs文件夹
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'pdmlogs');
  }
  
  /**
   * 初始化日志文件
   * @private
   */
  _initLogFile() {
    if (!this.enableFile) {
      return;
    }
    
    try {
      // 确保日志目录存在
      if (!fs.existsSync(this.logDir)) {
        fs.mkdirSync(this.logDir, { recursive: true });
      }
      
      // 创建日志文件名（使用当前日期）
      const now = new Date();
      const fileName = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}.log`;
      const filePath = path.join(this.logDir, fileName);
      
      // 创建日志文件流（追加模式）
      this.logStream = fs.createWriteStream(filePath, { flags: 'a' });
      
      // 写入启动日志
      const startMessage = `\n[${this._getTimestamp()}] [INFO] 日志系统启动\n`;
      this.logStream.write(startMessage);
    } catch (error) {
      console.error('初始化日志文件失败:', error);
      this.enableFile = false;
    }
  }
  
  /**
   * 获取当前时间戳
   * @private
   * @returns {string} 时间戳字符串
   */
  _getTimestamp() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
  }
  
  /**
   * 格式化日志消息
   * @private
   * @param {number} level - 日志级别
   * @param {string} message - 日志消息
   * @param {*} [data] - 附加数据
   * @returns {string} 格式化后的日志消息
   */
  _formatLogMessage(level, message, data) {
    const timestamp = this._getTimestamp();
    const levelName = LOG_LEVEL_NAMES[level];
    
    let formattedMessage = `[${timestamp}] [${levelName}] ${message}`;
    
    if (data !== undefined) {
      if (typeof data === 'object') {
        try {
          formattedMessage += ` ${JSON.stringify(data)}`;
        } catch (error) {
          formattedMessage += ` ${format(data)}`;
        }
      } else {
        formattedMessage += ` ${data}`;
      }
    }
    
    return formattedMessage;
  }
  
  /**
   * 清理旧日志文件
   * @private
   */
  _cleanOldLogs() {
    try {
      if (!fs.existsSync(this.logDir)) {
        return;
      }

      const files = fs.readdirSync(this.logDir);
      const now = new Date();

      files.forEach(file => {
        const filePath = path.join(this.logDir, file);
        const stats = fs.statSync(filePath);
        const fileDate = this._getDateFromFileName(file);

        if (!fileDate) {
          return;
        }

        const daysDiff = Math.floor((now - fileDate) / (1000 * 60 * 60 * 24));

        // 删除超过最大保留天数的日志
        if (daysDiff > LOG_CONFIG.MAX_FILES) {
          fs.unlinkSync(filePath);
          return;
        }

        // 压缩超过指定天数的日志文件
        if (daysDiff > LOG_CONFIG.COMPRESS_AFTER_DAYS && !file.endsWith('.gz')) {
          this._compressLogFile(filePath);
        }
      });
    } catch (error) {
      console.error('清理旧日志文件失败:', error);
    }
  }

  /**
   * 从文件名中获取日期
   * @private
   * @param {string} fileName - 文件名
   * @returns {Date|null} 日期对象
   */
  _getDateFromFileName(fileName) {
    const match = fileName.match(/^(\d{4})-(\d{2})-(\d{2})/);
    if (!match) {
      return null;
    }
    return new Date(match[1], parseInt(match[2]) - 1, match[3]);
  }

  /**
   * 压缩日志文件
   * @private
   * @param {string} filePath - 文件路径
   */
  _compressLogFile(filePath) {
    try {
      const gzFilePath = `${filePath}.gz`;
      const fileContent = fs.readFileSync(filePath);
      const compressed = zlib.gzipSync(fileContent);
      fs.writeFileSync(gzFilePath, compressed);
      fs.unlinkSync(filePath);
    } catch (error) {
      console.error('压缩日志文件失败:', error);
    }
  }

  /**
   * 检查并轮转日志文件
   * @private
   */
  _checkRotation() {
    if (!this.logStream || !this.enableFile) {
      return;
    }

    try {
      const stats = fs.statSync(this.logStream.path);
      this.currentFileSize = stats.size;

      if (this.currentFileSize >= LOG_CONFIG.MAX_FILE_SIZE) {
        // 关闭当前日志流
        this.logStream.end();
        this.logStream = null;

        // 重命名当前日志文件，添加时间戳
        const oldPath = this.logStream.path;
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const newPath = `${oldPath}.${timestamp}`;
        fs.renameSync(oldPath, newPath);

        // 创建新的日志文件
        this._initLogFile();
      }
    } catch (error) {
      console.error('检查日志文件轮转失败:', error);
    }
  }
  
  /**
   * 写入日志
   * @private
   * @param {number} level - 日志级别
   * @param {string} message - 日志消息
   * @param {*} [data] - 附加数据
   */
  _log(level, message, data) {
    if (level < this.level) {
      return;
    }
    
    const formattedMessage = this._formatLogMessage(level, message, data);
    
    // 控制台输出
    if (this.enableConsole) {
      const color = LOG_COLORS[level];
      console.log(`${color}${formattedMessage}${LOG_COLORS.RESET}`);
    }
    
    // 文件输出
    if (this.enableFile && this.logStream) {
      const messageWithNewline = `${formattedMessage}\n`;
      this.logStream.write(messageWithNewline);
      this.currentFileSize += Buffer.byteLength(messageWithNewline);
      
      // 检查是否需要轮转
      if (this.currentFileSize >= LOG_CONFIG.MAX_FILE_SIZE) {
        this._checkRotation();
      }
    }
  }
  
  /**
   * 设置日志级别
   * @param {number|string} level - 日志级别或级别名称
   */
  setLevel(level) {
    if (typeof level === 'string') {
      const upperLevel = level.toUpperCase();
      if (LOG_LEVELS[upperLevel] !== undefined) {
        this.level = LOG_LEVELS[upperLevel];
      }
    } else if (typeof level === 'number' && level >= LOG_LEVELS.DEBUG && level <= LOG_LEVELS.NONE) {
      this.level = level;
    }
  }
  
  /**
   * 启用或禁用控制台输出
   * @param {boolean} enable - 是否启用
   */
  setConsoleOutput(enable) {
    this.enableConsole = !!enable;
  }
  
  /**
   * 启用或禁用文件输出
   * @param {boolean} enable - 是否启用
   */
  setFileOutput(enable) {
    this.enableFile = !!enable;
    
    if (enable && !this.logStream) {
      this._initLogFile();
    }
  }
  
  /**
   * 记录调试级别日志
   * @param {string} message - 日志消息
   * @param {*} [data] - 附加数据
   */
  debug(message, data) {
    this._log(LOG_LEVELS.DEBUG, message, data);
  }
  
  /**
   * 记录信息级别日志
   * @param {string} message - 日志消息
   * @param {*} [data] - 附加数据
   */
  info(message, data) {
    this._log(LOG_LEVELS.INFO, message, data);
  }
  
  /**
   * 记录警告级别日志
   * @param {string} message - 日志消息
   * @param {*} [data] - 附加数据
   */
  warn(message, data) {
    this._log(LOG_LEVELS.WARN, message, data);
  }
  
  /**
   * 记录错误级别日志
   * @param {string} message - 日志消息
   * @param {*} [data] - 附加数据
   */
  error(message, data) {
    this._log(LOG_LEVELS.ERROR, message, data);
  }
  
  /**
   * 关闭日志系统
   */
  close() {
    if (this.logStream) {
      const closeMessage = `[${this._getTimestamp()}] [INFO] 日志系统关闭\n`;
      this.logStream.write(closeMessage);
      this.logStream.end();
      this.logStream = null;
    }
  }
}

// 创建单例
const logger = new Logger();

// 导出单例
module.exports = logger;
