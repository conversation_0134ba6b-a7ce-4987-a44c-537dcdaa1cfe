/**
 * 错误处理工具
 * 提供统一的错误类型和处理机制
 */

const logger = require('./logger');

/**
 * 应用错误类
 * 扩展Error类，添加错误代码和详情
 */
class AppError extends Error {
  /**
   * 构造函数
   * @param {string} message - 错误消息
   * @param {string} code - 错误代码
   * @param {Object} [details={}] - 错误详情
   */
  constructor(message, code, details = {}) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.details = details;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 错误类型枚举
 * @readonly
 * @enum {string}
 */
const ErrorTypes = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  CONFIG_ERROR: 'CONFIG_ERROR',
  FILE_ERROR: 'FILE_ERROR'
};

/**
 * 处理错误
 * @param {Error} error - 错误对象
 * @param {string} [context=''] - 错误上下文
 */
function handleError(error, context = '') {
  if (error instanceof AppError) {
    logger.error(`[${context}] ${error.code}: ${error.message}`, error.details);
  } else {
    logger.error(`[${context}] UNEXPECTED_ERROR: ${error.message}`, error);
  }
  
  // 可以在这里添加错误报告、崩溃恢复等逻辑
}

/**
 * 创建验证错误
 * @param {string} message - 错误消息
 * @param {Object} [details={}] - 错误详情
 * @returns {AppError} 验证错误
 */
function createValidationError(message, details = {}) {
  return new AppError(message, ErrorTypes.VALIDATION_ERROR, details);
}

/**
 * 创建未找到错误
 * @param {string} message - 错误消息
 * @param {Object} [details={}] - 错误详情
 * @returns {AppError} 未找到错误
 */
function createNotFoundError(message, details = {}) {
  return new AppError(message, ErrorTypes.NOT_FOUND_ERROR, details);
}

/**
 * 创建网络错误
 * @param {string} message - 错误消息
 * @param {Object} [details={}] - 错误详情
 * @returns {AppError} 网络错误
 */
function createNetworkError(message, details = {}) {
  return new AppError(message, ErrorTypes.NETWORK_ERROR, details);
}

/**
 * 创建配置错误
 * @param {string} message - 错误消息
 * @param {Object} [details={}] - 错误详情
 * @returns {AppError} 配置错误
 */
function createConfigError(message, details = {}) {
  return new AppError(message, ErrorTypes.CONFIG_ERROR, details);
}

/**
 * 创建文件错误
 * @param {string} message - 错误消息
 * @param {Object} [details={}] - 错误详情
 * @returns {AppError} 文件错误
 */
function createFileError(message, details = {}) {
  return new AppError(message, ErrorTypes.FILE_ERROR, details);
}

module.exports = {
  AppError,
  ErrorTypes,
  handleError,
  createValidationError,
  createNotFoundError,
  createNetworkError,
  createConfigError,
  createFileError
};
