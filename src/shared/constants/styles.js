/**
 * CSS类名常量
 * 用于统一管理CSS类名
 */

/**
 * 设备状态CSS类名
 * @enum {string}
 */
export const DeviceStatusClass = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  DEVICE_OFFLINE: 'device-offline'
};

/**
 * 批量模式CSS类名
 * @enum {string}
 */
export const BatchModeClass = {
  BATCH_MODE: 'batch-mode',
  HIDDEN: 'hidden'
};

/**
 * 下拉菜单CSS类名
 * @enum {string}
 */
export const DropdownClass = {
  SHOW: 'show',
  DROPDOWN: 'dropdown',
  DROPDOWN_CONTENT: 'dropdown-content',
  DROPDOWN_BUTTON: 'dropdown-button',
  DROPDOWN_ITEM: 'dropdown-item'
};

/**
 * 设备列表CSS类名
 * @enum {string}
 */
export const DeviceListClass = {
  HAS_DEVICES: 'has-devices',
  DEVICE_ITEM: 'device-item',
  DEVICE_INFO: 'device-info',
  DEVICE_HEADER: 'device-header',
  DEVICE_STATUS: 'device-status',
  STATUS_INDICATOR: 'status-indicator',
  STATUS_ITEM: 'status-item',
  STATUS_LABEL: 'status-label'
};

/**
 * 复选框CSS类名
 * @enum {string}
 */
export const CheckboxClass = {
  DEVICE_SELECT_CHECKBOX: 'device-select-checkbox',
  DEVICE_SELECT: 'device-select',
  CHECKMARK: 'checkmark'
};
