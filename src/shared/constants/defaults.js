/**
 * 默认配置值常量
 * 用于统一管理默认配置值
 */

/**
 * 默认PC版本号
 * 注意：此常量仅用于类型定义和文档目的
 * 实际版本号在运行时从package.json获取
 * @constant {string}
 */
export const DEFAULT_PC_VERSION = '1.0.0';

/**
 * 默认端口号
 * @constant {number}
 */
export const DEFAULT_PORT = 50208;

/**
 * 默认UDP端口号
 * @constant {number}
 */
export const DEFAULT_UDP_PORT = 9944;

/**
 * 默认WebSocket端口号
 * @constant {number}
 */
export const DEFAULT_WS_PORT = 50208;

/**
 * 默认心跳间隔（毫秒）
 * @constant {number}
 */
export const DEFAULT_HEARTBEAT_INTERVAL = 10000;

/**
 * 默认分组
 * @constant {Array<string>}
 */
export const DEFAULT_GROUPS = ['未分组'];

/**
 * 默认文件块大小（字节）
 * @constant {number}
 */
export const DEFAULT_CHUNK_SIZE = 2 * 1024 * 1024; // 2MB

/**
 * 默认超时阈值（毫秒）
 * @constant {number}
 */
export const DEFAULT_TIMEOUT_THRESHOLD = 30000; // 30秒

/**
 * 默认配置对象
 * @type {Object}
 */
export const DEFAULT_CONFIG = {
  pcVersion: DEFAULT_PC_VERSION,
  port: DEFAULT_PORT,
  groups: DEFAULT_GROUPS
};

/**
 * 默认设置对象
 * @type {Object}
 */
export const DEFAULT_SETTINGS = {
  controlledMode: false,
  basic: {
    serverName: '',
    udpPort: DEFAULT_UDP_PORT,
    wsPort: DEFAULT_WS_PORT
  },
  advanced: {
    heartbeatInterval: DEFAULT_HEARTBEAT_INTERVAL
  }
};
