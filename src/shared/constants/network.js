/**
 * 网络常量定义
 * 集中管理网络相关的常量
 */
import { CommandType } from './command-types.js';

/**
 * 网络常量
 * @readonly
 * @enum {number}
 */
export const NETWORK = {
  DEFAULT_UDP_PORT: 9944,
  DEFAULT_WS_PORT: 50208,
  DEFAULT_HEARTBEAT_INTERVAL: 10000,
  BROADCAST_INTERVAL: 1000 // 广播间隔固定为1秒
};

/**
 * 命令类型常量 (兼容旧代码)
 * @readonly
 * @enum {number}
 * @deprecated 请使用 CommandType 代替
 */
export const COMMAND_TYPES = {
  PLAY: CommandType.PLAY,
  STOP: CommandType.STOP,
  CONTROL: CommandType.CONTROL,
  REBOOT: CommandType.RESTART,
  SHUTDOWN: CommandType.SHUTDOWN,
  UPDATE: CommandType.UPDATE,
  DEPLOY: CommandType.DEPLOY
};

/**
 * 批处理常量
 * @readonly
 * @enum {number}
 */
export const BATCH = {
  DEFAULT_SIZE: 5,
  DEFAULT_CHUNK_SIZE: 1024 * 1024 // 1MB
};
