/**
 * 页面ID常量
 * @enum {string}
 */
export const PageId = {
  DEVICES: 'devices',
  CONTROL: 'control',
  SOLUTIONS: 'solutions',
  SETTINGS: 'settings',
  ADD_DEVICE: 'add-device',
  ADD_RESOURCE: 'add-resource'
};

/**
 * 页面元素ID常量
 * @enum {string}
 */
export const PageElementId = {
  DEVICES: 'devices-page',
  CONTROL: 'control-page',
  SOLUTIONS: 'solutions-page',
  SETTINGS: 'settings-page',
  ADD_DEVICE: 'add-device-page',
  ADD_RESOURCE: 'add-resource-page'
};

/**
 * 页面图标常量
 * @enum {string}
 */
export const PageIcon = {
  DEVICES: 'icon-device',
  CONTROL: 'icon-control',
  SOLUTIONS: 'icon-solution',
  SETTINGS: 'icon-settings'
};

/**
 * 页面名称常量
 * @enum {string}
 */
export const PageName = {
  DEVICES: '设备管理',
  CONTROL: '播控',
  SOLUTIONS: '方案管理',
  SETTINGS: '系统设置',
  ADD_DEVICE: '添加设备',
  ADD_RESOURCE: '添加资源'
};

/**
 * 特殊页面配置
 * @type {Object}
 */
export const SpecialPages = {
  [PageId.ADD_DEVICE]: {
    pageId: PageElementId.ADD_DEVICE,
    parentId: PageId.DEVICES
  },
  [PageId.ADD_RESOURCE]: {
    pageId: PageElementId.ADD_RESOURCE,
    parentId: PageId.CONTROL
  }
};

/**
 * 创建页面配置对象
 * @param {string} id - 页面ID
 * @returns {Object} 页面配置对象
 */
export function createPageConfig(id) {
  switch (id) {
    case PageId.DEVICES:
      return {
        id: PageId.DEVICES,
        name: PageName.DEVICES,
        icon: PageIcon.DEVICES,
        pageId: PageElementId.DEVICES
      };
    case PageId.CONTROL:
      return {
        id: PageId.CONTROL,
        name: PageName.CONTROL,
        icon: PageIcon.CONTROL,
        pageId: PageElementId.CONTROL
      };
    case PageId.SOLUTIONS:
      return {
        id: PageId.SOLUTIONS,
        name: PageName.SOLUTIONS,
        icon: PageIcon.SOLUTIONS,
        pageId: PageElementId.SOLUTIONS
      };
    case PageId.SETTINGS:
      return {
        id: PageId.SETTINGS,
        name: PageName.SETTINGS,
        icon: PageIcon.SETTINGS,
        pageId: PageElementId.SETTINGS
      };
    default:
      return null;
  }
}

/**
 * 获取所有主页面配置
 * @returns {Array<Object>} 页面配置数组
 */
export function getAllPageConfigs() {
  return [
    createPageConfig(PageId.DEVICES),
    createPageConfig(PageId.CONTROL),
    createPageConfig(PageId.SOLUTIONS),
    createPageConfig(PageId.SETTINGS)
  ];
}
