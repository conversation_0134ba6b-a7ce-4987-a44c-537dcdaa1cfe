import { DEFAULT_GROUPS } from './defaults.js';

/**
 * 配置常量定义
 * 集中管理配置相关的常量
 */

/**
 * 配置类型常量
 * @readonly
 * @enum {string}
 */
export const CONFIG_TYPES = {
  RESOURCES: 'resources',
  SETTINGS: 'settings',
  CUSTOM_DATA: 'customData',
  DEVICE_HISTORY: 'devices.history',
  DEVICE_BLOCKLIST: 'devices.blocklist',
  DEVICE_GROUPS: 'devices.groups'
};

/**
 * 默认配置
 * @readonly
 * @type {Object}
 */
export const DEFAULT_CONFIGS = {
  RESOURCES: {
    UUID: '',
    groups: DEFAULT_GROUPS,
    list: [],
    name: "播控方案",
    description: "自动创建的默认方案",
    pcVersion: "1.0.0", // 此值将在运行时通过app-handlers.js中的方法被替换为实际版本
    port: 50208
  },
  SETTINGS: {
    controlledMode: false,
    basic: {
      serverName: '',
      udpPort: 9944,
      wsPort: 50208
    },
    advanced: {
      heartbeatInterval: 10000
    }
  },
  CUSTOM_DATA: {
    appLogo: ''
  },
  DEVICE_GROUPS: []
};

/**
 * 配置文件夹大小限制（GB）
 * @readonly
 * @type {number}
 */
export const CONFIG_FOLDER_MAX_SIZE_GB = 10;
