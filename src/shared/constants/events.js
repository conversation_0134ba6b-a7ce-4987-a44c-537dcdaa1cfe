/**
 * 事件常量定义
 * 使用命名空间来组织事件名称，避免冲突
 */

// 设备相关事件
export const DeviceEvents = {
  CONNECTED: 'device:connected',
  DISCONNECTED: 'device:disconnected',
  STATUS_UPDATED: 'device:statusUpdated',
  ADDED: 'device:added',
  DELETED: 'device:deleted',
  LIST_UPDATED: 'device:listUpdated',

  // 设备组相关事件
  GROUP_CREATED: 'device:groupCreated',
  GROUP_UPDATED: 'device:groupUpdated',
  GROUP_DELETED: 'device:groupDeleted',
  GROUP_LIST_UPDATED: 'device:groupListUpdated',
  DEVICE_ADDED_TO_GROUP: 'device:addedToGroup',
  DEVICE_REMOVED_FROM_GROUP: 'device:removedFromGroup'
};

// 资源相关事件
export const ResourceEvents = {
  LOADED: 'resource:loaded',
  PUBLISHED: 'resource:published',
  PUBLISH_SUCCESS: 'resource:publishSuccess', // 新增：发布成功事件
  STOPPED: 'resource:stopped',
  SHOW_PUBLISH: 'resource:showPublish',
  GET_GROUPS: 'resource:getGroups',
  GROUPS_UPDATED: 'resource:groupsUpdated',
  GROUP_CHANGED: 'resource:groupChanged',
  SHOW_ADD_RESOURCE: 'resource:showAddResource'
};

// 方案相关事件
export const SolutionEvents = {
  LOAD: 'solution:load',
  LOADED: 'solution:loaded',
  RELOAD: 'solution:reload',
  CREATED: 'solution:created',
  UPDATED: 'solution:updated',
  DELETED: 'solution:deleted',
  DEPLOYED: 'solution:deployed',
  EXPORTED: 'solution:exported',
  SELECTED: 'solution:selected',
  REQUEST_SOLUTION: 'solution:requestSolution',

  // 资源相关事件
  RESOURCE_CREATED: 'solution:resourceCreated',
  RESOURCE_ADDED: 'solution:resourceAdded',
  RESOURCE_UPDATE: 'solution:resourceUpdate',
  RESOURCE_UPDATED: 'solution:resourceUpdated',
  RESOURCE_DELETE: 'solution:resourceDelete',
  RESOURCE_DELETED: 'solution:resourceDeleted',

  // 分组相关事件
  GROUP_ADDED: 'solution:groupAdded',
  GROUP_UPDATED: 'solution:groupUpdated',
  GROUP_DELETED: 'solution:groupDeleted',

  // Logo相关事件
  LOGO_UPDATED: 'solution:logoUpdated',
  LOGO_DELETED: 'solution:logoDeleted',
};

// 设置相关事件
export const SettingsEvents = {
  LOADED: 'settings:loaded',
  SAVED: 'settings:saved',
  UPDATED: 'settings:updated',
  CONTROLLED_MODE_CHANGED: 'settings:controlledModeChanged',
  ERROR: 'settings:error'
};

// UI相关事件
export const UIEvents = {
  PAGE_CHANGED: 'ui:pageChanged',
  DIALOG_OPENED: 'ui:dialogOpened',
  DIALOG_CLOSED: 'ui:dialogClosed',
  BATCH_MODE_TOGGLED: 'ui:batchModeToggled'
};
