/**
 * 命令类型常量定义
 * 这些常量用于与设备通信
 *
 * @deprecated 请使用 command-types.js 中的常量代替
 */

// 导入统一的命令类型常量
import {
  CommandType,
  DeviceStatusType,
  PlayStatusType,
  createControlCommand,
  createRestartCommand,
  createShutdownCommand,
  createPlayResourceCommand,
  createStopResourceCommand,
  createMonitorCommand,
  createGetVolumeCommand,
  createSetVolumeCommand,
  createScreenOnCommand,
  createScreenOffCommand,
  createLocateStartCommand,
  createLocateStopCommand,
  createResetViewCommand,
  createAppControlCommand,
  createDeployCommand
} from './command-types.js';

// 重新导出所有常量和函数，保持向后兼容性
export {
  CommandType,
  DeviceStatusType,
  PlayStatusType,
  createControlCommand,
  createRestartCommand,
  createShutdownCommand,
  createPlayResourceCommand,
  createStopResourceCommand,
  createMonitorCommand,
  createGetVolumeCommand,
  createSetVolumeCommand,
  createScreenOnCommand,
  createScreenOffCommand,
  createLocateStartCommand,
  createLocateStopCommand,
  createResetViewCommand,
  createAppControlCommand,
  createDeployCommand
};
