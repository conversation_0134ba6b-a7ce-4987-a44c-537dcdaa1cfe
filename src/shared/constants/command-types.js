/**
 * 统一命令类型常量定义
 * 这些常量用于与设备通信
 *
 * 注意：此文件统一了原来在 network.js 和 commands.js 中定义的命令类型常量
 */

/**
 * 命令类型常量
 * @readonly
 * @enum {number}
 */
export const CommandType = {
  // 设备状态命令
  STATUS: 1000,
  MONITOR_START_RES: 1005,
  MONITOR_STOP_RES: 1006,
  ERROR: 2099,

  // 设备控制命令
  CONTROL: 2004,
  RESTART: 2008,
  SHUTDOWN: 2009,
  MONITOR: 2011,
  MONITOR_STOP: 2012,
  GET_VOLUME: 2013,  // 获取音量
  SET_VOLUME: 2014,  // 设置音量
  SCREEN_ON: 2015,   // 点亮屏幕
  SCREEN_OFF: 2016,  // 关闭屏幕
  LOCATE_START: 2017, // 开启定位
  LOCATE_STOP: 2018,  // 停止定位
  RESET_VIEW: 2019,   // 重置视野
  APP_CONTROL: 2020,  // 应用播控

  // 播放控制命令
  PLAY: 2002,
  PAUSE: 3002,  // not used
  STOP: 2003,

  // 方案管理命令
  QUICK_ACCESS: 2024, //快捷入口显示隐藏

  // 方案管理命令
  FETCH_CONFIG: 2010,  // 获取配置命令
  DEPLOY: 2021,        // 部署命令
  DEPLOY_END: 2025,
  DEPLOY_CANCEL: 2026
};

/**
 * 设备状态类型常量
 * 用于设备状态命令的数据
 * @readonly
 * @enum {number}
 */
export const DeviceStatusType = {
  ONLINE: 1,
  OFFLINE: 0
};

/**
 * 播放状态类型常量
 * 用于设备状态命令的数据
 * @readonly
 * @enum {number}
 */
export const PlayStatusType = {
  IDLE: 0,
  PLAYING: 1,
  PAUSED: 2,
  ERROR: 3
};

/**
 * 创建控制命令
 * @param {boolean} isControlled - 是否控制
 * @param {number} deviceId - 设备ID
 * @returns {Object} 控制命令对象
 */
export function createControlCommand(isControlled, deviceId) {
  return {
    type: CommandType.CONTROL,
    data: {
      controlled: isControlled,
      id: deviceId
    }
  };
}

/**
 * 创建重启命令
 * @returns {Object} 重启命令对象
 */
export function createRestartCommand() {
  return {
    type: CommandType.RESTART,
    data: { id: Date.now() }
  };
}

/**
 * 创建关机命令
 * @returns {Object} 关机命令对象
 */
export function createShutdownCommand() {
  return {
    type: CommandType.SHUTDOWN,
    data: { id: Date.now() }
  };
}

/**
 * 创建播放资源命令
 * @param {number} resourceIndex - 资源索引
 * @param {boolean} loopPlay - 是否循环播放
 * @returns {Object} 播放资源命令对象
 */
export function createPlayResourceCommand(resourceIndex, loopPlay = false) {
  return {
    type: CommandType.PLAY,
    data: {
      index: resourceIndex,
      loopPlay: loopPlay,
      id: Date.now()
    }
  };
}

/**
 * 创建停止播放命令
 * @returns {Object} 停止播放命令对象
 */
export function createStopResourceCommand() {
  return {
    type: CommandType.STOP,
    data: { id: Date.now() }
  };
}

/**
 * 创建监控命令（用于投屏）
 * @returns {Object} 监控命令对象
 */
export function createMonitorCommand() {
  return {
    type: CommandType.MONITOR,
    data: { id: Date.now() }
  };
}

/**
 * 创建获取音量命令
 * @returns {Object} 获取音量命令对象
 */
export function createGetVolumeCommand() {
  return {
    type: CommandType.GET_VOLUME,
    data: { id: Date.now() }
  };
}

/**
 * 创建设置音量命令
 * @param {number} volume - 音量值（0-100）
 * @returns {Object} 设置音量命令对象
 */
export function createSetVolumeCommand(volume) {
  // 确保音量在有效范围内
  const validVolume = Math.max(0, Math.min(100, volume));

  return {
    type: CommandType.SET_VOLUME,
    data: {
      volume: validVolume,
      id: Date.now()
    }
  };
}

/**
 * 创建点亮屏幕命令
 * @returns {Object} 点亮屏幕命令对象
 */
export function createScreenOnCommand() {
  return {
    type: CommandType.SCREEN_ON,
    data: { id: Date.now() }
  };
}

/**
 * 创建关闭屏幕命令
 * @returns {Object} 关闭屏幕命令对象
 */
export function createScreenOffCommand() {
  return {
    type: CommandType.SCREEN_OFF,
    data: { id: Date.now() }
  };
}

/**
 * 创建开启定位命令
 * @returns {Object} 开启定位命令对象
 */
export function createLocateStartCommand() {
  return {
    type: CommandType.LOCATE_START,
    data: { id: Date.now() }
  };
}

/**
 * 创建停止定位命令
 * @returns {Object} 停止定位命令对象
 */
export function createLocateStopCommand() {
  return {
    type: CommandType.LOCATE_STOP,
    data: { id: Date.now() }
  };
}

/**
 * 创建重置视野命令
 * @returns {Object} 重置视野命令对象
 */
export function createResetViewCommand() {
  return {
    type: CommandType.RESET_VIEW,
    data: { id: Date.now() }
  };
}

/**
 * 创建应用播控命令
 * @param {string} packageName - 应用包名
 * @param {boolean} open - 是否打开应用，true表示打开，false表示关闭
 * @param {number} index - 资源索引
 * @returns {Object} 应用播控命令对象
 */
export function createAppControlCommand(packageName, open, index) {
  return {
    type: CommandType.APP_CONTROL,
    data: {
      index: index,      // 资源索引
      pkg: packageName,  // 应用包名
      open: !!open,      // 确保是布尔值
      id: Date.now()
    }
  };
}

/**
 * 创建部署命令
 * @param {string} solutionPath - 方案路径
 * @param {boolean} [incremental=true] - 是否为增量部署
 * @returns {Object} 部署命令对象
 */
export function createDeployCommand(solutionPath, incremental = true) {
  return {
    type: CommandType.DEPLOY,
    data: {
      path: solutionPath,
      incremental: incremental,
      id: Date.now()
    }
  };
}

/**
 * 创建获取配置命令
 * @returns {Object} 获取配置命令对象
 */
export function createFetchConfigCommand() {
  return {
    type: CommandType.FETCH_CONFIG,
    data: { id: Date.now() }
  };
}
