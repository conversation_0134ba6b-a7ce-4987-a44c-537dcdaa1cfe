/**
 * 事件总线类
 * 用于组件之间的通信
 */
export class EventBus {
  constructor() {
    this.listeners = new Map();
  }

  /**
   * 注册事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   * @returns {Function} 用于取消监听的函数
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);

    // 返回一个取消监听的函数
    return () => this.off(event, callback);
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    if (!this.listeners.has(event)) return;

    const callbacks = this.listeners.get(event);
    const index = callbacks.indexOf(callback);

    if (index !== -1) {
      callbacks.splice(index, 1);
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   * @returns {Promise<*>} 最后一个回调函数的返回值
   */
  async emit(event, data) {
    if (!this.listeners.has(event)) return;

    // 创建回调函数的副本，以防在回调中修改监听器列表
    const callbacks = [...this.listeners.get(event)];

    let result;
    for (const callback of callbacks) {
      try {
        result = await callback(data);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    }

    return result;
  }

  /**
   * 只监听一次事件
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   * @returns {Function} 用于取消监听的函数
   */
  once(event, callback) {
    const onceCallback = (data) => {
      this.off(event, onceCallback);
      callback(data);
    };

    return this.on(event, onceCallback);
  }

  /**
   * 移除所有事件监听器
   * @param {string} [event] - 事件名称，如果不提供则移除所有事件的监听器
   */
  removeAllListeners(event) {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }
}
