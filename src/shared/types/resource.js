import { DEFAULT_GROUPS } from '../constants/defaults.js';

/**
 * 文件类型枚举
 * 按照指定的编号规则：
 * - 类型 0~3：为图片
 * - 类型 4~6：为2D视频
 * - 类型 7~11：为3D视频
 * - 类型 15：是APK
 * @enum {number}
 */
export const FileType = {
  // 图片类型 (0~3)
  IMAGE_2D_PLANE: 0,      // 2D平面图片
  IMAGE_CUBE_MAP: 1,      // 3D立方体贴图
  IMAGE_VR_360: 2,      // 3D-360全景图片
  IMAGE_VR_180: 3,      // 3D-180全景图片

  // 2D视频类型 (4~6)
  VIDEO_2D_PLANE: 4,      // 2D平面视频
  VIDEO_2D_180: 5,      // 2D-180全景视频
  VIDEO_2D_360: 6,   // 2D全景视频

  // 3D视频类型 (7~11)
  //VIDEO_3D_TBNormal: 7,      // 3D平面视频
  //VIDEO_3D_TB180: 8,
  VIDEO_3D_TB360: 9,
  VIDEO_3D_LRNormal: 10,
  VIDEO_3D_LR180: 11,

  // 应用类型
  APP: 15                // 应用文件(APK)
};

/**
 * 文件类型范围定义
 * 用于判断文件类型所属的类别
 * @type {Object}
 */
export const FileTypeRanges = {
  // 图片类型范围
  IMAGE: {
    MIN: FileType.IMAGE_2D_PLANE,
    MAX: FileType.IMAGE_VR_180
  },
  // 2D视频类型范围
  VIDEO_2D: {
    MIN: FileType.VIDEO_2D_PLANE,
    MAX: FileType.VIDEO_2D_360
  },
  // 3D视频类型范围
  VIDEO_3D: {
    MIN: FileType.VIDEO_3D_TB360,
    MAX: FileType.VIDEO_3D_LR180
  },
  // 所有视频类型（2D和3D）
  VIDEO_ALL: {
    RANGES: [
      { MIN: FileType.VIDEO_2D_PLANE, MAX: FileType.VIDEO_2D_360 },
      { MIN: FileType.VIDEO_3D_TB360, MAX: FileType.VIDEO_3D_LR180 }
    ]
  }
};

/**
 * 主要文件类型分组
 * 用于将详细的文件类型归类为主要类别
 * @enum {string}
 */
export const MainFileType = {
  IMAGE: 'image',    // 图片类型 (0~3)
  VIDEO: 'video',    // 视频类型 (4~12)，包含2D和3D视频
  APP: 'app'         // 应用类型 (15)
};

/**
 * 文件扩展名映射
 * 定义各种文件类型对应的扩展名
 * @type {Object}
 */
export const FileExtensions = {
  VIDEO: ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v', '3gp', 'm3u8'],  // 添加 m3u8 格式
  IMAGE: ['jpg', 'jpeg', 'png', 'bmp'],
  APP: ['apk'],  // 应用文件类型
  OTHER: ['*']   // 所有文件类型
};

/**
 * 带点的文件扩展名映射（兼容旧代码）
 * @type {Object}
 */
export const DottedFileExtensions = {
  VIDEO: FileExtensions.VIDEO.map(ext => `.${ext}`),
  IMAGE: FileExtensions.IMAGE.map(ext => `.${ext}`),
  APP: FileExtensions.APP.map(ext => `.${ext}`),  // 应用文件类型
  OTHER: FileExtensions.OTHER.map(ext => `.${ext}`)  // 所有文件类型
};

/**
 * 文件类型目录前缀映射
 * 定义各种文件类型对应的目录前缀
 * @type {Object}
 */
export const DirectoryPrefixes = {
  [FileType.VIDEO_2D_PLANE]: 'video',
  [FileType.IMAGE_2D_PLANE]: 'image',
  [FileType.APP]: 'app'  // 应用文件类型
};

/**
 * 文件类型标签映射
 * 定义各种文件类型对应的显示标签
 * @type {Object}
 */
export const TypeLabels = {
  // 图片类型 (0~3)
  [FileType.IMAGE_2D_PLANE]: '2D平面',
  [FileType.IMAGE_CUBE_MAP]: '立方体投影',
  [FileType.IMAGE_VR_360]: '2D 360',
  [FileType.IMAGE_VR_180]: '2D 180',

  // 2D视频类型 (4~6)
  [FileType.VIDEO_2D_PLANE]: '2D平面',
  [FileType.VIDEO_2D_180]: '2D 180',
  [FileType.VIDEO_2D_360]: '2D 360',

  // 3D视频类型 (7~11)
  // [FileType.VIDEO_3D_TBNormal]: '3D平面视频',
  // [FileType.VIDEO_3D_TB180]: '3D-180全景视频',
  [FileType.VIDEO_3D_TB360]: '3D 360',
  [FileType.VIDEO_3D_LRNormal]: '3D平面',
  [FileType.VIDEO_3D_LR180]: '3D 180',

  // 应用类型
  [FileType.APP]: '应用程序(APK)'
};

/**
 * 主要文件类型标签映射
 * 定义主要文件类型对应的显示标签
 * @type {Object}
 */
export const MainTypeLabels = {
  [MainFileType.IMAGE]: '图片',
  [MainFileType.VIDEO]: '视频',
  [MainFileType.APP]: '应用'
};

/**
 * 文件类型映射
 * 定义各种文件类型对应的字符串标识符
 * @type {Object}
 */
export const FileTypeKeys = {
  // 图片类型 (0~3)
  [FileType.IMAGE_2D_PLANE]: 'image',
  [FileType.IMAGE_CUBE_MAP]: 'image',
  [FileType.IMAGE_VR_360]: 'image',
  [FileType.IMAGE_VR_180]: 'image',

  // 2D视频类型 (4~6)
  [FileType.VIDEO_2D_PLANE]: 'video',
  [FileType.VIDEO_2D_180]: 'video',
  [FileType.VIDEO_2D_360]: 'video',

  // 3D视频类型 (7~11)
  // [FileType.VIDEO_3D_TBNormal]: 'video',
  //[FileType.VIDEO_3D_TB180]: 'video',
  [FileType.VIDEO_3D_TB360]: 'video',
  [FileType.VIDEO_3D_LRNormal]: 'video',
  [FileType.VIDEO_3D_LR180]: 'video',

  // 应用类型
  [FileType.APP]: 'app'
};

/**
 * 反向文件类型映射
 * 定义字符串标识符对应的文件类型
 * @type {Object}
 */
export const KeyToFileType = {
  'video': FileType.VIDEO_2D_PLANE,
  'image': FileType.IMAGE_2D_PLANE,
  'app': FileType.APP  // 应用文件类型
};

/**
 * 资源类型定义
 * @typedef {Object} Resource
 * @property {string} MD5 - 文件MD5值
 * @property {string} describe - 资源描述
 * @property {string} fileName - 文件名
 * @property {Array<string>} groups - 所属分组
 * @property {number} index - 索引
 * @property {string} path - 文件路径
 * @property {string} poster - 封面/缩略图路径（如果用户上传了封面则使用封面，否则自动生成缩略图）
 * @property {string} [thumbnail] - 缩略图路径（已废弃，保留此字段仅为向后兼容）
 * @property {string} showName - 显示名称
 * @property {FileType} type - 文件类型
 * @property {number} subType - 文件子类型（对于视频和图片）
 */

/**
 * 创建资源对象
 * @param {Object} data - 资源数据
 * @returns {Resource} 资源对象
 */
export function createResource(data) {
  // 使用导出的 FileType 枚举
  // 保留原始类型，如果没有类型则使用默认值
  let resourceType = data.type;
  if (resourceType === undefined || resourceType === null) {
    resourceType = FileType.IMAGE_2D_PLANE; // 默认为图片类型
  }

  // 处理子类型
  let subType = data.subType;
  if (subType === undefined || subType === null) {
    // 根据主类型设置默认子类型
    if (isVideoType(resourceType)) {
      subType = 0;  // 默认为2D平面视频
    } else if (isImageType(resourceType)) {
      subType = 0;  // 默认为2D平面图片
    } else {
      subType = 0;  // 默认子类型为0
    }
  }

  // 为了向后兼容，如果有 thumbnail 但没有 poster，则使用 thumbnail 作为 poster
  const posterValue = data.poster || data.thumbnail || '';

  // 创建基本资源对象，不包含 thumbnail 字段
  const resource = {
    MD5: data.MD5 || '',
    describe: data.describe || '',
    fileName: data.fileName || '',
    groups: data.groups || DEFAULT_GROUPS,
    index: data.index || 0,
    path: data.path || '',
    poster: posterValue,
    showName: data.showName || data.fileName || '',
    type: resourceType,
    subType: subType,
    pkg: data.pkg || ''  // 添加 pkg 字段，用于存储 APK 包名
  };

  // 只有在读取现有资源时才保留 thumbnail 字段（不写入新的 thumbnail 字段）
  // 这样可以确保新创建的资源不会包含 thumbnail 字段，但仍然可以读取旧资源中的 thumbnail 字段
  if (data.thumbnail) {
    // 为了向后兼容，保留 thumbnail 字段，但它已经被废弃
    // 这个字段只在内存中使用，不会写入到 config.dat 文件中
    Object.defineProperty(resource, 'thumbnail', {
      value: data.thumbnail,
      enumerable: false, // 设置为不可枚举，这样在 JSON.stringify 时不会包含此字段
      configurable: true,
      writable: true
    });
  }

  return resource;
}

/**
 * 获取主要文件类型
 * 根据详细文件类型返回主要类别
 * @param {FileType} fileType - 详细文件类型
 * @returns {string} 主要文件类型
 */
export function getMainFileType(fileType) {
  // 图片类型
  if (isImageType(fileType)) {
    return MainFileType.IMAGE;
  }

  // 视频类型，包含2D和3D视频
  if (isVideoType(fileType)) {
    return MainFileType.VIDEO;
  }

  // 应用类型
  if (fileType === FileType.APP) {
    return MainFileType.APP;
  }

  // 默认返回图片类型（作为兜底）
  return MainFileType.IMAGE;
}

/**
 * 判断文件类型是否为图片
 * @param {FileType} fileType - 文件类型
 * @returns {boolean} 是否为图片
 */
export function isImageType(fileType) {
  return fileType >= FileTypeRanges.IMAGE.MIN && fileType <= FileTypeRanges.IMAGE.MAX;
}

/**
 * 判断文件类型是否为视频
 * @param {FileType} fileType - 文件类型
 * @returns {boolean} 是否为视频
 */
export function isVideoType(fileType) {
  return FileTypeRanges.VIDEO_ALL.RANGES.some(range =>
    fileType >= range.MIN && fileType <= range.MAX
  );
}

/**
 * 判断文件类型是否为2D视频
 * @param {FileType} fileType - 文件类型
 * @returns {boolean} 是否为2D视频
 */
export function is2DVideoType(fileType) {
  return fileType >= FileTypeRanges.VIDEO_2D.MIN && fileType <= FileTypeRanges.VIDEO_2D.MAX;
}

/**
 * 判断文件类型是否为3D视频
 * @param {FileType} fileType - 文件类型
 * @returns {boolean} 是否为3D视频
 */
export function is3DVideoType(fileType) {
  return fileType >= FileTypeRanges.VIDEO_3D.MIN && fileType <= FileTypeRanges.VIDEO_3D.MAX;
}

/**
 * 判断文件类型是否为应用
 * @param {FileType} fileType - 文件类型
 * @returns {boolean} 是否为应用
 */
export function isAppType(fileType) {
  return fileType === FileType.APP;
}

/**
 * 根据文件扩展名确定文件类型
 * @param {string} fileName - 文件名
 * @returns {number} 文件类型
 */
export function determineFileType(fileName) {
  const ext = fileName.split('.').pop().toLowerCase();

  if (FileExtensions.IMAGE.includes(ext)) {
    return FileType.IMAGE_2D_PLANE; // 默认为2D平面图片
  }

  if (FileExtensions.VIDEO.includes(ext)) {
    return FileType.VIDEO_2D_PLANE; // 默认为2D平面视频
  }

  if (FileExtensions.APP.includes(ext)) {
    return FileType.APP;
  }

  // 默认返回图片类型（作为兜底）
  return FileType.IMAGE_2D_PLANE;
}

/**
 * 获取文件类型显示标签
 * @param {FileType} fileType - 文件类型
 * @returns {string} 显示标签
 */
export function getFileTypeLabel(fileType) {
  if (isImageType(fileType)) {
    return '图片';
  }

  if (is2DVideoType(fileType)) {
    return '2D视频';
  }

  if (is3DVideoType(fileType)) {
    return '3D视频';
  }

  if (isAppType(fileType)) {
    return '应用';
  }

  // 默认返回图片（作为兜底）
  return '图片';
}

/**
 * 文件类型列表映射
 * 定义各种主要文件类型对应的详细类型列表
 * @type {Object}
 */
export const FileTypesByMainType = {
  [MainFileType.IMAGE]: [
    FileType.IMAGE_2D_PLANE,
    FileType.IMAGE_CUBE_MAP,
    FileType.IMAGE_VR_360,
    FileType.IMAGE_VR_180
  ],
  [MainFileType.VIDEO]: [
    // 2D视频
    FileType.VIDEO_2D_PLANE,
    FileType.VIDEO_2D_180,
    FileType.VIDEO_2D_360,
    // 3D视频
    //FileType.VIDEO_3D_TBNormal,
    //FileType.VIDEO_3D_TB180,
    FileType.VIDEO_3D_TB360,
    FileType.VIDEO_3D_LRNormal,
    FileType.VIDEO_3D_LR180
  ],
  [MainFileType.APP]: [
    FileType.APP
  ]
};

/**
 * 按主要类型获取文件类型列表
 * @param {string} mainType - 主要文件类型
 * @returns {Array<number>} 文件类型列表
 */
export function getFileTypesByMainType(mainType) {
  return FileTypesByMainType[mainType] || [FileType.IMAGE_2D_PLANE];
}
