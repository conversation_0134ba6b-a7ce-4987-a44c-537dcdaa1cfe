/**
 * 设备状态枚举
 * @enum {number}
 */
export const DeviceStatus = {
  OFFLINE: 0,
  ONLINE: 1,
  BUSY: 2,
  ERROR: 3
};

/**
 * 播放状态枚举（字符串形式）
 * @enum {string}
 */
export const PlayStatus = {
  IDLE: 'idle',
  PLAYING: 'playing',
  PAUSED: 'paused',
  ERROR: 'error'
};

/**
 * 播放状态枚举（数字形式）
 * @enum {number}
 */
export const PlayStatusCode = {
  IDLE: 0,
  PLAYING: 1,
  PAUSED: 2,
  ERROR: 3
};

/**
 * 播放状态标签映射
 * @type {Object}
 */
export const PlayStatusLabels = {
  [PlayStatusCode.IDLE]: '空闲',
  [PlayStatusCode.PLAYING]: '播放中',
  [PlayStatusCode.PAUSED]: '暂停',
  [PlayStatusCode.ERROR]: '错误'
};

/**
 * 设备类型定义
 * @typedef {Object} Device
 * @property {string} sn - 设备序列号
 * @property {number} id - 设备ID
 * @property {boolean} isOnline - 设备是否在线
 * @property {DeviceStatus} deviceStatus - 设备状态（0=离线，1=在线）
 * @property {string} battery - 电池状态
 * @property {boolean} onuse - 是否在使用中
 * @property {string} playStatus - 播放状态
 * @property {boolean} controlled - 是否被控制
 * @property {string} ip - 设备IP地址（可选）
 * @property {number} addedAt - 添加时间戳
 */

/**
 * 创建设备对象
 * @param {Object} data - 设备数据
 * @returns {Device} 设备对象
 */
export function createDevice(data) {
  return {
    sn: data.sn || '',
    id: data.id || 0,
    isOnline: data.isOnline || false,
    deviceStatus: data.deviceStatus || DeviceStatus.OFFLINE,
    battery: data.battery || '-',
    onuse: data.onuse || false,
    playStatus: data.playStatus || '-',
    controlled: data.controlled || false,
    ip: data.ip || '',
    addedAt: data.addedAt || Date.now()
  };
}

/**
 * 设备组类型定义
 * @typedef {Object} DeviceGroup
 * @property {string} id - 组ID
 * @property {string} name - 组名称
 * @property {string} description - 组描述
 * @property {Array<string>} devices - 组内设备SN列表
 * @property {number} createdAt - 创建时间戳
 * @property {number} updatedAt - 更新时间戳
 */

/**
 * 创建设备组对象
 * @param {Object} data - 设备组数据
 * @returns {DeviceGroup} 设备组对象
 */
export function createDeviceGroup(data) {
  return {
    id: data.id || generateId(),
    name: data.name || '新建分组',
    description: data.description || '',
    devices: data.devices || [],
    createdAt: data.createdAt || Date.now(),
    updatedAt: data.updatedAt || Date.now()
  };
}

/**
 * 生成唯一ID
 * @private
 * @returns {string} 唯一ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}
