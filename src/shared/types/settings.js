/**
 * 设置类型定义
 * @typedef {Object} Settings
 * @property {boolean} controlledMode - 是否启用控制模式
 * @property {Object} basic - 基本设置
 * @property {string} basic.serverName - 服务器名称
 * @property {number} basic.udpPort - UDP端口
 * @property {number} basic.wsPort - WebSocket端口
 * @property {Object} advanced - 高级设置
 * @property {number} advanced.heartbeatInterval - 心跳间隔（毫秒）
 */

/**
 * 创建设置对象
 * @param {Object} data - 设置数据
 * @returns {Settings} 设置对象
 */
export function createSettings(data = {}) {
  return {
    controlledMode: data.controlledMode !== undefined ? data.controlledMode : false,
    basic: {
      serverName: data.basic?.serverName || '',
      udpPort: data.basic?.udpPort || 9944,
      wsPort: data.basic?.wsPort || 50208
    },
    advanced: {
      heartbeatInterval: data.advanced?.heartbeatInterval || 10000
    }
  };
}
