import { DEFAULT_GROUPS } from '../constants/defaults.js';

/**
 * 方案类型定义
 * @typedef {Object} Solution
 * @property {string} UUID - 方案唯一标识
 * @property {Array<string>} groups - 分组列表
 * @property {Array<import('./resource').Resource>} list - 资源列表
 * @property {string} pcVersion - PC版本号
 * @property {number} port - 端口号
 * @property {string} name - 方案名称
 * @property {string} description - 方案描述
 * @property {number} createdAt - 创建时间戳
 * @property {number} updatedAt - 更新时间戳
 */

/**
 * 创建方案对象
 * @param {Object} data - 方案数据
 * @returns {Solution} 方案对象
 */
export function createSolution(data = {}) {
  // 默认版本号
  let appVersion = '1.0.0';
  try {
    // 在渲染进程中，我们可以通过window.electronAPI获取版本信息
    if (typeof window !== 'undefined' && window.electronAPI && window.electronAPI.getVersion) {
      // 注意：这是异步的，但createSolution是同步函数，所以我们只能使用默认值
      // 实际版本会在后续通过ConfigService更新
      window.electronAPI.getVersion().then(version => {
        appVersion = version;
      }).catch(() => {});
    }
  } catch (e) {
    console.error('获取应用版本失败', e);
  }

  return {
    UUID: data.UUID || (typeof crypto !== 'undefined' && crypto.randomUUID ? crypto.randomUUID() : `solution-${Date.now()}`),
    groups: data.groups || DEFAULT_GROUPS,
    list: data.list || [],
    pcVersion: data.pcVersion || appVersion,
    port: data.port || 50208,
    name: data.name || '新方案',
    description: data.description || '',
    createdAt: data.createdAt || Date.now(),
    updatedAt: data.updatedAt || Date.now(),
    logo: data.logo || ''
  };
}
