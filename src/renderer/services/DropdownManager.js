/**
 * 下拉菜单管理器
 * 使用事件委托处理所有下拉菜单的打开和关闭
 */
class DropdownManager {
  constructor() {
    this.initialized = false;
    this.activeDropdown = null;
  }

  /**
   * 初始化下拉菜单管理器
   */
  init() {
    if (this.initialized) return;

    // 添加单个全局点击事件监听器，处理所有下拉菜单相关的点击
    this.handleClick = this.handleClick.bind(this);
    document.addEventListener('click', this.handleClick);

    this.initialized = true;
    console.log('DropdownManager: 初始化完成');
  }

  /**
   * 处理所有点击事件
   * @param {Event} e - 点击事件
   * @private
   */
  handleClick(e) {
    const dropdownButton = e.target.closest('.dropdown-button');

    // 如果点击的是下拉菜单按钮
    if (dropdownButton) {
      e.stopPropagation(); // 阻止事件冒泡
      this.handleDropdownButtonClick(dropdownButton);
      return;
    }

    // 如果点击的不是下拉菜单区域，关闭所有下拉菜单
    if (!e.target.closest('.dropdown-content')) {
      this.closeAllDropdowns();
    }
  }

  /**
   * 处理下拉菜单按钮点击事件
   * @param {HTMLElement} dropdownButton - 下拉菜单按钮元素
   * @private
   */
  handleDropdownButtonClick(dropdownButton) {
    const dropdown = dropdownButton.closest('.dropdown');
    const dropdownContent = dropdown.querySelector('.dropdown-content');

    if (!dropdownContent) return;

    // 如果当前下拉菜单已经打开，关闭它
    if (dropdownContent.classList.contains('show')) {
      dropdownContent.classList.remove('show');
      this.activeDropdown = null;
      return;
    }

    // 关闭其他打开的下拉菜单
    this.closeAllDropdowns();

    // 打开当前下拉菜单
    dropdownContent.classList.add('show');
    this.activeDropdown = dropdownContent;
  }

  /**
   * 关闭所有下拉菜单
   * @private
   */
  closeAllDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown-content.show');
    dropdowns.forEach(dropdown => dropdown.classList.remove('show'));
    this.activeDropdown = null;
  }

  /**
   * 销毁下拉菜单管理器
   */
  destroy() {
    if (!this.initialized) return;

    document.removeEventListener('click', this.handleClick);

    this.initialized = false;
    console.log('DropdownManager: 已销毁');
  }
}

// 创建单例实例
const dropdownManager = new DropdownManager();

export default dropdownManager;
