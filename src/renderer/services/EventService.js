import { EventBus } from '../../shared/utils/EventBus.js';
import { DeviceEvents } from '../../shared/constants/events.js';

/**
 * 事件服务类
 * 处理主进程发送的事件
 */
export class EventService {
  constructor() {
    this.eventBus = new EventBus();
    this.setupEventListeners();
  }

  /**
   * 设置事件监听器
   * @private
   */
  setupEventListeners() {
    // 设备连接事件
    window.electronAPI.onDeviceConnected((data) => {
      this.eventBus.emit(DeviceEvents.CONNECTED, data);
    });

    // 设备断开连接事件
    window.electronAPI.onDeviceDisconnected((ip) => {
      this.eventBus.emit(DeviceEvents.DISCONNECTED, ip);
    });

    // 设备状态更新事件
    window.electronAPI.onDeviceStatusUpdate((data) => {
      this.eventBus.emit(DeviceEvents.STATUS_UPDATED, data);
    });

    // 传输进度事件
    window.electronAPI.onTransferProgress((data) => {
      this.eventBus.emit('transfer:progress', data);
    });

    // 部署错误事件
    window.electronAPI.onDeployError((data) => {
      this.eventBus.emit('deploy:error', data);
    });
  }

  /**
   * 获取事件总线实例
   * @returns {EventBus} 事件总线实例
   */
  getEventBus() {
    return this.eventBus;
  }

  /**
   * 注册事件监听器
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   * @returns {Function} 用于取消监听的函数
   */
  on(event, callback) {
    return this.eventBus.on(event, callback);
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   * @returns {Promise<*>} 事件处理函数的返回值
   */
  async emit(event, data) {
    return await this.eventBus.emit(event, data);
  }
}

// 创建单例
const eventService = new EventService();
export default eventService;
