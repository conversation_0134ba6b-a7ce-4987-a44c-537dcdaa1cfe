import { createApp, h } from 'vue';
import ResourceDialog from '../components/ResourceDialog.vue';
import ResourceTypeDialog from '../components/ResourceTypeDialog.vue';
import { DEFAULT_GROUPS } from '../../../shared/constants/defaults.js';
import { MainFileType } from '../../../shared/types/resource.js';

/**
 * 资源对话框服务
 * 提供显示资源添加/编辑对话框的功能
 */
class ResourceDialogService {
  /**
   * 显示资源对话框
   * @param {Object} options - 配置选项
   * @param {Object} options.resource - 要编辑的资源对象，如果为空则为添加模式
   * @param {Array} options.categories - 可用的分类列表
   * @param {String} options.selectedCategory - 当前选中的分类（用于添加模式）
   * @returns {Promise} 返回一个Promise，resolve时传入更新后的资源对象，或者在取消时返回null
   */
  show(options = {}) {
    return new Promise((resolve) => {
      // 如果是编辑模式，直接显示资源对话框
      if (options.resource && Object.keys(options.resource).length > 0) {
        this.showResourceDialog(options, resolve);
        return;
      }

      // 如果是添加模式，先显示类型选择对话框
      this.showTypeDialog(options, resolve);
    });
  }

  /**
   * 显示类型选择对话框
   * @private
   */
  showTypeDialog(options, resolve) {
    // 创建一个容器元素
    const container = document.createElement('div');
    document.body.appendChild(container);

    // 创建一个Vue应用
    const app = createApp({
      render: () => {
        return h(ResourceTypeDialog, {
          onSelect: (type) => {
            console.log('类型选择对话框选择的类型:', type, typeof type);
            // 销毁类型选择对话框
            destroyApp();
            // 显示资源对话框，并传入选中的类型
            this.showResourceDialog({
              ...options,
              selectedType: type
            }, resolve);
          },
          onClose: () => {
            resolve(null);
            destroyApp();
          }
        });
      }
    });

    // 挂载应用
    const instance = app.mount(container);

    // 销毁应用的函数
    const destroyApp = () => {
      try {
        app.unmount();
        if (document.body.contains(container)) {
          document.body.removeChild(container);
        }
      } catch (err) {
        console.error('ResourceDialogService: 销毁类型选择对话框失败', err);
      }
    };
  }

  /**
   * 显示资源对话框
   * @private
   */
  showResourceDialog(options, resolve) {
    // 创建一个容器元素
    const container = document.createElement('div');
    document.body.appendChild(container);

    // 创建一个Vue应用
    const app = createApp({
      render: () => {
        console.log('显示资源对话框时的类型:', options.selectedType, typeof options.selectedType);
        return h('div', {
          class: 'dialog-overlay',
          onClick: (e) => {
            // 不再响应点击遮罩层的事件
          }
        }, [
          h(ResourceDialog, {
            resource: options.resource || {},
            categories: options.categories || DEFAULT_GROUPS,
            selectedCategory: options.selectedCategory || '',
            selectedType: options.selectedType || MainFileType.VIDEO,
            onSave: (updatedResource) => {
              // 保存按钮点击事件
              // 确保保留原始资源的关键字段
              if (options.resource && Object.keys(options.resource).length > 0) {
                // 确保保留 index 字段
                if (options.resource.index !== undefined && updatedResource.index === undefined) {
                  updatedResource.index = options.resource.index;
                }
                // 确保保留 MD5 字段
                if (options.resource.MD5 && !updatedResource.MD5) {
                  updatedResource.MD5 = options.resource.MD5;
                }
                // 确保保留 path 字段
                if (options.resource.path && !updatedResource.path) {
                  updatedResource.path = options.resource.path;
                }
              }

              resolve(updatedResource);
              // 销毁应用
              destroyApp();
            },
            onClose: () => {
              // 关闭按钮点击事件
              console.log('ResourceDialogService: 关闭对话框');
              resolve(null);
              // 销毁应用
              destroyApp();
            }
          })
        ]);
      }
    });

    // 挂载应用
    const instance = app.mount(container);

    // 销毁应用的函数
    const destroyApp = () => {
      try {
        app.unmount();
        if (document.body.contains(container)) {
          document.body.removeChild(container);
        }
      } catch (err) {
        console.error('ResourceDialogService: 销毁资源对话框失败', err);
      }
    };
  }
}

// 添加全局样式
const style = document.createElement('style');
style.textContent = `
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 暗色主题下的对话框遮罩层 */
[data-theme="dark"] .dialog-overlay {
  background-color: rgba(0, 0, 0, 0.7);
}
`;
document.head.appendChild(style);

// 导出单例
export default new ResourceDialogService();
