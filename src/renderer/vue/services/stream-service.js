/**
 * 流媒体服务
 * 提供流媒体相关的公共方法
 */

// 获取 electronAPI
const getElectronAPI = () => {
  if (window && window.electronAPI) {
    return window.electronAPI;
  }
  console.error('electronAPI 不可用');
  return null;
};

/**
 * 停止所有投屏
 * @param {boolean} [waitForCompletion=false] - 是否等待所有停止操作完成
 * @param {number} [delayBetweenStops=0] - 停止操作之间的延迟时间（毫秒）
 * @returns {Promise<void>}
 */
export const stopAllScreenCasts = async (waitForCompletion = false, delayBetweenStops = 0) => {
  try {
    const electronAPI = getElectronAPI();
    if (!electronAPI) return;

    // 获取所有流
    const streams = await electronAPI.getAllStreams();

    // 如果有正在进行的投屏，停止它们
    if (streams && streams.length > 0) {
      console.log(`停止所有投屏，共 ${streams.length} 个`);

      // 创建停止操作的Promise数组
      const stopPromises = [];

      // 逐个停止流
      for (const stream of streams) {
        const stopPromise = (async () => {
          try {
            // 如果设置了延迟，则等待指定时间
            if (delayBetweenStops > 0) {
              await new Promise(resolve => setTimeout(resolve, delayBetweenStops));
            }

            await electronAPI.stopRtspStream(stream.sn);
            console.log(`已停止设备 ${stream.sn} 的投屏`);
            return { sn: stream.sn, success: true };
          } catch (error) {
            console.error(`停止设备 ${stream.sn} 的投屏失败:`, error);
            return { sn: stream.sn, success: false, error };
          }
        })();

        stopPromises.push(stopPromise);
      }

      // 如果不需要等待完成，则在后台处理
      if (!waitForCompletion) {
        // 在后台处理，不阻塞当前流程
        Promise.all(stopPromises).then(results => {
          const successCount = results.filter(r => r.success).length;
          const failCount = results.length - successCount;
          console.log(`后台停止投屏完成: 成功 ${successCount} 个, 失败 ${failCount} 个`);
        }).catch(error => {
          console.error('后台停止投屏失败:', error);
        });

        // 立即返回，不等待完成
        return;
      }

      // 等待所有停止操作完成
      const results = await Promise.all(stopPromises);
      const successCount = results.filter(r => r.success).length;
      const failCount = results.length - successCount;

      console.log(`停止投屏完成: 成功 ${successCount} 个, 失败 ${failCount} 个`);
    }
  } catch (error) {
    console.error('停止所有投屏失败:', error);
  }
};

/**
 * 停止指定设备的投屏
 * @param {string} sn - 设备序列号
 * @returns {Promise<boolean>} 是否成功停止
 */
export const stopScreenCast = async (sn) => {
  try {
    const electronAPI = getElectronAPI();
    if (!electronAPI) return false;

    // 停止流
    await electronAPI.stopRtspStream(sn);
    console.log(`已停止设备 ${sn} 的投屏`);
    return true;
  } catch (error) {
    console.error(`停止设备 ${sn} 的投屏失败:`, error);
    return false;
  }
};

/**
 * 获取所有正在投屏的设备
 * @returns {Promise<Array>} 正在投屏的设备列表
 */
export const getScreenCastingDevices = async () => {
  try {
    const electronAPI = getElectronAPI();
    if (!electronAPI) return [];

    // 获取所有流
    const streams = await electronAPI.getAllStreams();
    return streams || [];
  } catch (error) {
    console.error('获取正在投屏的设备失败:', error);
    return [];
  }
};

export default {
  stopAllScreenCasts,
  stopScreenCast,
  getScreenCastingDevices
};
