import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './components/App.vue';
import router from './router';
import { electronPlugin } from './plugins/electron';
import { alertPlugin } from './plugins/alert';

// 引入 Element Plus 组件库和样式
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// 如果使用中文语言包（可选）
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import './styles/global.css';
import './styles/element-override.css';

// 初始化主题
const initTheme = () => {
  // 强制使用亮色主题
  document.documentElement.setAttribute('data-theme', 'light');
  localStorage.setItem('theme', 'light');
};

// 禁用拖拽功能
const disableDragAndDrop = () => {
  document.addEventListener('dragstart', (e) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  }, true);

  document.addEventListener('drop', (e) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  }, true);

  document.addEventListener('dragover', (e) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  }, true);

  // 禁用图片拖拽
  document.addEventListener('mousedown', (e) => {
    if (e.target.tagName === 'IMG') {
      e.preventDefault();
      return false;
    }
  }, true);
};

// 创建应用实例
const app = createApp(App);

// 使用插件
app.use(createPinia());
app.use(router);
app.use(electronPlugin);
app.use(alertPlugin);
app.use(ElementPlus, {
  locale: zhCn,
});

// 初始化主题
initTheme();

// 禁用拖拽功能
disableDragAndDrop();

// 挂载应用
app.mount('#app');
