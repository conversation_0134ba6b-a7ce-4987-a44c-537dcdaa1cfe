import { createApp, h } from 'vue';
import AlertDialog from '../components/AlertDialog.vue';
import CustomDialog from '../components/CustomDialog.vue';

// 存储当前活动的对话框实例
let currentDialogInstance = null;
let currentDialogContainer = null;

// 创建一个全局的提示对话框服务
const alertService = {
  // 关闭当前对话框
  closeDialog() {
    if (currentDialogInstance && currentDialogContainer) {
      console.log('[AlertService] 手动关闭对话框');
      currentDialogInstance.unmount();
      document.body.removeChild(currentDialogContainer);
      currentDialogInstance = null;
      currentDialogContainer = null;
    }
  },

  // 显示提示对话框
  alert(options) {
    console.log('[AlertService] 创建对话框', options.title, options.message);
    return new Promise((resolve) => {
      // 创建一个容器元素
      const container = document.createElement('div');
      document.body.appendChild(container);

      // 创建一个Vue应用
      const app = createApp({
        render() {
          return h(AlertDialog, {
            ref: 'alertDialog',
            title: options.title || '提示',
            message: options.message,
            confirmButtonText: options.confirmButtonText || '确定',
            cancelButtonText: options.cancelButtonText || '取消',
            showCancelButton: options.showCancelButton || false,
            showCloseButton: options.showCloseButton !== false,
            closeOnOverlayClick: options.closeOnOverlayClick === true,
            isLoading: options.isLoading || false,
            onConfirm: () => {
              // 确认按钮点击事件
              resolve(true);
              // 销毁应用
              destroyApp();
            },
            onCancel: () => {
              // 取消按钮点击事件
              resolve(false);
              // 销毁应用
              destroyApp();
            },
            onClose: () => {
              // 关闭按钮点击事件
              resolve(false);
              // 销毁应用
              destroyApp();
            }
          });
        },
        mounted() {
          // 组件挂载后，打开对话框
          this.$refs.alertDialog.open();
        }
      });

      // 关闭之前的对话框
      this.closeDialog();

      // 挂载应用
      const instance = app.mount(container);

      // 保存当前对话框实例
      currentDialogInstance = app;
      currentDialogContainer = container;

      // 销毁应用的函数
      const destroyApp = () => {
        console.log('[AlertService] 销毁对话框', options.title, options.message);
        app.unmount();
        document.body.removeChild(container);

        // 清除当前对话框实例
        if (currentDialogInstance === app) {
          currentDialogInstance = null;
          currentDialogContainer = null;
        }
      };
    });
  },

  // 显示确认对话框
  confirm(options) {
    return this.alert({
      ...options,
      showCancelButton: true
    });
  },

  // 显示自定义组件对话框
  customDialog(options) {
    return new Promise((resolve) => {
      // 创建一个容器元素
      const container = document.createElement('div');
      document.body.appendChild(container);

      // 创建一个Vue应用
      const app = createApp({
        render() {
          return h(CustomDialog, {
            ref: 'customDialog',
            title: options.title || '对话框',
            component: options.component,
            componentProps: options.props || {},
            modelValue: options.modelValue,
            width: options.width,
            confirmButtonText: options.confirmButtonText || '确定',
            cancelButtonText: options.cancelButtonText || '取消',
            showClose: options.showClose !== false,
            showConfirmButton: options.showConfirmButton !== false,
            showCancelButton: options.showCancelButton !== false,
            closeOnOverlayClick: options.closeOnOverlayClick === true,
            onRefresh: options.onRefresh,
            'onUpdate:modelValue': (value) => {
              if (options.modelValue) {
                options.modelValue.value = value;
              }
            },
            onConfirm: (value) => {
              // 确认按钮点击事件
              resolve(true);
              // 销毁应用
              destroyApp();
            },
            onCancel: () => {
              // 取消按钮点击事件
              resolve(false);
              // 销毁应用
              destroyApp();
            },
            onClose: () => {
              // 关闭按钮点击事件
              resolve(false);
              // 销毁应用
              destroyApp();
            }
          });
        },
        mounted() {
          // 组件挂载后，打开对话框
          this.$refs.customDialog.open();
        }
      });

      // 关闭之前的对话框
      this.closeDialog();

      // 挂载应用
      const instance = app.mount(container);

      // 保存当前对话框实例
      currentDialogInstance = app;
      currentDialogContainer = container;

      // 销毁应用的函数
      const destroyApp = () => {
        console.log('[AlertService] 销毁自定义对话框', options.title);
        app.unmount();
        document.body.removeChild(container);

        // 清除当前对话框实例
        if (currentDialogInstance === app) {
          currentDialogInstance = null;
          currentDialogContainer = null;
        }
      };
    });
  },


};

// 添加全局样式
const style = document.createElement('style');
style.textContent = `
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 暗色主题下的对话框遮罩层 */
[data-theme="dark"] .dialog-overlay {
  background-color: rgba(0, 0, 0, 0.7);
}
`;
document.head.appendChild(style);

// 导出服务
export default alertService;

// Vue插件
export const alertPlugin = {
  install(app) {
    app.config.globalProperties.$alert = alertService.alert;
    app.config.globalProperties.$confirm = alertService.confirm;
    app.config.globalProperties.$customDialog = alertService.customDialog;
  }
};
