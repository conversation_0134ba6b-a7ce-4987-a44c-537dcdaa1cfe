import { defineStore } from 'pinia';

export const useDeployStore = defineStore('deploy', {
  state: () => ({
    isDeploying: false,
    deployingDevices: new Set(),
  }),

  actions: {
    startDeploy(deviceId) {
      this.deployingDevices.add(deviceId);
      this.isDeploying = true;
    },

    finishDeploy(deviceId) {
      this.deployingDevices.delete(deviceId);
      if (this.deployingDevices.size === 0) {
        this.isDeploying = false;
      }
    },

    cancelDeploy(deviceId) {
      this.deployingDevices.delete(deviceId);
      if (this.deployingDevices.size === 0) {
        this.isDeploying = false;
      }
    },

    isDeviceDeploying(deviceId) {
      return this.deployingDevices.has(deviceId);
    },

    clearDeployingDevices() {
      this.deployingDevices.clear();
      this.isDeploying = false;
    }
  }
}); 