/* 通用组件样式 */

/* 卡片基础样式 */
.card {
  background-color: var(--color-card-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--card-shadow);
  border: var(--card-border);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--card-hover-shadow);
  border-color: var(--color-primary);
  transform: var(--card-hover-transform);
}

/* 暗色主题卡片样式增强 */
[data-theme="dark"] .card {
  background-color: var(--color-card-background);
  border: 1px solid var(--color-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .card:hover {
  border-color: var(--color-primary);
  box-shadow:
    0 6px 16px rgba(0, 0, 0, 0.6),
    0 0 0 1px var(--color-primary-dark);
}

/* 复选框样式 */
.custom-checkbox {
  position: relative;
  display: inline-block;
}

.custom-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkbox-mark {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: rgba(250, 250, 250, 0.8);
  border: 2px solid rgba(240, 240, 240, 0.9);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.custom-checkbox input[type="checkbox"]:checked ~ .checkbox-mark {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.custom-checkbox input[type="checkbox"]:checked ~ .checkbox-mark:after {
  content: "";
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* 按钮基础样式 */
.btn {
  padding: 8px 14px;
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.btn-disabled {
  opacity: 0.3;
}

.btn-primary {
  background-color: var(--color-button);
  color: var(--color-white);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-success {
  background-color: var(--color-success);
  color: var(--color-white);
}

.btn-success:hover {
  background-color: var(--color-success-dark);
}

.btn-danger {
  background-color: var(--color-danger);
  color: var(--color-white);
}

.btn-danger:hover {
  background-color: var(--color-danger-dark);
}

.btn-warning {
  background-color: var(--color-warning);
  color: var(--color-white);
}

.btn-warning:hover {
  background-color: var(--color-warning-dark);
}

.btn-secondary {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-background-dark);
  border-color: var(--color-primary);
}

/* 返回按钮 */
.back-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: var(--color-menu-text);
  font-size: 18px;
  cursor: pointer;
  margin-right: 16px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s;

  text-align: center;
  font-style: normal;
  font-weight: 480;
  line-height: 18px;
}

.back-btn:hover {
  background-color: rgba(33, 150, 243, 0.1);
}

.back-btn image {
  width: 28px;
  height: 28px;
}

.back-btn i {
  margin-right: 4px;
}

/* 暗色主题按钮样式增强 */
[data-theme="dark"] .btn {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .btn-primary {
  background-color: var(--color-primary);
  border: 1px solid var(--color-primary-light);
}

[data-theme="dark"] .btn-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary);
}

[data-theme="dark"] .btn-success {
  background-color: var(--color-success);
  border: 1px solid var(--color-success-light);
}

[data-theme="dark"] .btn-success:hover {
  background-color: var(--color-success-dark);
  border-color: var(--color-success);
}

[data-theme="dark"] .btn-danger {
  background-color: var(--color-danger);
  border: 1px solid var(--color-danger-light);
}

[data-theme="dark"] .btn-danger:hover {
  background-color: var(--color-danger-dark);
  border-color: var(--color-danger);
}

[data-theme="dark"] .btn-warning {
  background-color: var(--color-warning);
  border: 1px solid var(--color-warning-light);
}

[data-theme="dark"] .btn-warning:hover {
  background-color: var(--color-warning-dark);
  border-color: var(--color-warning);
}

[data-theme="dark"] .btn-secondary {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

[data-theme="dark"] .btn-secondary:hover {
  background-color: var(--color-background-dark);
  border-color: var(--color-primary-light);
  color: var(--color-primary-light);
}

/* 图标按钮 */
.btn-icon {
  width: 30px;
  height: 30px;
  border-radius: var(--border-radius-circle);
  padding: 0;
  background-color: rgba(250, 250, 250, 0.8);
  color: var(--color-text-secondary);
  font-size: 16px;
}

.btn-icon:hover {
  background-color: var(--color-white);
  transform: scale(1.1);
}

/* 暗色主题图标按钮 */
[data-theme="dark"] .btn-icon {
  background-color: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.85);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* 下拉菜单 */
.dropdown {
  position: relative;
}

.dropdown-content {
  position: absolute;
  background-color: var(--color-card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  z-index: 1000;
  animation: fadeIn 0.15s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  padding: 10px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--color-text-primary);
}

.dropdown-item:hover {
  background-color: var(--color-background-light);
}

/* 暗色主题下拉菜单 */
[data-theme="dark"] .dropdown-content {
  background-color: #1a1a1a;
  border: 1px solid var(--color-border);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.6);
}

[data-theme="dark"] .dropdown-item {
  color: rgba(255, 255, 255, 0.85);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .dropdown-item:last-child {
  border-bottom: none;
}

[data-theme="dark"] .dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* 状态指示器 */
.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: var(--border-radius-circle);
  display: inline-block;
  transition: all var(--transition-normal);
}

.status-indicator.success {
  background-color: var(--color-success);
  box-shadow: 0 0 6px var(--color-success);
}

.status-indicator.warning {
  background-color: var(--color-warning);
  box-shadow: 0 0 6px var(--color-warning);
}

.status-indicator.danger {
  background-color: var(--color-danger);
  box-shadow: 0 0 6px var(--color-danger);
}

.status-indicator.info {
  background-color: var(--color-info);
  box-shadow: 0 0 6px var(--color-info);
}

.status-indicator.disabled {
  background-color: var(--color-text-disabled);
}

/* 输入框 */
.input {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: all var(--transition-fast);
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
}

.input:focus {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-active);
  outline: none;
}

.input::placeholder {
  color: var(--color-text-placeholder);
}

/* 标签 */
.tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: var(--border-radius);
  font-size: 12px;
  font-weight: 500;
}

.tag-primary {
  background-color: var(--color-primary-bg);
  color: var(--color-primary);
}

.tag-success {
  background-color: var(--color-success-bg);
  color: var(--color-success);
}

.tag-warning {
  background-color: var(--color-warning-bg);
  color: var(--color-warning);
}

.tag-danger {
  background-color: var(--color-danger-bg);
  color: var(--color-danger);
}

/* 加载指示器 */
.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 20px;
  color: var(--color-text-secondary);
}

/* 分割线 */
.divider {
  height: 1px;
  background-color: var(--color-border);
  margin: 16px 0;
}

.divider-vertical {
  width: 1px;
  height: 100%;
  background-color: var(--color-border);
  margin: 0 16px;
}
