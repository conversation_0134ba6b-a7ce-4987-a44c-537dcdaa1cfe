/* 基础主题变量 - 专业风格 */
:root {
  /* 基础颜色 */
  --color-primary: #1890ff; /* 更深的蓝色 - 专业感更强 */
  --color-primary-dark: #1e4e8c;
  --color-primary-light: #4299e1;
  --color-primary-bg: rgba(43, 108, 176, 0.1);

  --color-success: #00e38e; /* 更柔和的绿色 */
  --color-success-dark: #2f855a;
  --color-success-light: #48bb78;
  --color-success-bg: rgba(56, 161, 105, 0.1);

  --color-warning: #ed8936; /* 更现代的橙色 */
  --color-warning-dark: #dd6b20;
  --color-warning-light: #f6ad55;
  --color-warning-bg: rgba(237, 137, 54, 0.1);

  --color-danger: #e53e3e; /* 更柔和的红色 */
  --color-danger-dark: #c53030;
  --color-danger-light: #fc8181;
  --color-danger-bg: rgba(229, 62, 62, 0.1);

  --color-info: #2b6cb0; /* 与主色相同 */
  --color-info-dark: #1e4e8c;
  --color-info-light: #4299e1;
  --color-info-bg: rgba(43, 108, 176, 0.1);

  --color-purple: #805ad5; /* 更现代的紫色 */
  --color-purple-dark: #6b46c1;
  --color-purple-light: #9f7aea;
  --color-purple-bg: rgba(128, 90, 213, 0.1);

  --color-cyan: #0987a0; /* 更深的青色 */
  --color-cyan-dark: #086f83;
  --color-cyan-light: #38b2ac;
  --color-cyan-bg: rgba(9, 135, 160, 0.1);

  --color-orange: #ed8936; /* 更现代的橙色 */
  --color-orange-dark: #dd6b20;
  --color-orange-light: #f6ad55;
  --color-orange-bg: rgba(237, 137, 54, 0.1);

  /* 文本颜色 */
  --color-text-primary: #2d3748; /* 更深的文本颜色 */
  --color-text-secondary: #4a5568;
  --color-text-disabled: #a0aec0;
  --color-text-placeholder: #cbd5e0;

  /* 背景颜色 */
  --color-background: #fff; /* 带蓝色调的灰色背景 */
  --color-background-light: #f8fafc; /* 更亮的背景 */
  --color-background-dark: #e2e8f0; /* 更深的灰色背景 */
  --color-card-background: #ffffff; /* 纯白色卡片背景 */
  --color-card-background-online: #eff6fe; /* 在线设备卡片的淡蓝色背景 - 更柔和 */
  --color-card-background-offline: #eff6fe; /* 离线卡片的灰色背景 */
  --color-box-bg: #eff6fe;

  /* 边框颜色 */
  --color-border: #e2e8f0; /* 带蓝色调的边框 */
  --color-border-dark: #cbd5e0; /* 较深的灰色边框 */
  --color-border-light: #edf2f7; /* 非常浅的灰色边框 */

  /* 菜单颜色 */
  --color-menu: #e4f0ff; /* 深灰蓝色菜单背景 */
  --color-menu-active: #238aff; /* 活动项背景 */
  --color-menu-active-end: #bfddff; /* 活动项背景渐变结束颜色 */
  --color-menu-hover: #4a5568; /* 悬停背景 */
  --color-menu-text: #595b6a; /* 文字颜色 新增 */

  /* 其他颜色 */
  --color-white: #ffffff; /* 纯白色 */
  --color-black: #1a202c; /* 深黑色 */
  --color-white-trans: rgba(255, 255, 255, 0.9);
  --color-black-trans: rgba(26, 32, 44, 0.1);
  --color-split-line: #bcbcbc;

  /* 弹框颜色 */
  --color-dialog-background: #d9e8f8; /* 弹框背景 */

  /* 阴影 */
  --shadow-sm:
    0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1); /* 更现代的阴影 */
  --shadow-md:
    0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.1); /* 更现代的阴影 */
  --shadow-lg:
    0 10px 15px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.1); /* 更现代的阴影 */
  --shadow-hover:
    0 15px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.1); /* 更现代的阴影 */
  --shadow-active: 0 0 0 3px rgba(43, 108, 176, 0.25); /* 主色调阴影 */

  /* 圆角 */
  --border-radius: 8px; /* 更大的圆角 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-circle: 50%;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  /* 动画 */
  --transition-fast: 0.15s;
  --transition-normal: 0.25s;
  --transition-slow: 0.35s;

  /* 卡片样式 */
  --card-padding: 16px;
  --card-shadow:
    0 2px 4px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1); /* 更现代的阴影 */
  --card-hover-shadow:
    0 15px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.1); /* 更现代的阴影 */
  --card-border: 1px solid #e2e8f0;
  --card-hover-transform: translateY(-3px);

  /* 按钮相关 */
  --color-button: #3593ff; /* 按钮颜色 */
}

/* 暗色主题 */
[data-theme="dark"] {
  /* 基础颜色 */
  --color-primary: #3182ce; /* 深蓝色主色调 */
  --color-primary-dark: #2c5282;
  --color-primary-light: #4299e1;
  --color-primary-bg: rgba(49, 130, 206, 0.15);

  --color-success: #38a169; /* 深绿色 */
  --color-success-dark: #2f855a;
  --color-success-light: #48bb78;
  --color-success-bg: rgba(56, 161, 105, 0.15);

  --color-warning: #dd6b20; /* 深橙色 */
  --color-warning-dark: #c05621;
  --color-warning-light: #ed8936;
  --color-warning-bg: rgba(221, 107, 32, 0.15);

  --color-danger: #e53e3e; /* 深红色 */
  --color-danger-dark: #c53030;
  --color-danger-light: #f56565;
  --color-danger-bg: rgba(229, 62, 62, 0.15);

  --color-info: #3182ce; /* 与主色相同 */
  --color-info-dark: #2c5282;
  --color-info-light: #4299e1;
  --color-info-bg: rgba(49, 130, 206, 0.15);

  --color-purple: #6b46c1; /* 深紫色 */
  --color-purple-dark: #553c9a;
  --color-purple-light: #805ad5;
  --color-purple-bg: rgba(107, 70, 193, 0.15);

  --color-cyan: #319795; /* 深青色 */
  --color-cyan-dark: #2c7a7b;
  --color-cyan-light: #38b2ac;
  --color-cyan-bg: rgba(49, 151, 149, 0.15);

  --color-orange: #dd6b20; /* 深橙色 */
  --color-orange-dark: #c05621;
  --color-orange-light: #ed8936;
  --color-orange-bg: rgba(221, 107, 32, 0.15);

  /* 文本颜色 */
  --color-text-primary: rgba(255, 255, 255, 0.95);
  --color-text-secondary: rgba(255, 255, 255, 0.7);
  --color-text-disabled: rgba(255, 255, 255, 0.35);
  --color-text-placeholder: rgba(255, 255, 255, 0.3);
  --color-text-inverse: #1a202c; /* 深色文本 */

  /* 背景颜色 */
  --color-background: #1a202c; /* 深蓝灰色背景 */
  --color-background-light: #2d3748; /* 较亮的背景 */
  --color-background-dark: #171923; /* 更深的背景 */
  --color-card-background: #2d3748; /* 卡片背景 */
  --color-card-background-offline: #414e61; /* 离线卡片背景 - 更亮一些，增加对比度 */
  --color-card-background-online: #2c5282; /* 在线设备卡片背景 - 蓝色调 */
  --color-box-bg: #2c5282;

  /* 边框颜色 */
  --color-border: #4a5568; /* 中等灰色边框 */
  --color-border-dark: #2d3748; /* 深色边框 */
  --color-border-light: #718096; /* 亮色边框 */

  /* 菜单颜色 */
  --color-menu: #171923; /* 深色菜单背景 */
  --color-menu-active: #2d3748; /* 活动项背景 */
  --color-menu-active-end: #2d3748; /* 活动项背景渐变结束颜色 */
  --color-menu-hover: #2d3748; /* 悬停背景 */
  --color-menu-text: rgba(255, 255, 255, 0.8); /* 文字颜色 新增 */

  /* 其他颜色 */
  --color-white: #ffffff; /* 纯白色 */
  --color-black: #000000; /* 纯黑色 */
  --color-white-trans: rgba(255, 255, 255, 0.8);
  --color-black-trans: rgba(0, 0, 0, 0.4);
  --color-split-line: rgba(255, 255, 255, 0.1);

  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4); /* 简化的暗色阴影 */
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4); /* 简化的暗色阴影 */
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4); /* 简化的暗色阴影 */
  --shadow-hover: 0 15px 25px rgba(0, 0, 0, 0.4); /* 简化的暗色阴影 */
  --shadow-active: 0 0 0 3px rgba(49, 130, 206, 0.4); /* 主色调阴影 */

  /* 卡片样式 */
  --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.4); /* 简化的暗色阴影 */
  --card-hover-shadow: 0 15px 25px rgba(0, 0, 0, 0.4); /* 简化的暗色阴影 */
  --card-border: 1px solid #4a5568;

  /* 弹框颜色 */
  --color-dialog-background: #2d3748; /* 弹框背景 */
}
