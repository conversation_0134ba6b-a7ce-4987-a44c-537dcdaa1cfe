/* 图标样式定义 */

/* 基础图标样式 */
[class^="icon-"] {
  font-style: normal;
  font-family: monospace;
  display: inline-block;
  vertical-align: middle;
}

/* 使用图片的图标 */
.icon-refresh {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_action_restart.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 4px;
}

/* 设备卡片菜单图标 */
.icon-edit-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_edit.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 8px;
}

/* 箭头图标 */
.icon-arrow-down {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_arrow_down.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 4px;
  transition: transform 0.3s ease;
}

/* 旋转箭头图标 */
.icon-arrow-down.rotated {
  transform: rotate(180deg);
}

.icon-delete-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_delete.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 8px;
}

.icon-resource-empty {
  display: inline-block;
  width: 48px;
  height: 48px;
  background-image: url('../../assets/images/yvr_plan_manager.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
}

.icon-shutdown {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_action_close.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 8px;
}

.icon-restart-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_action_restart.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 8px;
}

/* 关闭屏幕图标 */
.icon-screen-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_idle.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 8px;
}

/* 音量控制图标 */
.icon-volume-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url('../../assets/images/yvr_volume.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  margin-right: 8px;
}

/* 图标反色效果 - 可以应用于任何图标 */
.icon-invert {
  /* 使用 invert 滤镜实现反色效果 */
  filter: invert(1);
}

/* 图片图标的反色效果 */
.icon-edit-img.icon-invert,
.icon-delete-img.icon-invert,
.icon-shutdown.icon-invert,
.icon-restart-img.icon-invert,
.icon-screen-img.icon-invert,
.icon-volume-img.icon-invert,
.icon-refresh.icon-invert,
.icon-arrow-down.icon-invert,
.icon-resource-empty.icon-invert {
  filter: invert(1);
}

/* 深色主题下的图标 - 所有图标都显示为白色 */
[data-theme="dark"] .dropdown-item i {
  filter: brightness(0) invert(1); /* 先转为黑色，再反色为白色 */
}

/* 确保深色主题下的图标在下拉菜单中正确显示 */
[data-theme="dark"] .dropdown-menu .icon-edit-img,
[data-theme="dark"] .dropdown-menu .icon-delete-img,
[data-theme="dark"] .dropdown-menu .icon-shutdown,
[data-theme="dark"] .dropdown-menu .icon-restart-img,
[data-theme="dark"] .dropdown-menu .icon-screen-img,
[data-theme="dark"] .dropdown-menu .icon-volume-img,
[data-theme="dark"] .dropdown-menu .icon-volume,
[data-theme="dark"] .dropdown-menu .icon-screen {
  filter: brightness(0) invert(1); /* 先转为黑色，再反色为白色 */
}

/* 确保深色主题下的音量控制子菜单中的图标也正确显示 */
[data-theme="dark"] .volume-submenu .icon-volume,
[data-theme="dark"] .volume-submenu .icon-volume-img {
  filter: brightness(0) invert(1); /* 先转为黑色，再反色为白色 */
}

/* 确保深色主题下的刷新按钮图标正确显示 */
[data-theme="dark"] .btn .icon-refresh,
[data-theme="dark"] .btn-secondary .icon-refresh,
[data-theme="dark"] .header-actions .btn .icon-refresh {
  filter: brightness(0) invert(1); /* 先转为黑色，再反色为白色 */
}

/* 文本图标 */
.icon-add::before { content: "+"; }
.icon-edit::before { content: "✎"; }
.icon-delete::before { content: "×"; }
.icon-play::before { content: "▶"; }
.icon-pause::before { content: "⏸"; }
.icon-grid::before { content: "▦"; }
.icon-list::before { content: "≡"; }
.icon-device-empty::before { content: "⚠"; }
.icon-group::before { content: "⊞"; }
.icon-chevron-left::before { content: "◀"; }
.icon-chevron-right::before { content: "▶"; }
.icon-task-empty::before { content: "📅"; }
.icon-deploy::before { content: "↗"; }
.icon-folder-open::before { content: "📂"; }
.icon-arrow.up::before { content: "↑"; }
.icon-arrow.down::before { content: "↓"; }
.icon-more-vertical::before { content: "⋮"; }
.icon-resource-empty {
  display: inline-block;
  width: 48px;
  height: 48px;
  background-image: url('../../assets/images/yvr_plan_manager.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
}
.icon-solution-empty::before { content: "📋"; }
