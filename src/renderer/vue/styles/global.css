/* 全局样式 */

/* 导入主题变量 */
@import "./themes/variables.css";

/* 导入组件样式 */
@import "./components.css";

/* 导入图标样式 */
@import "./icons.css";

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
    Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* 应用于整个应用的不可选中文本 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}

.page {
  height: 100%;
  width: 100%;
  border-radius: var(--border-radius) 0 0 0;
  background-color: var(--color-background);

  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  overflow: hidden;
}

/* 防止文本被选中 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}

/* 允许特定元素的文本被选中 */
.selectable,
input,
textarea,
[contenteditable="true"] {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  cursor: text;
}

/* 允许代码块文本被选中 */
pre,
code {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  cursor: text;
}

/* 布局辅助类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-grow {
  flex-grow: 1;
}

/* 间距辅助类 */
.mt-xs {
  margin-top: var(--spacing-xs);
}
.mt-sm {
  margin-top: var(--spacing-sm);
}
.mt-md {
  margin-top: var(--spacing-md);
}
.mt-lg {
  margin-top: var(--spacing-lg);
}
.mt-xl {
  margin-top: var(--spacing-xl);
}

.mb-xs {
  margin-bottom: var(--spacing-xs);
}
.mb-sm {
  margin-bottom: var(--spacing-sm);
}
.mb-md {
  margin-bottom: var(--spacing-md);
}
.mb-lg {
  margin-bottom: var(--spacing-lg);
}
.mb-xl {
  margin-bottom: var(--spacing-xl);
}

.ml-xs {
  margin-left: var(--spacing-xs);
}
.ml-sm {
  margin-left: var(--spacing-sm);
}
.ml-md {
  margin-left: var(--spacing-md);
}
.ml-lg {
  margin-left: var(--spacing-lg);
}
.ml-xl {
  margin-left: var(--spacing-xl);
}

.mr-xs {
  margin-right: var(--spacing-xs);
}
.mr-sm {
  margin-right: var(--spacing-sm);
}
.mr-md {
  margin-right: var(--spacing-md);
}
.mr-lg {
  margin-right: var(--spacing-lg);
}
.mr-xl {
  margin-right: var(--spacing-xl);
}

.p-xs {
  padding: var(--spacing-xs);
}
.p-sm {
  padding: var(--spacing-sm);
}
.p-md {
  padding: var(--spacing-md);
}
.p-lg {
  padding: var(--spacing-lg);
}
.p-xl {
  padding: var(--spacing-xl);
}

/* 文本辅助类 */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.text-primary {
  color: var(--color-primary);
}
.text-success {
  color: var(--color-success);
}
.text-warning {
  color: var(--color-warning);
}
.text-danger {
  color: var(--color-danger);
}
.text-info {
  color: var(--color-info);
}
.text-secondary {
  color: var(--color-text-secondary);
}
.text-disabled {
  color: var(--color-text-disabled);
}

.text-bold {
  font-weight: bold;
}
.text-small {
  font-size: 12px;
}
.text-large {
  font-size: 16px;
}
.text-xl {
  font-size: 18px;
}
.text-xxl {
  font-size: 24px;
}

/* 隐藏元素 */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* 暗色主题滚动条 */
[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.25);
  border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.img-bg-placeholder {
  width: 100%;
  height: 100%;
  background: #d3e7fe;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}
