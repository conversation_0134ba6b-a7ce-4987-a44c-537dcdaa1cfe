/**
 * 分组工具函数
 * 用于处理分组的层级结构、解析和格式化
 */

/**
 * 分组分隔符
 * 用于分隔主分类和子分类
 * @type {string}
 */
export const GROUP_SEPARATOR = '/';

/**
 * 检查分组名称是否为二级分类
 * @param {string} groupName - 分组名称
 * @returns {boolean} 是否为二级分类
 */
export function isSubcategory(groupName) {
  return groupName && groupName.includes(GROUP_SEPARATOR);
}

/**
 * 获取分组的主分类名称
 * @param {string} groupName - 分组名称
 * @returns {string} 主分类名称
 */
export function getMainCategory(groupName) {
  if (!isSubcategory(groupName)) {
    return groupName;
  }
  return groupName.split(GROUP_SEPARATOR)[0];
}

/**
 * 获取分组的子分类名称
 * @param {string} groupName - 分组名称
 * @returns {string|null} 子分类名称，如果不是二级分类则返回null
 */
export function getSubcategory(groupName) {
  if (!isSubcategory(groupName)) {
    return null;
  }
  const parts = groupName.split(GROUP_SEPARATOR);
  return parts.length > 1 ? parts[1] : null;
}

/**
 * 创建完整的分组名称
 * @param {string} mainCategory - 主分类名称
 * @param {string} subcategory - 子分类名称
 * @returns {string} 完整的分组名称
 */
export function createFullGroupName(mainCategory, subcategory) {
  if (!subcategory) {
    return mainCategory;
  }
  return `${mainCategory}${GROUP_SEPARATOR}${subcategory}`;
}

/**
 * 将平面分组列表转换为层级结构
 * @param {Array<string>} groups - 分组列表
 * @returns {Array<Object>} 层级结构的分组列表
 */
export function convertToHierarchical(groups) {
  const result = [];
  const mainCategories = new Map();

  // 首先处理所有分组
  groups.forEach(group => {
    if (isSubcategory(group)) {
      const mainCategory = getMainCategory(group);
      const subCategory = getSubcategory(group);
      
      if (!mainCategories.has(mainCategory)) {
        const mainCategoryObj = {
          name: mainCategory,
          fullName: mainCategory,
          children: []
        };
        mainCategories.set(mainCategory, mainCategoryObj);
        result.push(mainCategoryObj);
      }
      
      mainCategories.get(mainCategory).children.push({
        name: subCategory,
        fullName: group,
        children: []
      });
    } else {
      // 如果不是子分类，直接添加到结果中
      if (!mainCategories.has(group)) {
        const categoryObj = {
          name: group,
          fullName: group,
          children: []
        };
        mainCategories.set(group, categoryObj);
        result.push(categoryObj);
      }
    }
  });

  return result;
}

/**
 * 获取资源所属的所有主分类
 * @param {Object} resource - 资源对象
 * @returns {Array<string>} 主分类列表
 */
export function getResourceMainCategories(resource) {
  if (!resource.groups || !Array.isArray(resource.groups)) {
    return [];
  }
  
  return resource.groups.map(group => getMainCategory(group))
    .filter((value, index, self) => self.indexOf(value) === index); // 去重
}

/**
 * 检查资源是否属于指定的主分类
 * @param {Object} resource - 资源对象
 * @param {string} mainCategory - 主分类名称
 * @returns {boolean} 是否属于该主分类
 */
export function isResourceInMainCategory(resource, mainCategory) {
  if (!resource.groups || !Array.isArray(resource.groups)) {
    return false;
  }
  
  return resource.groups.some(group => {
    const groupMainCategory = getMainCategory(group);
    return groupMainCategory === mainCategory;
  });
}

/**
 * 检查资源是否属于指定的完整分组（包括子分类）
 * @param {Object} resource - 资源对象
 * @param {string} fullGroupName - 完整的分组名称
 * @returns {boolean} 是否属于该分组
 */
export function isResourceInGroup(resource, fullGroupName) {
  if (!resource.groups || !Array.isArray(resource.groups)) {
    return false;
  }
  
  return resource.groups.includes(fullGroupName);
}

/**
 * 获取所有主分类列表
 * @param {Array<string>} groups - 分组列表
 * @returns {Array<string>} 主分类列表
 */
export function getAllMainCategories(groups) {
  return groups.map(group => getMainCategory(group))
    .filter((value, index, self) => self.indexOf(value) === index); // 去重
}

/**
 * 获取指定主分类下的所有子分类
 * @param {Array<string>} groups - 分组列表
 * @param {string} mainCategory - 主分类名称
 * @returns {Array<string>} 子分类列表
 */
export function getSubcategoriesForMainCategory(groups, mainCategory) {
  return groups
    .filter(group => isSubcategory(group) && getMainCategory(group) === mainCategory)
    .map(group => getSubcategory(group))
    .filter(Boolean); // 过滤掉null值
}
