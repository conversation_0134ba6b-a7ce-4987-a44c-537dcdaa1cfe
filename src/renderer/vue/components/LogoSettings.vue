<template>
  <div class="logo-control">
    <div class="logo-row">
      <span class="logo-text">应用 Logo</span>
      <div class="logo-preview">
        <img :src="logoPreview" alt="应用 Logo 预览" class="logo-image" />
      </div>
      <div class="logo-actions">
        <button class="select-button" @click="selectLogo">
          选择新 Logo
        </button>
        <button class="reset-button" @click="resetLogo" :disabled="!customLogo">
          恢复默认
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useElectronAPI } from '../plugins/electron';
import defaultLogoIcon from '../../assets/images/yvr_logo.png';

// 使用Electron API
const electronAPI = useElectronAPI();

// 状态
const logoPreview = ref('');
const customLogo = ref(false);
const defaultLogoPath = defaultLogoIcon;
const isSelectingFile = ref(false);

// 初始化 Logo 预览
const initLogoPreview = async () => {
  try {
    // 获取当前应用 Logo
    const appLogoPath = await electronAPI.getAppLogo();

    // 设置 Logo 预览
    if (appLogoPath) {
      logoPreview.value = appLogoPath;
      customLogo.value = true;
    } else {
      // 使用默认 Logo
      logoPreview.value = defaultLogoPath;
      customLogo.value = false;
    }
  } catch (error) {
    console.error('获取 Logo 设置失败:', error);
    logoPreview.value = defaultLogoPath;
    customLogo.value = false;
  }
};

// 选择 Logo
const selectLogo = async () => {
  // 如果已经在选择文件，直接返回
  if (isSelectingFile.value) {
    return;
  }

  try {
    isSelectingFile.value = true;
    // 调用 Electron API 打开文件选择对话框
    const result = await electronAPI.selectFiles({
      properties: ['openFile'],
      fileType: 'image',  // 指定文件类型为图片
      title: '选择应用 Logo 图片'
    });

    if (!result || result.length === 0) {
      return;
    }

    const filePath = result[0].path;

    // 保存应用 Logo
    const logoPath = await electronAPI.saveAppLogo(filePath);

    // 更新预览
    logoPreview.value = logoPath;
    customLogo.value = true;

    console.log('应用 Logo 已更新:', filePath);
  } catch (error) {
    console.error('选择 Logo 失败:', error);
  } finally {
    isSelectingFile.value = false;
  }
};

// 重置 Logo
const resetLogo = async () => {
  try {
    // 删除应用 Logo
    const result = await electronAPI.deleteAppLogo();

    if (result) {
      console.log('应用 Logo 已重置');
    } else {
      console.warn('应用 Logo 重置可能未完全成功，但会继续更新 UI');
    }

    // 无论结果如何，都更新预览
    logoPreview.value = defaultLogoPath;
    customLogo.value = false;
  } catch (error) {
    console.error('重置 Logo 失败:', error);

    // 即使出错，也更新预览
    logoPreview.value = defaultLogoPath;
    customLogo.value = false;
  }
};

// 生命周期钩子
onMounted(() => {
  initLogoPreview();
});
</script>

<style scoped>
.logo-control {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--color-card-background);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--color-primary-light);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.logo-control:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.logo-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
}

.logo-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
  flex: 0 0 80px;
}

.logo-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  border-radius: var(--border-radius-sm);
  border: 1px dashed var(--color-border);
  width: 70px;
  height: 35px;
  margin-right: auto;
  margin-left: var(--spacing-md);
}

.logo-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.logo-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.select-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
  height: 28px;
  white-space: nowrap;
}

.select-button:hover {
  background-color: var(--color-primary-dark);
}

.reset-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
  height: 28px;
  white-space: nowrap;
}

.reset-button:hover:not(:disabled) {
  background-color: var(--color-danger-light);
  color: var(--color-danger);
  border-color: var(--color-danger);
}

.reset-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
