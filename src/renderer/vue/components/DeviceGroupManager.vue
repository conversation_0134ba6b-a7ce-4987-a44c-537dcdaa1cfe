<template>
  <div class="device-group-manager">
    <div class="group-header">
      <h4>设备分组</h4>
      <button class="btn btn-primary" @click="showCreateGroupDialog">
        <i class="icon-add"></i>新建分组
      </button>
    </div>
    
    <div class="group-list">
      <div v-if="loading" class="loading-indicator">
        <div class="spinner"></div>
        <p>加载中...</p>
      </div>
      
      <div v-else-if="groups.length === 0" class="empty-message">
        暂无设备分组，点击"新建分组"创建
      </div>
      
      <div v-else class="group-items">
        <div
          v-for="group in groups"
          :key="group.id"
          class="group-item"
          :class="{ 'active': selectedGroupId === group.id }"
          @click="selectGroup(group.id)"
        >
          <div class="group-info">
            <div class="group-name">{{ group.name }}</div>
            <div class="device-count">{{ group.devices.length }}个设备</div>
          </div>
          <div class="group-actions">
            <button class="btn btn-icon" @click.stop="editGroup(group)">
              <i class="icon-edit"></i>
            </button>
            <button class="btn btn-icon" @click.stop="deleteGroup(group)">
              <i class="icon-delete"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useElectronAPI } from '../plugins/electron';
import alertService from '../plugins/alert';

const electronAPI = useElectronAPI();

// 定义组件属性
const props = defineProps({
  // 设备组列表
  groups: {
    type: Array,
    default: () => []
  },
  // 选中的设备组ID
  selectedGroupId: {
    type: String,
    default: null
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits([
  'select',
  'create',
  'update',
  'delete',
  'refresh'
]);

// 选择设备组
const selectGroup = (groupId) => {
  emit('select', groupId);
};

// 显示创建设备组对话框
const showCreateGroupDialog = () => {
  emit('create');
};

// 编辑设备组
const editGroup = (group) => {
  emit('update', group);
};

// 删除设备组
const deleteGroup = async (group) => {
  try {
    const confirmed = await alertService.confirm({
      title: '删除设备组',
      message: `确定要删除设备组 "${group.name}" 吗？`,
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    });

    if (confirmed) {
      emit('delete', group.id);
    }
  } catch (error) {
    console.error('删除设备组失败:', error);
  }
};

// 刷新设备组列表
const refreshGroups = () => {
  emit('refresh');
};

// 组件挂载时刷新设备组列表
onMounted(() => {
  refreshGroups();
});
</script>

<style scoped>
.device-group-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--color-background-light);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.group-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.group-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.group-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.group-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.group-item:hover {
  background-color: var(--color-background-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.group-item.active {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
}

.group-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.group-name {
  font-weight: 600;
  font-size: 14px;
}

.device-count {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.group-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.btn-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background-color: var(--color-background-dark);
  color: var(--color-text-primary);
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--spacing-lg);
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-sm);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--spacing-lg);
  color: var(--color-text-secondary);
  text-align: center;
}
</style>
