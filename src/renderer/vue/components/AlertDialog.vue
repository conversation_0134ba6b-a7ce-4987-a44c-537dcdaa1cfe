<template>
  <teleport to="body">
    <div
      v-if="show"
      class="alert-dialog-overlay"
      @click="closeOnOverlayClick && close()"
    >
      <div class="alert-dialog" @click.stop>
        <img
          v-if="showCloseButton"
          class="alert-dialog-close"
          @click="close"
          src="@assets/svg/close.svg"
          alt=""
        />
        <div class="alert-dialog-header">
          <h3 class="alert-dialog-title">{{ title }}</h3>
        </div>
        <div class="alert-dialog-content">
          <div v-if="isLoading" class="loading-spinner-container">
            <div class="loading-spinner"></div>
            <div class="loading-text">{{ message }}</div>
          </div>
          <div v-else>{{ message }}</div>
        </div>
        <div class="alert-dialog-footer">
          <!-- <button
            v-if="showCancelButton"
            class="alert-dialog-button cancel-button"
            @click="cancel"
          >
            {{ cancelButtonText }}
          </button> -->
          <button class="alert-dialog-button confirm-button" @click="confirm">
            {{ confirmButtonText }}
          </button>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from "vue";

// 定义props
const props = defineProps({
  title: {
    type: String,
    default: "提示",
  },
  message: {
    type: String,
    required: true,
  },
  confirmButtonText: {
    type: String,
    default: "确定",
  },
  cancelButtonText: {
    type: String,
    default: "取消",
  },
  showCancelButton: {
    type: Boolean,
    default: false,
  },
  showCloseButton: {
    type: Boolean,
    default: true,
  },
  closeOnOverlayClick: {
    type: Boolean,
    default: false,
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
});

// 定义事件
const emit = defineEmits(["confirm", "cancel", "close"]);

// 对话框显示状态
const show = ref(false);

// 打开对话框
const open = () => {
  console.log("[AlertDialog] 打开对话框", props.title, props.message);
  show.value = true;
  // 阻止背景滚动
  document.body.style.overflow = "hidden";
};

// 关闭对话框
const close = () => {
  console.log("[AlertDialog] 关闭对话框", props.title, props.message);
  show.value = false;
  // 恢复背景滚动
  document.body.style.overflow = "";
  emit("close");
};

// 确认
const confirm = () => {
  emit("confirm");
  close();
};

// 取消
const cancel = () => {
  emit("cancel");
  close();
};

// 监听ESC键关闭对话框
const handleKeyDown = (event) => {
  if (event.key === "Escape" && show.value) {
    close();
  }
};

// 生命周期钩子
onMounted(() => {
  document.addEventListener("keydown", handleKeyDown);
});

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeyDown);
});

// 暴露方法
defineExpose({
  open,
  close,
});
</script>

<style scoped>
.alert-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.2s ease-out;
}

.alert-dialog {
  background-color: var(--color-card-background);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  width: 480px;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;

  gap: 15px;
  flex-direction: column;
  animation: slideIn 0.2s ease-out;

  position: relative;
  background-color: var(--color-dialog-background);
  padding: 30px 34px;
}

.alert-dialog-header {
  padding: 16px 20px;

  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;
}

.alert-dialog-title {
  color: var(--color-menu-text);
  font-size: 24px;
  text-align: center;
  font-style: normal;
  font-weight: 480;
  line-height: 18px;
}

.alert-dialog-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--color-text-secondary);
  width: 28px;
  height: 28px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.alert-dialog-close:hover {
  background-color: var(--color-background-dark);
  color: var(--color-text-primary);
}

/* 暗色主题下的关闭按钮悬停效果 */
[data-theme="dark"] .alert-dialog-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.alert-dialog-content {
  padding: 20px;
  overflow-y: auto;
  color: var(--color-text-primary);
  line-height: 1.5;
}

.alert-dialog-button {
  padding: 8px 14px;
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  width: 220px;
}
.alert-dialog-footer {
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}

.cancel-button {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.cancel-button:hover {
  background-color: var(--color-background-dark);
}

/* 暗色主题下的取消按钮 */
[data-theme="dark"] .cancel-button {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

[data-theme="dark"] .cancel-button:hover {
  background-color: var(--color-background-dark);
}

.confirm-button {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: 1px solid var(--color-primary);
}

.confirm-button:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

/* 暗色主题下的确认按钮 */
[data-theme="dark"] .confirm-button {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: 1px solid var(--color-primary);
}

[data-theme="dark"] .confirm-button:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 加载中样式 */
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: var(--spacing-md);
}

.loading-text {
  font-size: 16px;
  color: var(--color-text-primary);
  text-align: center;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
