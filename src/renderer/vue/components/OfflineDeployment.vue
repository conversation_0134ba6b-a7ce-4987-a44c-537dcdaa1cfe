<template>
  <div class="page">
    <!-- 顶部栏 -->
    <div class="content">
      <span class="title">方式一：直接将导出资源包拷贝至设备根目录</span>
      <span class="desc"> 适用于小批量设备部署</span>
      <pre>
1、将设备开机，并插入PC上；
2、将离线导出后的文件夹拷贝至VR设备的根目录下；
3、拷贝结束，则该设备部署成功；
4、重启设备；
5、对其他需要的设备逐一部署；
      </pre>
      <span class="title">方式二：通过SD卡/U盘部署</span>
      <span class="desc"
        >适用于大批量设备，可借助SD卡或U盘，批量操作部署，节省部署时间</span
      >
      <pre>
1、在VR设备内打开“行业助手”，详见说明书；
2、将离线导出后的文件夹，拷贝至SD卡或U盘的根目录下；
3、将SD卡或U盘插入VR设备中；
4、点击“部署”按钮，等待设备自动执行部署，部署时间根据文件大小不同；
5、设备中弹出“方案部署成功”，设备将自动重启；
6、对其他需要的设备逐一部署；        
      </pre>
      <span class="bottom-text"
        >离线部署：将目前现有的配置内容，统一下载至本地路径，然后将下载内容拷贝至VR设备目录下。</span
      >
    </div>

    <div class="btn btn-primary" @click="exportSolution">离线导出资源</div>
  </div>
</template>

<script setup>
import { useElectronAPI } from "../plugins/electron";
// 使用Electron API
const electronAPI = useElectronAPI();

/**
 * 离线部署（打开config文件夹）
 * @returns {Promise<void>}
 */
const exportSolution = async () => {
  try {
    // 直接打开 DeviceManagerResources 文件夹
    await electronAPI.openResourcesFolder();
    console.log("打开资源文件夹成功");
  } catch (err) {
    await handleError("打开资源文件夹失败", err);
  }
};
</script>

<style scoped>
.page {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--color-menu-text);
}
.content {
  display: flex;
  flex-direction: column;
  background-color: var(--color-box-bg);
  border-radius: 16px;
  padding: 24px 34px;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  width: 70vw;
}
.desc {
  line-height: 35px;
}
.title {
  font-size: 18px;
  font-style: normal;
  font-weight: 480;
  line-height: normal;
}
.bottom-text {
  color: #f69422;
}
.btn-primary {
  margin-top: 24px;
  width: 220px;
}
</style>
