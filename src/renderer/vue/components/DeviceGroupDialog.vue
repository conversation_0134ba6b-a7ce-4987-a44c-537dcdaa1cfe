<template>
  <div class="dialog-overlay" @click="$emit('close')">
    <div class="dialog-content" @click.stop>
      <div class="dialog-header">
        <h3>设备分组管理</h3>
        <button class="btn-close" @click="$emit('close')">&times;</button>
      </div>

      <div class="dialog-body">
        <div class="dialog-layout">
          <!-- 左侧分组列表 -->
          <div class="group-list-container">
            <div class="group-list-header">
              <h4>分组列表</h4>
              <button class="btn btn-sm btn-primary" @click="createNewGroup">
                <i class="icon-add"></i> 新建分组
              </button>
            </div>

            <div class="group-list">
              <div
                v-for="group in groups"
                :key="group.id"
                class="group-item"
                :class="{ 'active': selectedGroup && selectedGroup.id === group.id }"
                @click="selectGroup(group)"
              >
                <div class="group-item-name">{{ group.name }}</div>
                <div class="group-item-count">{{ group.devices.length }} 个设备</div>
              </div>

              <div v-if="groups.length === 0" class="empty-group-list">
                暂无分组，点击"新建分组"按钮创建
              </div>
            </div>
          </div>

          <!-- 右侧内容区 -->
          <div class="right-content" v-if="showForm">
            <!-- 表单容器 -->
            <div class="group-form-container">
              <div class="form-header">
                <h4>{{ isEdit ? '编辑分组' : '新建分组' }}</h4>
              </div>

              <div class="form-group">
                <label for="group-name">分组名称</label>
                <input
                  type="text"
                  id="group-name"
                  v-model="formData.name"
                  placeholder="请输入分组名称"
                  required
                  @input="validateGroupName"
                >
                <div v-if="nameError" class="form-error">{{ nameError }}</div>
              </div>

              <div class="form-group">
                <label for="group-description">分组描述</label>
                <textarea
                  id="group-description"
                  v-model="formData.description"
                  placeholder="请输入分组描述（可选）"
                  rows="3"
                ></textarea>
              </div>

              <div class="form-actions">
                <button v-if="isEdit" class="btn btn-danger" @click="confirmDelete">删除分组</button>
                <div class="form-actions-right">
                  <button class="btn btn-secondary" @click="cancelEdit">取消</button>
                  <button class="btn btn-primary" @click="saveGroup">保存</button>
                </div>
              </div>
            </div>

            <!-- 设备列表容器 (仅在编辑模式下显示且有设备时) -->
            <div v-if="isEdit && selectedGroup && selectedGroup.devices && selectedGroup.devices.length > 0" class="devices-container">
              <div class="devices-header">
                <h4>分组设备 ({{ selectedGroup.devices.length }})</h4>
                <div class="devices-actions">
                  <div class="search-box">
                    <input
                      type="text"
                      v-model="groupDeviceSearchQuery"
                      placeholder="搜索设备..."
                      @input="filterGroupDevices"
                    >
                  </div>
                  <button class="btn btn-sm btn-primary" @click="showAddDevicesDialog">
                    <i class="icon-add"></i> 添加设备
                  </button>
                </div>
              </div>

              <div class="devices-list">
                <div v-if="filteredGroupDevices.length === 0" class="empty-devices">
                  <p>没有匹配的设备</p>
                </div>

                <div v-else class="device-items">
                  <div v-for="deviceSn in filteredGroupDevices" :key="deviceSn" class="device-item">
                    <div class="device-info">
                      <div class="device-id-row">
                        <span class="device-sn">{{ deviceSn }}</span>
                        <span class="device-id" v-if="getDeviceId(deviceSn)">ID: {{ getDeviceId(deviceSn) }}</span>
                      </div>
                      <span class="device-status" :class="{ 'online': isDeviceOnline(deviceSn) }">
                        {{ isDeviceOnline(deviceSn) ? '在线' : '离线' }}
                      </span>
                    </div>
                    <button class="btn-delete" @click="removeDeviceFromGroup(deviceSn)" title="从分组中移除">
                      删除
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 添加设备按钮 (当编辑模式且没有设备时显示) -->
            <div v-if="isEdit && selectedGroup && (!selectedGroup.devices || selectedGroup.devices.length === 0)" class="add-devices-button-container">
              <button class="btn btn-primary" @click="showAddDevicesDialog">
                <i class="icon-add"></i> 添加设备
              </button>
            </div>
          </div>

          <!-- 右侧空状态 -->
          <div class="right-content empty-state" v-if="!showForm">
            <div class="empty-form-content">
              <div class="empty-icon">
                <i class="icon-group"></i>
              </div>
              <p>选择一个分组进行编辑，或点击"新建分组"按钮创建新分组</p>
              <button class="btn btn-primary" @click="createNewGroup">
                <i class="icon-add"></i> 新建分组
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加设备对话框 (放在主对话框外部) -->
  <div v-if="showAddDevices" class="add-devices-dialog" @click.stop="closeAddDevicesDialog">
    <div class="add-devices-content" @click.stop>
      <div class="add-devices-header">
        <h4>添加设备到分组 "{{ selectedGroup?.name }}"</h4>
        <button class="btn-close" @click="closeAddDevicesDialog">&times;</button>
      </div>

      <div class="add-devices-body">
        <div v-if="addSuccess" class="success-message">
          <i class="icon-success"></i> 已成功添加 {{ addedDevicesCount }} 个设备
        </div>

        <div class="search-box">
          <input
            type="text"
            v-model="deviceSearchQuery"
            placeholder="搜索设备..."
            @input="filterAvailableDevices"
          >
        </div>

        <div class="devices-selection">
          <div v-if="filteredAvailableDevices.length === 0" class="empty-devices">
            <p>没有可添加的设备</p>
          </div>

          <div v-else class="device-items selectable">
            <div
              v-for="device in filteredAvailableDevices"
              :key="device.sn"
              class="device-item"
              :class="{ 'selected': selectedDevicesToAdd.includes(device.sn) }"
              @click="toggleDeviceSelection(device.sn)"
            >
              <div class="device-info">
                <div class="device-id-row">
                  <span class="device-sn">{{ device.sn }}</span>
                  <span class="device-id" v-if="device.id">ID: {{ device.id }}</span>
                </div>
                <span class="device-status" :class="{ 'online': device.isOnline }">
                  {{ device.isOnline ? '在线' : '离线' }}
                </span>
              </div>
              <div class="device-checkbox">
                <input
                  type="checkbox"
                  :checked="selectedDevicesToAdd.includes(device.sn)"
                  @click.stop
                  @change="toggleDeviceSelection(device.sn)"
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="add-devices-footer">
        <div class="selection-info">
          已选择 {{ selectedDevicesToAdd.length }} 个设备
        </div>
        <div class="action-buttons">
          <button class="btn btn-secondary" @click="closeAddDevicesDialog">取消</button>
          <button
            class="btn btn-primary"
            @click="addSelectedDevicesToGroup"
            :disabled="selectedDevicesToAdd.length === 0"
          >
            添加
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import alertService from '../plugins/alert';

// 定义组件属性
const props = defineProps({
  // 所有设备组列表
  groups: {
    type: Array,
    default: () => []
  },
  // 所有设备列表
  devices: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['close', 'save', 'delete', 'addDeviceToGroup', 'removeDeviceFromGroup']);

// 状态
const selectedGroup = ref(null);
const isEdit = ref(false);
const showForm = ref(false);
const nameError = ref('');

// 表单数据
const formData = ref({
  name: '',
  description: ''
});

// 设备管理相关状态
const showAddDevices = ref(false);
const deviceSearchQuery = ref('');
const groupDeviceSearchQuery = ref('');
const selectedDevicesToAdd = ref([]);
const availableDevices = ref([]);
const filteredAvailableDevices = ref([]);
const filteredGroupDevices = ref([]);
const addSuccess = ref(false);
const addedDevicesCount = ref(0);

// 选择分组
const selectGroup = (group) => {
  selectedGroup.value = group;
  isEdit.value = true;
  showForm.value = true;
  nameError.value = '';

  // 更新表单数据
  formData.value = {
    name: group.name || '',
    description: group.description || ''
  };

  // 重置设备搜索和过滤
  groupDeviceSearchQuery.value = '';
  filteredGroupDevices.value = group.devices || [];
};

// 创建新分组
const createNewGroup = () => {
  selectedGroup.value = null;
  isEdit.value = false;
  showForm.value = true;
  nameError.value = '';

  // 清空表单数据
  formData.value = {
    name: '',
    description: ''
  };
};

// 验证分组名称
const validateGroupName = () => {
  nameError.value = '';

  if (!formData.value.name) {
    return;
  }

  // 检查是否存在同名分组
  const currentId = isEdit.value && selectedGroup.value ? selectedGroup.value.id : null;
  const existingGroup = props.groups.find(
    group => group.name === formData.value.name && group.id !== currentId
  );

  if (existingGroup) {
    nameError.value = `已存在名为"${formData.value.name}"的分组，请使用其他名称`;
  }
};

// 取消编辑
const cancelEdit = () => {
  showForm.value = false;
  selectedGroup.value = null;
};

// 保存设备组
const saveGroup = async () => {
  // 验证表单
  if (!formData.value.name) {
    await alertService.alert({
      title: '表单验证',
      message: '请输入分组名称',
      confirmButtonText: '确定'
    });
    return;
  }

  // 检查是否存在同名分组
  const currentId = isEdit.value && selectedGroup.value ? selectedGroup.value.id : null;
  const existingGroup = props.groups.find(
    group => group.name === formData.value.name && group.id !== currentId
  );

  if (existingGroup) {
    await alertService.alert({
      title: '表单验证',
      message: `已存在名为"${formData.value.name}"的分组，请使用其他名称`,
      confirmButtonText: '确定'
    });
    return;
  }

  // 发送保存事件
  emit('save', {
    ...formData.value,
    id: currentId
  });

  // 保存后关闭表单
  showForm.value = false;
};

// 确认删除分组
const confirmDelete = async () => {
  if (selectedGroup.value && selectedGroup.value.id) {
    const confirmed = await alertService.confirm({
      title: '删除分组',
      message: `确定要删除分组"${selectedGroup.value.name}"吗？此操作不可恢复。`,
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    });

    if (confirmed) {
      emit('delete', selectedGroup.value.id);
      showForm.value = false;
      selectedGroup.value = null;
    }
  }
};

// 检查设备是否在线
const isDeviceOnline = (sn) => {
  const device = props.devices.find(d => d.sn === sn);
  return device ? device.isOnline : false;
};

// 获取设备ID
const getDeviceId = (sn) => {
  const device = props.devices.find(d => d.sn === sn);
  return device ? device.id : '';
};

// 显示添加设备对话框
const showAddDevicesDialog = () => {
  if (!selectedGroup.value) return;

  // 获取可添加的设备（不在当前分组中的设备）
  availableDevices.value = props.devices.filter(
    device => !selectedGroup.value.devices.includes(device.sn)
  );

  // 初始化过滤后的设备列表
  filteredAvailableDevices.value = [...availableDevices.value];

  // 清空搜索和选择
  deviceSearchQuery.value = '';
  selectedDevicesToAdd.value = [];

  // 重置成功提示
  addSuccess.value = false;
  addedDevicesCount.value = 0;

  // 显示对话框
  showAddDevices.value = true;
};

// 关闭添加设备对话框
const closeAddDevicesDialog = () => {
  showAddDevices.value = false;
};

// 过滤可用设备
const filterAvailableDevices = () => {
  if (!deviceSearchQuery.value) {
    filteredAvailableDevices.value = [...availableDevices.value];
    return;
  }

  const query = deviceSearchQuery.value.toLowerCase();
  filteredAvailableDevices.value = availableDevices.value.filter(device =>
    device.sn.toLowerCase().includes(query)
  );
};

// 过滤分组内设备
const filterGroupDevices = () => {
  if (!selectedGroup.value) return;

  if (!groupDeviceSearchQuery.value) {
    filteredGroupDevices.value = [...selectedGroup.value.devices];
    return;
  }

  const query = groupDeviceSearchQuery.value.toLowerCase();
  filteredGroupDevices.value = selectedGroup.value.devices.filter(sn =>
    sn.toLowerCase().includes(query)
  );
};

// 切换设备选择状态
const toggleDeviceSelection = (sn) => {
  const index = selectedDevicesToAdd.value.indexOf(sn);
  if (index === -1) {
    selectedDevicesToAdd.value.push(sn);
  } else {
    selectedDevicesToAdd.value.splice(index, 1);
  }
};

// 添加选中的设备到分组
const addSelectedDevicesToGroup = async () => {
  if (!selectedGroup.value || selectedDevicesToAdd.value.length === 0) return;

  // 记录添加的设备数量
  const count = selectedDevicesToAdd.value.length;

  // 发送添加设备到分组事件
  for (const sn of selectedDevicesToAdd.value) {
    await emit('addDeviceToGroup', {
      groupId: selectedGroup.value.id,
      sn
    });

    // 本地更新分组设备列表（避免等待刷新）
    if (selectedGroup.value && !selectedGroup.value.devices.includes(sn)) {
      selectedGroup.value.devices.push(sn);
    }
  }

  // 更新过滤后的设备列表
  filterGroupDevices();

  // 显示成功提示
  addSuccess.value = true;
  addedDevicesCount.value = count;

  // 清空选择的设备
  selectedDevicesToAdd.value = [];

  // 更新可添加的设备列表（移除已添加的设备）
  availableDevices.value = props.devices.filter(
    device => !selectedGroup.value.devices.includes(device.sn)
  );

  // 更新过滤后的可用设备列表
  filteredAvailableDevices.value = [...availableDevices.value];

  // 如果没有可添加的设备了，则关闭对话框
  if (availableDevices.value.length === 0) {
    closeAddDevicesDialog();
  }

  // 3秒后自动隐藏成功提示
  setTimeout(() => {
    addSuccess.value = false;
  }, 3000);
};

// 从分组中移除设备
const removeDeviceFromGroup = async (sn) => {
  if (!selectedGroup.value) return;

  const confirmed = await alertService.confirm({
    title: '移除设备',
    message: `确定要从分组"${selectedGroup.value.name}"中移除设备 ${sn} 吗？`,
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  });

  if (confirmed) {
    await emit('removeDeviceFromGroup', {
      groupId: selectedGroup.value.id,
      sn
    });

    // 从当前选中的分组中移除该设备（本地更新，避免等待刷新）
    if (selectedGroup.value && selectedGroup.value.devices) {
      const index = selectedGroup.value.devices.indexOf(sn);
      if (index !== -1) {
        selectedGroup.value.devices.splice(index, 1);
      }

      // 从过滤后的设备列表中也移除该设备
      const filteredIndex = filteredGroupDevices.value.indexOf(sn);
      if (filteredIndex !== -1) {
        filteredGroupDevices.value.splice(filteredIndex, 1);
      }
    }
  }
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-overlay, rgba(0, 0, 0, 0.5));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-content {
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow-y: auto;
  animation: dialog-fade-in 0.3s ease;
}

@keyframes dialog-fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.btn-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: color 0.2s ease;
}

.btn-close:hover {
  color: var(--color-text-primary);
}

.dialog-body {
  padding: var(--spacing-md);
}

/* 布局 */
.dialog-layout {
  display: flex;
  gap: var(--spacing-md);
  height: 450px;
}

/* 分组列表 */
.group-list-container {
  width: 200px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 右侧内容区 */
.right-content {
  flex: 1;
  display: flex;
  gap: var(--spacing-md);
}

.group-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-background-light);
}

.group-list-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.group-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xs);
}

.group-item {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: var(--spacing-xs);
}

.group-item:hover {
  background-color: var(--color-background-hover);
}

.group-item.active {
  background-color: var(--color-primary-light, rgba(33, 150, 243, 0.1));
  border-left: 3px solid var(--color-primary);
}

.group-item-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.group-item-count {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.empty-group-list {
  padding: var(--spacing-md);
  text-align: center;
  color: var(--color-text-secondary);
  font-size: 14px;
}

/* 表单容器 */
.group-form-container {
  width: 280px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
}

/* 设备列表容器 */
.devices-container {
  flex: 1;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 添加设备按钮容器 */
.add-devices-button-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: var(--color-background-light);
}

.form-header {
  margin-bottom: var(--spacing-md);
}

.form-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

/* 暗色主题下的输入框样式 */
[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--color-primary);
  outline: none;
}

.form-error {
  color: var(--color-danger, #f44336);
  font-size: 12px;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  padding-top: var(--spacing-md);
}

.form-actions-right {
  display: flex;
  gap: var(--spacing-sm);
}

/* 空表单状态 */
.empty-state {
  justify-content: center;
  align-items: center;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
}

.empty-form-content {
  text-align: center;
  padding: var(--spacing-lg);
}

.empty-icon {
  font-size: 48px;
  color: var(--color-text-disabled);
  margin-bottom: var(--spacing-md);
}

.empty-form-content p {
  margin-bottom: var(--spacing-md);
  color: var(--color-text-secondary);
  max-width: 250px;
}

/* 按钮样式 */
.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn i {
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-light, white);
  border: none;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-background-dark);
}

.btn-danger {
  background-color: var(--color-danger, #f44336);
  color: var(--color-text-light, white);
  border: none;
}

.btn-danger:hover {
  background-color: var(--color-danger-dark, #d32f2f);
}

/* 设备列表样式 */
.devices-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.devices-header h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.devices-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.devices-actions .search-box {
  position: relative;
}

.devices-actions .search-box input {
  padding: 6px 10px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  font-size: 13px;
  width: 150px;
}

/* 暗色主题下的搜索框样式 */
[data-theme="dark"] .devices-actions .search-box input {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
}

.devices-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: var(--color-background-light);
}

.empty-devices {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--color-text-secondary);
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 150px;
}

.empty-devices .empty-icon {
  font-size: 32px;
  color: var(--color-text-disabled);
  margin-bottom: var(--spacing-sm);
}

.empty-devices p {
  margin-bottom: var(--spacing-md);
}

.device-items {
  display: flex;
  flex-direction: column;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--color-border);
  transition: background-color 0.2s;
}

.device-item:hover {
  background-color: var(--color-background-hover);
}

.device-item:last-child {
  border-bottom: none;
}

.device-info {
  display: flex;
  flex-direction: column;
}

.device-id-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-sn {
  font-weight: 500;
  font-size: 14px;
  color: var(--color-text-primary);
}

.device-id {
  font-size: 12px;
  color: var(--color-text-secondary);
  background-color: var(--color-background-light);
  padding: 1px 4px;
  border-radius: 3px;
}

.device-status {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: 2px;
}

.device-status.online {
  color: var(--color-success, #4caf50);
}

.btn-icon {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.btn-delete {
  background: none;
  border: 1px solid var(--color-danger, #f44336);
  color: var(--color-danger, #f44336);
  cursor: pointer;
  padding: 2px 8px;
  font-size: 12px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  min-width: 40px;
}

.btn-delete:hover {
  background-color: var(--color-danger, #f44336);
  color: var(--color-text-light, white);
}

/* 添加设备对话框 */
.add-devices-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-overlay-dark, rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1200; /* 确保比主对话框的z-index高 */
}

.add-devices-content {
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 550px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: dialog-fade-in 0.3s ease;
}

.add-devices-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
  background-color: var(--color-background-light);
}

.add-devices-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.add-devices-body {
  padding: var(--spacing-lg);
  flex: 1;
  overflow-y: auto;
  max-height: 450px;
}

.add-devices-body .success-message {
  background-color: var(--color-success-light, rgba(76, 175, 80, 0.1));
  color: var(--color-success, #4caf50);
  padding: 10px 16px;
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  animation: fade-in 0.3s ease;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.add-devices-body .search-box {
  margin-bottom: var(--spacing-md);
  position: relative;
}

.add-devices-body .search-box input {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: 14px;
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  transition: border-color 0.2s;
}

/* 暗色主题下的搜索框样式 */
[data-theme="dark"] .add-devices-body .search-box input {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
}

.add-devices-body .search-box input:focus {
  border-color: var(--color-primary);
  outline: none;
}

.devices-selection {
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  max-height: 350px;
  overflow-y: auto;
  background-color: var(--color-background-light);
}

.device-items.selectable .device-item {
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 8px 12px;
}

.device-items.selectable .device-item:hover {
  background-color: var(--color-background-hover);
}

.device-items.selectable .device-item.selected {
  background-color: var(--color-primary-light, rgba(33, 150, 243, 0.1));
  border-left: 3px solid var(--color-primary);
}

.device-checkbox {
  display: flex;
  align-items: center;
}

.device-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--color-primary);
}

.add-devices-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--color-border);
  background-color: var(--color-background-light);
}

.selection-info {
  font-size: 14px;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
}
</style>
