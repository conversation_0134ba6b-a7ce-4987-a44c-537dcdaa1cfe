<template>
  <div class="custom-dialog-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="custom-dialog" :style="{ width: width || 'auto' }" @click.stop>
      <img
        v-if="showClose"
        class="close-btn"
        @click="handleClose"
        src="@assets/svg/close.svg"
        alt=""
      />
      <div class="custom-dialog-header">
        {{ title }}
      </div>
      <div class="custom-dialog-body">
        <component
          :is="component"
          v-bind="componentProps"
          v-model="localModelValue"
          @refresh="handleRefresh"
        />
      </div>
      <div
        class="custom-dialog-footer"
        v-if="showConfirmButton || showCancelButton"
      >
        <button
          v-if="showCancelButton"
          class="btn btn-secondary"
          @click="handleCancel"
        >
          {{ cancelButtonText }}
        </button>
        <button
          v-if="showConfirmButton"
          class="btn btn-primary"
          @click="handleConfirm"
        >
          {{ confirmButtonText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";

// 定义组件属性
const props = defineProps({
  title: {
    type: String,
    default: "对话框",
  },
  component: {
    type: Object,
    required: true,
  },
  componentProps: {
    type: Object,
    default: () => ({}),
  },
  modelValue: {
    type: [Array, Object, String, Number, Boolean],
    default: null,
  },
  width: {
    type: String,
    default: "500px",
  },
  confirmButtonText: {
    type: String,
    default: "确定",
  },
  cancelButtonText: {
    type: String,
    default: "取消",
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  showConfirmButton: {
    type: Boolean,
    default: true,
  },
  showCancelButton: {
    type: Boolean,
    default: true,
  },
  closeOnOverlayClick: {
    type: Boolean,
    default: false,
  },
  // 刷新回调
  onRefresh: {
    type: Function,
    default: null,
  },
});

// 定义事件
const emit = defineEmits(["confirm", "cancel", "close", "update:modelValue"]);

// 内部状态
const visible = ref(false);
const localModelValue = ref(props.modelValue);

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    localModelValue.value = newValue;
  }
);

// 监听localModelValue变化
watch(
  () => localModelValue.value,
  (newValue) => {
    emit("update:modelValue", newValue);
  }
);

// 方法 - 打开对话框
const open = () => {
  visible.value = true;
};

// 方法 - 关闭对话框
const close = () => {
  visible.value = false;
};

// 方法 - 处理确认按钮点击
const handleConfirm = () => {
  emit("confirm", localModelValue.value);
};

// 方法 - 处理取消按钮点击
const handleCancel = () => {
  emit("cancel");
};

// 方法 - 处理关闭按钮点击
const handleClose = () => {
  emit("close");
};

// 方法 - 处理遮罩层点击
const handleOverlayClick = () => {
  if (props.closeOnOverlayClick) {
    emit("close");
  }
};

// 方法 - 处理刷新事件
const handleRefresh = () => {
  if (props.onRefresh && typeof props.onRefresh === "function") {
    props.onRefresh();
  }
};

// 暴露方法
defineExpose({
  open,
  close,
});
</script>

<style scoped>
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.custom-dialog {
  background-color: var(--color-card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 34px;

  position: relative;
  background-color: var(--color-box-bg);
}

.custom-dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  text-align: center;

  color: var(--color-menu-text);
  font-size: 24px;
  text-align: center;
  font-style: normal;
  font-weight: 480;
  line-height: 18px;
}

.close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 28px;
  height: 28px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background-color: var(--color-background-dark);
  color: var(--color-text-primary);
}

/* 暗色主题下的关闭按钮悬停效果 */
[data-theme="dark"] .close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.custom-dialog-body {
  padding: var(--spacing-md);
  overflow-y: auto;
  flex: 1;
  margin: 34px 0 10px;
}

.custom-dialog-footer {
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
}

.btn {
  padding: 8px 14px;
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  width: 220px;
}

.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* 暗色主题下的次要按钮 */
[data-theme="dark"] .btn-secondary {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

[data-theme="dark"] .btn-secondary:hover {
  border-color: var(--color-primary-light);
  color: var(--color-primary-light);
}
</style>
