<template>
  <div v-if="visible" class="dialog-overlay">
    <div class="dialog-container">
      <div class="dialog-header">
        <h3>{{ title }}</h3>
        <button v-if="!updating" class="close-btn" @click="handleClose">&times;</button>
      </div>

      <div class="dialog-body">
        <!-- 检查更新状态 -->
        <div v-if="status === 'checking'" class="checking-status">
          <div class="spinner"></div>
          <span>正在检查更新...</span>
        </div>

        <!-- 有新版本可用 -->
        <div v-if="status === 'available'" class="update-info">
          <div class="icon-success">✓</div>
          <h3>发现新版本 {{ updateInfo.version }}</h3>
          <div class="update-description">
            <p>{{ updateInfo.description }}</p>
            <p class="release-date">发布日期：{{ updateInfo.releaseDate }}</p>
          </div>
        </div>

        <!-- 下载进度 -->
        <div v-if="status === 'downloading'" class="download-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: Math.round(progress * 100) + '%' }"></div>
          </div>
          <p class="progress-text">{{ Math.round(progress * 100) }}%</p>
          <p class="download-info">
            已下载：{{ formatSize(downloaded) }} / {{ formatSize(total) }}
          </p>
        </div>

        <!-- 准备安装 -->
        <div v-if="status === 'ready'" class="install-ready">
          <div class="icon-success">✓</div>
          <span>下载完成，准备安装</span>
        </div>

        <!-- 最新版本 -->
        <div v-if="status === 'latest'" class="latest-version">
          <div class="icon-success">✓</div>
          <span>当前已是最新版本</span>
        </div>

        <!-- 错误状态 -->
        <div v-if="status === 'error'" class="error-status">
          <div class="icon-error">✕</div>
          <span>{{ error }}</span>
        </div>
      </div>

      <!-- 对话框按钮 -->
      <div class="dialog-footer">
        <button class="cancel-btn" @click="handleClose" :disabled="updating">
          {{ closeButtonText }}
        </button>
        <button
          v-if="status === 'available'"
          class="confirm-btn"
          @click="handleDownload"
          :disabled="updating"
        >
          立即更新
        </button>
        <button
          v-if="status === 'ready'"
          class="confirm-btn"
          @click="handleInstall"
          :disabled="updating"
        >
          立即安装
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useElectronAPI } from '../plugins/electron';

const electronAPI = useElectronAPI();

// 状态
const visible = ref(false);
const status = ref('checking'); // checking, available, downloading, ready, latest, error
const updateInfo = ref(null);
const progress = ref(0);
const downloaded = ref(0);
const total = ref(0);
const error = ref('');
const updating = ref(false);

// 计算属性
const title = computed(() => {
  switch (status.value) {
    case 'checking':
      return '检查更新';
    case 'available':
      return '发现新版本';
    case 'downloading':
      return '正在下载';
    case 'ready':
      return '更新就绪';
    case 'latest':
      return '检查更新';
    case 'error':
      return '更新失败';
    default:
      return '软件更新';
  }
});

const closeButtonText = computed(() => {
  if (status.value === 'available') {
    return '稍后更新';
  }
  if (status.value === 'latest' || status.value === 'error') {
    return '关闭';
  }
  return '取消';
});

// 方法
const checkUpdate = async () => {
  try {
    status.value = 'checking';
    updating.value = true;
    visible.value = true;

    const result = await electronAPI.checkUpdate();
    if (result) {
      updateInfo.value = result;
      status.value = 'available';
    } else {
      status.value = 'latest';
      setTimeout(() => {
        visible.value = false;
      }, 1500);
    }
  } catch (err) {
    status.value = 'error';
    error.value = err.message || '检查更新失败';
  } finally {
    updating.value = false;
  }
};

const handleDownload = async () => {
  try {
    status.value = 'downloading';
    updating.value = true;

    // 设置下载进度监听
    const progressHandler = (data) => {
      progress.value = data.progress;
      downloaded.value = data.loaded;
      total.value = data.total;
    };

    electronAPI.onUpdateProgress(progressHandler);

    // 开始下载
    await electronAPI.downloadUpdate();

    // 移除进度监听
    electronAPI.offUpdateProgress(progressHandler);

    // 更新状态
    status.value = 'ready';
    updating.value = false;  // 重置更新状态
  } catch (err) {
    status.value = 'error';
    error.value = err.message || '下载更新失败';
    updating.value = false;  // 确保在错误时也重置更新状态
  }
};

const handleInstall = async () => {
  try {
    updating.value = true;  // 开始安装时设置更新状态
    await electronAPI.installUpdate();
  } catch (err) {
    status.value = 'error';
    error.value = err.message || '安装更新失败';
    updating.value = false;  // 安装失败时重置更新状态
  }
};

const handleClose = () => {
  if (updating.value) return;
  visible.value = false;
};

const formatSize = (bytes) => {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

// 导出方法供外部调用
defineExpose({
  checkUpdate
});
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-container {
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 400px;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--color-text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
}

.close-btn:hover {
  color: var(--color-text-primary);
}

.dialog-body {
  padding: var(--spacing-md);
  overflow-y: auto;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.checking-status,
.latest-version,
.error-status,
.install-ready {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-border);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.icon-success {
  color: var(--color-success);
  font-size: 20px;
}

.icon-error {
  color: var(--color-danger);
  font-size: 20px;
}

.update-info {
  width: 100%;
}

.update-description {
  margin-top: var(--spacing-md);
  text-align: left;
  color: var(--color-text-secondary);
}

.release-date {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: var(--spacing-sm);
}

.download-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: var(--color-background-light);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  transition: width 0.3s ease;
}

.progress-text {
  margin: var(--spacing-sm) 0;
  color: var(--color-primary);
  font-weight: bold;
}

.download-info {
  margin-top: var(--spacing-sm);
  font-size: 12px;
  color: var(--color-text-secondary);
}

.dialog-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.cancel-btn,
.confirm-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-btn {
  background-color: var(--color-button-secondary-bg);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.cancel-btn:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.confirm-btn {
  background-color: var(--color-primary);
  color: var(--color-white);
  border: none;
}

.confirm-btn:hover {
  background-color: var(--color-primary-dark);
}

.confirm-btn:disabled,
.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 暗色主题适配 */
[data-theme="dark"] .dialog-container {
  background-color: var(--color-background-dark);
}

[data-theme="dark"] .cancel-btn:hover {
  border-color: var(--color-primary-light);
  color: var(--color-primary-light);
}

[data-theme="dark"] .confirm-btn:hover {
  background-color: var(--color-primary-dark);
}
</style> 