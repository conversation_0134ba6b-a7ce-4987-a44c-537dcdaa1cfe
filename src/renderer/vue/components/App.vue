<template>
  <div class="app-container">
    <!-- 标题栏 -->
    <div class="titlebar">
      <div class="titlebar-drag"></div>
      <div class="window-controls">
        <div class="window-control" @click="minimizeWindow">
          <img :src="minIcon" alt="最小化" />
        </div>
        <div class="window-control" @click="maximizeWindow">
          <img :src="maxIcon" alt="最大化" />
        </div>
        <div class="window-control" id="close-btn" @click="closeWindow">
          <img :src="closeIcon" alt="关闭" />
        </div>
      </div>
    </div>

    <div class="main-container">
      <!-- 左侧菜单 -->
      <div id="menu">
        <div class="logo-wrapper">
          <img :src="logoIcon" alt="YVR Logo" class="menu-logo" />
        </div>
        <div id="h-line"></div>
        <div
          v-for="group in menuGroups"
          :key="group.class"
          :class="group.class"
        >
          <router-link
            v-for="item in group.items"
            :key="item.path"
            :to="item.path"
            class="menu-item"
            :class="{
              active: activeMenu === item.path,
              disabled: isMenuItemDisabled(item.path),
            }"
            @click.prevent="
              !isMenuItemDisabled(item.path) && $router.push(item.path)
            "
          >
            <img
              :src="getIcon(item).value"
              :alt="item.label"
              class="menu-icon"
            />
            <span>{{ item.label }}</span>
          </router-link>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div id="content">
        <router-view v-slot="{ Component }">
          <transition
            :name="transitionName"
            mode="out-in"
            @before-leave="beforeLeave"
            @enter="enter"
            @after-enter="afterEnter"
          >
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from "vue";
import { useRoute } from "vue-router";
import { useElectronAPI } from "../plugins/electron";
import { useDeployStore } from "../stores/deploy";

// 导入图片资源
import defaultLogoIcon from "../../assets/images/yvr_logo.png";
import minIcon from "../../assets/svg/windows_min.svg";
import maxIcon from "../../assets/svg/windows_mid.svg";
import closeIcon from "../../assets/svg/windows_close.svg";

const route = useRoute();
const deployStore = useDeployStore();

const menuGroups = [
  {
    class: "menu-main",
    items: [
      {
        path: "/devices",
        label: "设备管理",
        icon: "yvr_device_manager",
        iconDark: "yvr_device_manager_dark",
      },
      {
        path: "/control",
        label: "播控",
        icon: "yvr_play_tab",
        iconDark: "yvr_play_tab_dark",
      },
    ],
  },
  {
    class: "menu-bottom",
    items: [
      {
        path: "/solutions",
        label: "方案管理",
        icon: "yvr_plan_manager",
        iconDark: "yvr_plan_manager_dark",
      },
      {
        path: "/settings",
        label: "设置",
        icon: "yvr_setting",
        iconDark: "yvr_setting_dark",
      },
    ],
  },
];

const activeMenu = ref("devices");
const mainMenuPaths = computed(() => {
  return menuGroups.flatMap((group) => group.items.map((item) => item.path));
});

// 计算菜单项是否禁用
const isMenuItemDisabled = (path) => {
  if (deployStore.isDeploying) {
    // 在部署过程中，禁用所有菜单项
    return true;
  }
  return false;
};

watch(
  () => route.path,
  (newPath) => {
    const matched = mainMenuPaths.value.find((path) => newPath === path);
    if (matched) {
      activeMenu.value = matched;
    }
  },
  { immediate: true }
);

// 修改图标获取逻辑
const getIcon = (item) => {
  return computed(() => {
    const isActiveItem = activeMenu.value === item.path;
    const fileName = isActiveItem ? item.icon : item.iconDark;
    return new URL(`../../assets/images/${fileName}.png`, import.meta.url).href;
  });
};

// Logo 状态
const logoIcon = ref(defaultLogoIcon);

const electronAPI = useElectronAPI();

// 状态
const transitionName = ref("fade");

// 窗口控制函数
const minimizeWindow = () => electronAPI.minimizeWindow();
const maximizeWindow = () => electronAPI.maximizeWindow();
const closeWindow = () => electronAPI.closeWindow();

// 页面过渡动画
const beforeLeave = (el) => {
  el.style.position = "absolute";
  el.style.width = "100%";
};

const enter = (el) => {
  el.style.position = "absolute";
  el.style.width = "100%";
};

const afterEnter = (el) => {
  el.style.position = "";
};

// 监听路由变化，设置过渡动画方向
watch(
  () => route.path,
  (newPath, oldPath) => {
    if (!oldPath) {
      transitionName.value = "fade";
      return;
    }

    // 根据菜单项的顺序确定动画方向
    const routeOrder = {
      "/devices": 1,
      "/control": 2,
      "/solutions": 3,
      "/settings": 4,
    };

    const newOrder = routeOrder[newPath] || 0;
    const oldOrder = routeOrder[oldPath] || 0;

    transitionName.value = newOrder > oldOrder ? "slide-left" : "slide-right";
  }
);

// 加载自定义 Logo
const loadCustomLogo = async () => {
  try {
    // 获取应用 Logo
    const appLogoPath = await electronAPI.getAppLogo();

    // 如果有自定义 Logo，使用自定义 Logo
    if (appLogoPath) {
      logoIcon.value = appLogoPath;
    } else {
      // 否则使用默认 Logo
      logoIcon.value = defaultLogoIcon;
    }
  } catch (error) {
    console.error("获取自定义 Logo 失败:", error);
    logoIcon.value = defaultLogoIcon;
  }
};

// 监听 Logo 更新事件
const setupLogoUpdateListener = () => {
  const handler = (event) => {
    console.log("Logo 已更新，重新加载...");
    loadCustomLogo();
  };

  // 添加事件监听器
  window.electronAPI.on("app-logo-updated", handler);

  // 返回处理函数，以便在组件卸载时移除
  return handler;
};

// 生命周期钩子
let logoUpdateHandler = null;

onMounted(() => {
  // 加载自定义 Logo
  loadCustomLogo();

  // 设置 Logo 更新事件监听器
  logoUpdateHandler = setupLogoUpdateListener();
});

onUnmounted(() => {
  // 移除 Logo 更新事件监听器
  if (logoUpdateHandler) {
    window.electronAPI.off("app-logo-updated", logoUpdateHandler);
  }
});
</script>

<style>
/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 左右滑动动画 */
/* .slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
}

.slide-left-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-left-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right-enter-from {
  transform: translateX(-30px);
  opacity: 0;
}

.slide-right-leave-to {
  transform: translateX(30px);
  opacity: 0;
} */

/* 应用容器 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: var(--color-background);
  position: fixed; /* 固定定位，防止出现滚动条 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* 主容器 */
.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: absolute; /* 绝对定位 */
  top: 32px; /* 标题栏高度 */
  left: 0;
  right: 0;
  bottom: 0;
}

/* 内容区域 */
#content {
  flex: 1;
  overflow: hidden; /* 修改为hidden，防止滚动条出现 */
  position: relative;
  background-color: var(--color-menu);
  padding: 0; /* 移除内边距，让页面组件自己控制内边距 */
  display: flex; /* 使用flex布局 */
  flex-direction: column; /* 垂直方向排列 */
  width: 100%; /* 确保宽度为100% */
}

/* 标题栏 */
.titlebar {
  height: 32px;
  background: var(--color-menu);
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.titlebar-drag {
  flex: 1;
  height: 100%;
  -webkit-app-region: drag;
}

.window-controls {
  display: flex;
  -webkit-app-region: no-drag;
}

.window-control {
  width: 46px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  cursor: pointer;
}

.window-control:hover {
  background: var(--color-black-trans);
}

#close-btn:hover {
  background: var(--color-danger);
}

/* Logo样式 */

/* 菜单样式 */
#menu {
  width: 92px;
  background-color: var(--color-menu);
  color: var(--color-white);
  padding: 0 0 var(--spacing-md) 0; /* 只保留底部padding */
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%; /* 使用100%高度 */
  position: relative; /* 相对定位 */
}

.logo-wrapper {
  padding: 5px;
  text-align: center;
  position: relative;
}

#h-line {
  width: 70%;
  margin: 5px 0;
  height: 1px;
  background-color: var(--color-split-line);
}

.menu-logo {
  width: 45px;
  height: 45px;
  object-fit: contain;
}

.menu-main {
  padding-top: var(--spacing-md);
  flex-grow: 1;
}

.menu-bottom {
  padding-top: var(--spacing-sm);
  padding-bottom: var(--spacing-lg);
  position: relative;
}

.menu-item {
  height: 64px;
  width: 64px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /* transition: all 0.3s; */
  font-size: 14px;
  position: relative;
  text-decoration: none;
  margin-bottom: 12px;
}

.menu-icon {
  width: 32px;
  height: 32px;
  transition: opacity 0.3s;
}

.menu-item span {
  font-size: 10px;
  font-weight: 500;
  color: var(--color-menu-text);
  /* transition: color 0.3s; */
}

.menu-item.active {
  color: var(--color-white-trans);
  background: linear-gradient(
    90deg,
    var(--color-menu-active) 0%,
    var(--color-menu-active-end) 103.58%
  );
}

.menu-item.active .menu-icon {
  opacity: 1;
}

.menu-item.active span {
  color: var(--color-white);
  font-weight: 600;
}

/* 禁用状态的菜单项样式 */
.menu-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.menu-item.disabled:hover {
  background: none;
}

.menu-item.disabled .menu-icon {
  filter: grayscale(100%);
}

.menu-item.disabled span {
  color: var(--color-menu-text-disabled);
}
</style>
