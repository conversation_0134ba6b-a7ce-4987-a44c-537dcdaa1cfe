<template>
  <div class="video-container">
    <canvas ref="videoCanvas"></canvas>
    <div v-if="errorInfo.show" class="status error-status">
      <div>{{ errorMessage }}</div>
      <button @click="retryConnection" class="retry-btn">重试连接</button>
    </div>
    <div v-else-if="status" class="status">{{ status }}</div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, defineProps, defineEmits } from 'vue';
import JSMpeg from '@cycjimmy/jsmpeg-player';

// 定义props
const props = defineProps({
  // WebSocket流地址
  streamUrl: {
    type: String,
    required: true
  },
  // 是否自动播放
  autoPlay: {
    type: Boolean,
    default: true
  },
  // 是否启用音频
  audio: {
    type: Boolean,
    default: false
  },
  // 是否显示控制条
  showControls: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['play', 'stop', 'error']);

// 状态变量
const videoCanvas = ref(null);
const status = ref('');
const errorMessage = ref('连接错误');
const errorInfo = reactive({
  show: false,
  time: new Date(),
  count: 0
});

// 播放器实例
let player = null;
let connectionTimeout = null;

// 记录错误信息
const recordError = (message = '连接错误') => {
  errorInfo.show = true;
  errorInfo.time = new Date();
  errorInfo.count++;
  errorMessage.value = message;

  // 触发错误事件
  emit('error', { message, time: errorInfo.time, count: errorInfo.count });

  console.error(`播放器错误: ${message}`);
};

// 重试连接
const retryConnection = () => {
  if (connectionTimeout) {
    clearTimeout(connectionTimeout);
  }

  if (player) {
    player.destroy();
    player = null;
  }

  // 重置错误状态
  errorInfo.show = false;

  // 重新初始化播放器
  initPlayer();
};

// 初始化播放器
const initPlayer = () => {
  try {
    if (!props.streamUrl) {
      recordError('无效的流地址');
      return;
    }

    // 使用传入的WebSocket URL
    const streamUrl = props.streamUrl;

    status.value = '正在连接...';

    // 设置连接超时
    connectionTimeout = setTimeout(() => {
      if (!player) {
        recordError('连接超时');
      }
    }, 15000); // 增加到15秒

    // 创建播放器
    console.log(`正在连接WebSocket流: ${streamUrl}`);
    player = new JSMpeg.Player(streamUrl, {
      canvas: videoCanvas.value,
      autoplay: props.autoPlay,
      audio: props.audio,
      loop: false,
      pauseWhenHidden: false,
      videoBufferSize: 1024 * 1024 * 4, // 增加到4MB
      audioBufferSize: props.audio ? 256 * 1024 : 0, // 增加到256KB
      // 增加重试次数和超时时间
      reconnectInterval: 5000, // 增加到5秒
      maxRetries: 10, // 增加到10次

      // 事件处理
      onSourceEstablished: () => {
        clearTimeout(connectionTimeout);
        // 视频开始播放后清除状态文本
        status.value = '';
        emit('play');
        console.log('WebSocket流连接成功，视频开始播放');
      },

      onSourceCompleted: () => {
        status.value = '';
        emit('stop');
        console.log('WebSocket流已完成');
      },

      onStalled: () => {
        // 不显示缓冲状态文本
        console.log('视频缓冲中...');
      },

      onError: (error) => {
        console.error(`WebSocket流错误: ${error}`);
        recordError(`播放错误: ${error}`);
      }
    });
  } catch (error) {
    console.error(`初始化播放器失败: ${error.message}`, error);
    recordError(`初始化失败: ${error.message}`);
  }
};

// 停止播放
const stop = () => {
  if (connectionTimeout) {
    clearTimeout(connectionTimeout);
  }

  if (player) {
    player.destroy();
    player = null;
  }

  status.value = '';
  emit('stop');
};

// 组件挂载时初始化播放器
onMounted(() => {
  if (props.autoPlay) {
    initPlayer();
  }
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  stop();
});

// 暴露方法给父组件
defineExpose({
  play: initPlayer,
  stop,
  retry: retryConnection
});
</script>

<style scoped>
.video-container {
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
}

canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.status {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: var(--color-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  text-align: center;
  font-size: 14px;
  max-width: 80%;
  z-index: 2;
}

.error-status {
  color: var(--color-danger-light);
}

.retry-btn {
  margin-top: var(--spacing-sm);
  padding: 6px 12px;
  background: var(--color-primary);
  border: none;
  border-radius: var(--border-radius-sm);
  color: var(--color-white);
  cursor: pointer;
  font-size: 12px;
  transition: background-color var(--transition-fast);
}

.retry-btn:hover {
  background: var(--color-primary-light);
}
</style>