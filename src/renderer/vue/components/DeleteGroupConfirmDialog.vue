<template>
  <div class="delete-group-dialog">
    <div class="dialog-message">
      <p>
        确定要删除{{ isSubCategory ? "子分类" : "分组" }} "{{ groupName }}"
        及其下所有资源？此操作不可撤销。
      </p>
    </div>

    <!-- 隐藏删除资源选项，但默认启用 -->
    <!-- deleteResources 已在 setup 中设置为 true -->
  </div>
</template>

<script setup>
import { ref } from "vue";

// 组件属性
const props = defineProps({
  /**
   * 分组名称
   */
  groupName: {
    type: String,
    required: true,
  },
  /**
   * 是否为子分类
   */
  isSubCategory: {
    type: Boolean,
    default: false,
  },
});

// 是否删除资源，默认为 true
const deleteResources = ref(true);

// 导出组件数据，供父组件使用
defineExpose({
  deleteResources,
});
</script>

<style scoped>
.delete-group-dialog {
  padding: 10px 0;
}

.dialog-message {
  margin-bottom: 15px;
}

.warning-text {
  color: var(--color-danger, #f44336);
  font-weight: 500;
}
</style>
