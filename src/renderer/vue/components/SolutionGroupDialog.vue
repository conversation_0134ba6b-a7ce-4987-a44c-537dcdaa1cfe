<template>
  <div class="dialog-overlay" v-if="visible" @click.self="onCancel">
    <div class="dialog-container">
      <div class="dialog-header">
        <h3>{{ isEdit ? '编辑分组' : '添加分组' }}</h3>
        <button class="close-btn" @click="onCancel">&times;</button>
      </div>
      <div class="dialog-body">
        <div class="form-group" v-if="enableSubcategories && !isEdit">
          <label>分组类型</label>
          <div class="radio-group">
            <label class="radio-label">
              <input type="radio" v-model="groupType" value="main" />
              <span>主分类</span>
            </label>
            <label class="radio-label">
              <input type="radio" v-model="groupType" value="sub" />
              <span>子分类</span>
            </label>
          </div>
        </div>

        <div class="form-group" v-if="enableSubcategories && groupType === 'sub' && !isEdit">
          <label for="parentGroup">所属主分类</label>
          <select
            id="parentGroup"
            v-model="parentGroupName"
            class="select-input"
          >
            <option v-for="group in mainCategories" :key="group" :value="group">
              {{ group }}
            </option>
          </select>
          <div class="error-message" v-if="parentError">{{ parentError }}</div>
        </div>

        <div class="form-group">
          <label for="groupName">{{ groupType === 'sub' && enableSubcategories ? '子分类名称' : '分组名称' }}</label>
          <input
            type="text"
            id="groupName"
            v-model="groupName"
            :placeholder="groupType === 'sub' && enableSubcategories ? '请输入子分类名称' : '请输入分组名称'"
            @keyup.enter.prevent="onConfirm"
          />
          <div class="error-message" v-if="nameError">{{ nameError }}</div>
        </div>

        <div class="form-group">
          <label for="groupDescription">分组描述</label>
          <textarea
            id="groupDescription"
            v-model="groupDescription"
            placeholder="请输入分组描述（可选）"
            rows="3"
          ></textarea>
        </div>
      </div>
      <div class="dialog-footer">
        <button class="cancel-btn" @click="onCancel">取消</button>
        <button class="confirm-btn" @click="onConfirm" :disabled="!isValid">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useElectronAPI } from '../plugins/electron';
import {
  GROUP_SEPARATOR,
  isSubcategory,
  getMainCategory,
  getSubcategory,
  createFullGroupName,
  getAllMainCategories
} from '../utils/group-utils';

const electronAPI = useElectronAPI();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editGroup: {
    type: Object,
    default: null
  },
  existingGroups: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['cancel', 'confirm']);

// 表单数据
const groupName = ref('');
const groupDescription = ref('');
const groupType = ref('main'); // 'main' 或 'sub'
const parentGroupName = ref('');
const nameError = ref('');
const parentError = ref('');

// 是否启用资源子分类
const enableSubcategories = ref(false);

// 获取设置
const fetchSettings = async () => {
  try {
    const settings = await electronAPI.getSettings();
    enableSubcategories.value = settings?.basic?.enableResourceSubcategories || false;
  } catch (error) {
    console.error('获取设置失败:', error);
    enableSubcategories.value = false;
  }
};

// 组件挂载时获取设置
onMounted(fetchSettings);

// 是否是编辑模式
const isEdit = computed(() => !!props.editGroup);

// 获取所有主分类
const mainCategories = computed(() => {
  // 如果是编辑模式，不需要显示主分类选择
  if (isEdit.value) return [];

  // 从现有分组中提取所有主分类
  return getAllMainCategories(props.existingGroups);
});

// 表单是否有效
const isValid = computed(() => {
  // 基本验证：分组名称不能为空且没有错误
  const basicValid = groupName.value.trim() !== '' && !nameError.value;

  // 如果是子分类，还需要验证父分类
  if (enableSubcategories.value && groupType.value === 'sub' && !isEdit.value) {
    return basicValid && parentGroupName.value && !parentError.value;
  }

  return basicValid;
});

// 监听编辑对象变化
watch(() => props.editGroup, (newVal) => {
  if (newVal) {
    // 如果是编辑模式
    if (isSubcategory(newVal.name)) {
      // 如果是子分类
      groupType.value = 'sub';
      parentGroupName.value = getMainCategory(newVal.name);
      groupName.value = getSubcategory(newVal.name) || '';
    } else {
      // 如果是主分类
      groupType.value = 'main';
      groupName.value = newVal.name || '';
    }
    groupDescription.value = newVal.description || '';
  } else {
    // 如果是添加模式，重置表单
    resetForm();
  }
}, { immediate: true });

// 监听分组名称变化，进行验证
watch(groupName, (newVal) => {
  validateName(newVal);
});

// 监听父分类变化，进行验证
watch(parentGroupName, (newVal) => {
  if (enableSubcategories.value && groupType.value === 'sub') {
    validateParent(newVal);
  }
});

// 验证分组名称
function validateName(name) {
  nameError.value = '';

  if (!name.trim()) {
    nameError.value = '分组名称不能为空';
    return false;
  }

  // 检查名称是否包含分隔符
  if (name.includes(GROUP_SEPARATOR)) {
    nameError.value = `分组名称不能包含 "${GROUP_SEPARATOR}" 字符`;
    return false;
  }

  // 构建完整的分组名称（如果是子分类）
  let fullName = name.trim();
  if (enableSubcategories.value && groupType.value === 'sub' && !isEdit.value) {
    if (!parentGroupName.value) {
      return true; // 父分类未选择，不进行重名检查
    }
    fullName = createFullGroupName(parentGroupName.value, name.trim());
  }

  // 检查是否与现有分组重名（编辑模式下排除自身）
  const isDuplicate = props.existingGroups.some(group => {
    if (isEdit.value && props.editGroup.name === group) {
      return false; // 编辑模式下，与自身名称相同不算重复
    }
    return group.toLowerCase() === fullName.toLowerCase();
  });

  if (isDuplicate) {
    nameError.value = '分组名称已存在';
    return false;
  }

  return true;
}

// 验证父分类
function validateParent(parent) {
  parentError.value = '';

  if (!parent) {
    parentError.value = '请选择父分类';
    return false;
  }

  return true;
}

// 重置表单
function resetForm() {
  groupName.value = '';
  groupDescription.value = '';
  groupType.value = 'main';
  parentGroupName.value = '';
  nameError.value = '';
  parentError.value = '';
}

// 取消按钮点击事件
function onCancel() {
  resetForm();
  emit('cancel');
}

// 确认按钮点击事件
function onConfirm() {
  if (!isValid.value) return;

  let finalName = groupName.value.trim();

  // 如果是子分类，构建完整的分组名称
  if (enableSubcategories.value && groupType.value === 'sub' && !isEdit.value) {
    finalName = createFullGroupName(parentGroupName.value, finalName);
  }

  const groupData = {
    name: finalName,
    description: groupDescription.value.trim(),
    isSubcategory: enableSubcategories.value && groupType.value === 'sub'
  };

  emit('confirm', groupData);
  resetForm();
}
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-container {
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 400px;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--color-text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
}

.dialog-body {
  padding: var(--spacing-md);
  overflow-y: auto;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--color-text-primary);
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-input-bg);
  color: var(--color-text-primary);
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: var(--color-primary);
  outline: none;
}

.error-message {
  color: var(--color-danger);
  font-size: 12px;
  margin-top: var(--spacing-xs);
}

.dialog-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.cancel-btn,
.confirm-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-weight: 500;
}

.cancel-btn {
  background-color: var(--color-button-secondary-bg);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.confirm-btn {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

.confirm-btn:disabled {
  background-color: var(--color-disabled);
  cursor: not-allowed;
}

/* 单选按钮组样式 */
.radio-group {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xs);
}

.radio-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
}

.radio-label input[type="radio"] {
  margin: 0;
}

/* 下拉选择框样式 */
.select-input {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-input-bg);
  color: var(--color-text-primary);
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%234a5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
}

.select-input:focus {
  border-color: var(--color-primary);
  outline: none;
}
</style>
