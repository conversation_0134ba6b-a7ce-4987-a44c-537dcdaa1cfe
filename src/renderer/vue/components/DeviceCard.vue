<template>
  <div
    class="device-card"
    :class="{
      offline: !device.isOnline,
      'batch-mode': batchMode,
      fullscreen: isFullscreen,
    }"
    :data-sn="device.sn"
    @click="selecteCardClick"
  >
    <!-- RTSP播放器 - 仅在投屏模式下显示 -->
    <div v-if="isScreenCasting" class="rtsp-player-container">
      <div class="full-screen">
        <RtspPlayer
          ref="rtspPlayerRef"
          :stream-url="rtspStreamUrl"
          :auto-play="true"
          :audio="false"
          :show-controls="false"
          @error="handleStreamError"
          @play="handleStreamPlay"
          @stop="handleStreamStop"
        />

        <!-- 批量选择复选框 - 左下角 -->
        <label v-if="batchMode" class="device-select-checkbox" @click.stop>
          <input
            type="checkbox"
            class="device-select"
            v-model="isSelected"
            @change.stop="handleSelectionChange"
          />
        </label>
      </div>
    </div>

    <div class="content">
      <!-- 设备信息 -->
      <div class="device-content">
        <!-- 设备标识信息 -->
        <div :class="isScreenCasting ? 'header-batch-mode' : 'device-header'">
          <div class="device-title" v-if="device.isOnline">
            <div class="online-status" :class="{ screen: isScreenCasting }">
              <span class="device-id" :class="{ screen: isScreenCasting }">{{
                device.id ? String(device.id).padStart(3, "0") : "未知"
              }}</span>
              <span
                v-if="!isScreenCasting"
                class="status-indicator online"
              ></span>
            </div>
            <div
              class="v-line"
              :class="isScreenCasting ? 'v-line-batch' : ''"
            ></div>
            <div
              class="status-group"
              :class="isScreenCasting ? 'status-min' : ''"
            >
              <img :src="useIcon" :class="isScreenCasting ? 'invert' : ''" />
              <img
                :src="controlIcon"
                :class="isScreenCasting ? 'invert' : ''"
              />
              <img
                :src="getPlayIcon(() => currentPlayStatus.value).value"
                :class="isScreenCasting ? 'invert' : ''"
              />
            </div>
            <div
              class="v-line"
              :class="isScreenCasting ? 'v-line-batch' : ''"
            ></div>
            <div
              class="battery"
              :class="batteryIconClass"
              :data-battery="device.battery || '未知'"
            ></div>
          </div>
          <div class="device-title" v-else>
            <div class="online-status">
              <span class="device-id">{{
                String(device.id).padStart(3, "0") || "未知"
              }}</span>
              <span class="status-indicator offline"></span>
            </div>
          </div>
        </div>

        <!-- 设备状态信息 - 非投屏模式下显示 -->
        <div v-if="!isScreenCasting" class="device-status">
          <div class="status-row">
            <span class="device-sn">SN: {{ device.sn }}</span>
          </div>
        </div>
      </div>

      <!-- 快捷菜单栏 -->
      <div class="quick-action-bar" v-if="!batchMode">
        <button
          v-if="device.isOnline && !isScreenCasting"
          class="action-btn locate-btn"
          :class="{ active: isLocating }"
          @click.stop="locateDevice"
          :title="isLocating ? '停止定位' : '定位设备'"
        >
          <img :src="isLocating && device.isOnline ? findOver : find" alt="" />
        </button>
        <button
          v-if="device.isOnline"
          class="action-btn reset-view-btn"
          @click.stop="resetDeviceView"
          :class="{ processing: isResettingView }"
          title="重置视野"
          :disabled="isResettingView"
        >
          <img
            :class="isFullscreen ? 'full-btn' : ''"
            src="@assets/svg/card/reset.svg"
            alt=""
          />
          <span v-if="isResettingView" class="loading-indicator"></span>
        </button>

        <button
          v-if="device.isOnline && isScreenCasting"
          class="action-btn reset-view-btn voice-btn"
          @click.stop="toggleVolumeControl"
          ref="volumeItemRef"
          :class="{ processing: isResettingView }"
          title="音量控制"
        >
          <img
            :class="isFullscreen ? 'full-btn' : ''"
            src="@assets/svg/card/voice.svg"
            alt=""
          />
        </button>

        <!-- 音量控制滑块子菜单 - 使用传送门将其放到body下 -->
        <teleport to="body" v-if="showVolumeSlider">
          <div
            class="volume-submenu"
            :style="{ top: volumePosition.top, left: volumePosition.left }"
            @mouseenter="volumeHovered = true"
            @mouseleave="volumeHovered = false"
          >
            <div class="volume-slider-container">
              <div class="volume-value">
                <span
                  v-if="isLoadingVolume"
                  class="loading-indicator-small"
                ></span>
                <span v-else>{{ volumeValue }}</span>
              </div>
              <input
                type="range"
                class="volume-slider"
                min="0"
                max="100"
                v-model="volumeValue"
                @input.stop="updateVolume"
                @mousedown.stop="startAdjustingVolume"
                @mouseup.stop="stopAdjustingVolume"
                :disabled="isLoadingVolume"
              />
              <i
                class="icon-volume-img icon-invert"
                style="margin-top: 8px"
              ></i>
            </div>
          </div>
        </teleport>

        <button
          v-if="device.isOnline && !isScreenCasting"
          class="action-btn cast-btn"
          :class="{ disabled: isProcessingCast || castCooldown }"
          @click.stop="openCastDialog"
          title="投屏"
          :disabled="isProcessingCast || castCooldown"
        >
          <img src="@assets/svg/card/proj.svg" alt="" />
          <span v-if="isProcessingCast" class="loading-indicator"></span>
        </button>
        <button
          v-if="isScreenCasting"
          class="action-btn"
          @click="maximizeStream"
          title="放大显示"
        >
          <img v-if="!isFullscreen" src="@assets/svg/card/scal.svg" alt="" />
          <img
            v-else
            :class="isFullscreen ? 'full-btn' : ''"
            src="@assets/svg/card/min.svg"
            alt=""
          />
        </button>

        <!-- 三点菜单移到最右侧 -->
        <button
          v-if="!isFullscreen"
          ref="menuRef"
          class="more-btn"
          @click="toggleMenu"
          title="更多选项"
        >
          <img v-if="!isScreenCasting" src="@assets/svg/card/more.svg" alt="" />
          <img v-else src="@assets/svg/card/more_white.svg" alt="" />
        </button>
      </div>

      <!-- 菜单内容 - 使用传送门将菜单放到body下，避免被其他元素覆盖 -->
      <teleport to="body">
        <div
          class="dropdown-menu"
          v-if="showMenu"
          :style="{ top: menuPosition.top, left: menuPosition.left }"
          v-click-outside="
            (event) => {
              // 检查点击是否来自更多按钮
              if (
                event.target.closest('.more-btn') ||
                event.target.closest('.voice-btn')
              ) {
                return;
              }
              showMenu = false;
            }
          "
          @scroll.window="showMenu = false"
        >
          <div
            v-if="device.isOnline"
            class="dropdown-item item-volume"
            @click.stop="toggleVolumeControl"
            ref="volumeItemRef"
          >
            音量控制
          </div>
          <!-- 音量控制滑块子菜单 - 使用传送门将其放到body下 -->
          <teleport to="body" v-if="showVolumeSlider">
            <div
              class="volume-submenu"
              :style="{ top: volumePosition.top, left: volumePosition.left }"
              @mouseenter="volumeHovered = true"
              @mouseleave="volumeHovered = false"
            >
              <div class="volume-slider-container">
                <div class="volume-value">
                  <span
                    v-if="isLoadingVolume"
                    class="loading-indicator-small"
                  ></span>
                  <span v-else>{{ volumeValue }}</span>
                </div>
                <input
                  type="range"
                  class="volume-slider"
                  min="0"
                  max="100"
                  v-model="volumeValue"
                  @input.stop="updateVolume"
                  @mousedown.stop="startAdjustingVolume"
                  @mouseup.stop="stopAdjustingVolume"
                  :disabled="isLoadingVolume"
                />
                <i
                  class="icon-volume-img icon-invert"
                  style="margin-top: 8px"
                ></i>
              </div>
            </div>
          </teleport>
          <div
            v-if="device.isOnline"
            class="dropdown-item item-volume"
            @click.stop="
              turnScreenOff();
              showMenu = false;
            "
          >
            关闭屏幕
          </div>
          <div
            v-if="device.isOnline"
            class="dropdown-item item-volume"
            @click.stop="
              turnScreenOn();
              showMenu = false;
            "
          >
            开启屏幕
          </div>
          <!-- <div
            v-if="device.isOnline"
            class="dropdown-item item-volume"
            @click.stop="
              handleQuickAccess(false);
              showMenu = false;
            "
          >
            隐藏快捷入口
          </div>
          <div
            v-if="device.isOnline"
            class="dropdown-item item-volume"
            @click.stop="
              handleQuickAccess(true);
              showMenu = false;
            "
          >
            显示快捷入口
          </div> -->
          <div
            v-if="device.isOnline"
            class="dropdown-item item-power"
            @click.stop="
              $emit('shutdown', device);
              showMenu = false;
            "
          >
            关机
          </div>
          <div
            v-if="device.isOnline"
            class="dropdown-item item-restart"
            @click.stop="
              $emit('restart', device);
              showMenu = false;
            "
          >
            重启
          </div>
          <div
            class="dropdown-item item-delete delete-item"
            @click.stop="
              $emit('delete', device);
              showMenu = false;
            "
          >
            删除
          </div>
          <div
            class="dropdown-item item-edit"
            @click.stop="
              openEditDialog();
              showMenu = false;
            "
          >
            编辑
          </div>
        </div>
      </teleport>

      <!-- 批量选择复选框 - 左下角 -->
      <label v-if="batchMode" class="device-select-checkbox" @click.stop>
        <input
          type="checkbox"
          class="device-select"
          v-model="isSelected"
          @change.stop="handleSelectionChange"
        />
      </label>

      <div
        v-if="isScreenCasting"
        class="close-stream-btn"
        @click="stopScreenCast"
      >
        <img src="@assets/svg/close_card.svg" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  watch,
  onMounted,
  onUnmounted,
  onActivated,
  onDeactivated,
  computed,
} from "vue";
import { PlayStatusLabels } from "../../../shared/types/device";
import RtspPlayer from "./RtspPlayer.vue";
import playIcon from "../../assets/svg/status/play.svg";
import playPauseIcon from "../../assets/svg/status/play_pause.svg";
import playErrorIcon from "../../assets/svg/status/play_error.svg";
import playWhiteIcon from "../../assets/svg/card/white_play.svg";
import { CommandType } from "../../../shared/constants/command-types";
import findOver from "../../assets/svg/card/find_over.svg";
import find from "../../assets/svg/card/find.svg";
import { ElMessage } from "element-plus";

// 注意：window.electronAPI 是通过 Electron 的 contextBridge 注入的
// TypeScript 无法识别它，但它在运行时是可用的

// 定义props
const props = defineProps({
  device: {
    type: Object,
    required: true,
  },
  batchMode: {
    type: Boolean,
    default: false,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  monitoredDevices: {
    type: Set,
    required: true,
  },
});
// 定义事件
const emit = defineEmits([
  "edit",
  "delete",
  "volume",
  "screen-off",
  "shutdown",
  "quick-access",
  "restart",
  "select",
  "deselect",
  "update",
  "locate",
  "cast",
  "reset-view",
]);

// 选中状态
const isSelected = ref(props.selected);

const selecteCardClick = () => {
  if (props.batchMode) {
    isSelected.value = !isSelected.value;
    handleSelectionChange();
  }
};

// 监听props变化
watch(
  () => props.selected,
  (newValue) => {
    isSelected.value = newValue;
  }
);

// 处理选择状态变化
const handleSelectionChange = () => {
  if (isSelected.value) {
    emit("select", props.device.sn);
  } else {
    emit("deselect", props.device.sn);
  }
};

// 处理卡片点击
const handleCardClick = () => {
  isSelected.value = !isSelected.value;
  handleSelectionChange();
};

// 菜单状态
const showMenu = ref(false);
const menuRef = ref(null);
const menuPosition = ref({ top: 0, left: 0 });

// 不再需要编辑对话框状态，已移至页面级别

// 音量控制状态
const showVolumeSlider = ref(false);
const volumeValue = ref(50); // 默认音量值
const volumeItemRef = ref(null);
const volumePosition = ref({ top: 0, left: 0 });
const volumeHovered = ref(false); // 鼠标是否悬停在音量控制上
const isAdjustingVolume = ref(false); // 是否正在调整音量
const justAdjustedVolume = ref(false); // 是否刚刚调整过音量（用于防止点击事件关闭音量控制）
const isLoadingVolume = ref(false); // 是否正在加载音量
const volumeResponseId = ref(null); // 音量响应事件监听器ID
const setVolumeResponseId = ref(null); // 设置音量响应事件监听器ID

// 不再需要快捷音量控制状态

// 切换菜单显示状态
const toggleMenu = (event) => {
  // 防止无法关闭之前已打开的更多弹框
  // event.stopPropagation();

  if (!showMenu.value) {
    // 计算菜单位置
    const buttonRect = event.currentTarget.getBoundingClientRect();
    const menuItemHeight = 40; // 每个菜单项的高度（包含padding和margin）
    const menuWidth = 140; // 菜单宽度

    // 根据设备在线状态计算菜单高度
    const menuItemCount = props.device.isOnline ? 7 : 2; // 在线设备7项，离线设备2项
    const menuHeight = menuItemHeight * menuItemCount;

    const windowHeight = window.innerHeight;
    const spaceBelow = windowHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top; // 按钮上方可用空间

    // 为向上弹出添加安全距离
    const safetyMargin = 20; // 安全边距

    // 如果下方空间不足，且上方空间足够，则向上弹出
    if (
      spaceBelow < menuHeight + safetyMargin &&
      spaceAbove >= menuHeight + safetyMargin
    ) {
      menuPosition.value = {
        top: buttonRect.bottom - menuHeight - safetyMargin + "px",
        left: buttonRect.right - menuWidth + "px",
      };
    } else {
      // 如果上方空间也不足，则强制向上弹出，但可能会被遮挡
      // 如果下方空间足够，则向下弹出
      menuPosition.value = {
        top: buttonRect.bottom + 5 + "px",
        left: buttonRect.right - menuWidth + "px",
      };
    }
  }

  showMenu.value = !showMenu.value;
};

// 点击外部关闭菜单和音量控制
const handleClickOutside = (event) => {
  // 如果刚刚调整过音量，忽略这次点击事件
  if (justAdjustedVolume.value) {
    justAdjustedVolume.value = false; // 重置标志
    return;
  }

  // 处理主菜单
  if (menuRef.value && !menuRef.value.contains(event.target)) {
    showMenu.value = false;
  }

  // 处理下拉菜单中的音量控制
  const volumeSubmenu = document.querySelector(".volume-submenu");
  const isClickOnVolumeItem =
    volumeItemRef.value && volumeItemRef.value.contains(event.target);
  const isClickOnVolumeSubmenu =
    volumeSubmenu && volumeSubmenu.contains(event.target);

  // 如果点击在音量控制之外，并且不是正在调整音量，则隐藏
  if (
    !isClickOnVolumeItem &&
    !isClickOnVolumeSubmenu &&
    !isAdjustingVolume.value
  ) {
    showVolumeSlider.value = false;
  }
};

// 切换音量控制显示状态
const toggleVolumeControl = async (event) => {
  event.stopPropagation();

  // 切换显示状态
  showVolumeSlider.value = !showVolumeSlider.value;

  // 如果显示，则更新位置并获取当前音量
  if (showVolumeSlider.value) {
    // 计算音量控制的位置
    const itemRect = volumeItemRef.value.getBoundingClientRect();
    volumePosition.value = {
      top: itemRect.top - 60 + "px", // 垂直居中
      left: itemRect.right + 10 + "px", // 在菜单项右侧显示
    };

    console.log(`设备 ${props.device.sn} 显示音量控制`);

    // 获取设备当前音量
    try {
      isLoadingVolume.value = true;

      // 设置音量响应事件监听器
      setupVolumeResponseListeners();

      // 发送获取音量命令
      await window.electronAPI.getDeviceVolume(props.device.sn);

      console.log(`已发送获取音量命令: ${props.device.sn}`);
    } catch (error) {
      console.error(`获取设备音量失败: ${props.device.sn}`, error);
      isLoadingVolume.value = false;
    }
  } else {
    console.log(`设备 ${props.device.sn} 隐藏音量控制`);

    // 清理音量响应事件监听器
    cleanupVolumeResponseListeners();
  }
};

// 开始调整音量
const startAdjustingVolume = () => {
  isAdjustingVolume.value = true;
  console.log(
    `设备 ${props.device.sn} 开始调整音量，当前值: ${volumeValue.value}%`
  );
};

// 停止调整音量
const stopAdjustingVolume = () => {
  // 设置为false，但不会立即隐藏音量控制
  isAdjustingVolume.value = false;

  // 设置刚刚调整过音量的标志，防止mouseup后的click事件关闭音量控制
  justAdjustedVolume.value = true;

  console.log(
    `设备 ${props.device.sn} 停止调整音量，最终值: ${volumeValue.value}%，音量控制保持显示`
  );
};

// 更新音量
const updateVolume = async () => {
  // 打印日志，显示当前调整的音量值
  console.log(`设备 ${props.device.sn} 音量调整中: ${volumeValue.value}%`);

  try {
    // 发送设置音量命令
    await window.electronAPI.setDeviceVolume(
      props.device.sn,
      volumeValue.value
    );
    console.log(
      `已发送设置音量命令: ${props.device.sn}, 音量: ${volumeValue.value}%`
    );
  } catch (error) {
    console.error(`设置设备音量失败: ${props.device.sn}`, error);
  }
};

// 设置音量响应事件监听器
const setupVolumeResponseListeners = () => {
  // 清理之前的监听器
  cleanupVolumeResponseListeners();

  // 设置获取音量响应监听器
  const volumeResponseHandler = (data) => {
    // 检查是否是当前设备的响应
    if (data.sn === props.device.sn) {
      console.log(`收到设备 ${props.device.sn} 的音量响应:`, data);

      // 更新音量值
      volumeValue.value = data.volume;

      // 重置加载状态
      isLoadingVolume.value = false;
    }
  };

  // 设置设置音量响应监听器
  const setVolumeResponseHandler = (data) => {
    // 检查是否是当前设备的响应
    if (data.sn === props.device.sn) {
      console.log(`收到设备 ${props.device.sn} 的设置音量响应:`, data);

      if (data.success) {
        console.log(`设备 ${props.device.sn} 设置音量成功`);
      } else {
        console.error(
          `设备 ${props.device.sn} 设置音量失败: 错误码 ${data.code}`
        );
      }
    }
  };

  // 注册事件监听器并保存ID
  volumeResponseId.value = window.electronAPI.onVolumeResponse(
    volumeResponseHandler
  );
  setVolumeResponseId.value = window.electronAPI.onSetVolumeResponse(
    setVolumeResponseHandler
  );

  console.log(
    `设备 ${props.device.sn} 注册音量响应事件监听器 ID: ${volumeResponseId.value}, ${setVolumeResponseId.value}`
  );
};

// 清理音量响应事件监听器
const cleanupVolumeResponseListeners = () => {
  // 移除获取音量响应事件监听器
  if (volumeResponseId.value) {
    console.log(
      `移除设备 ${props.device.sn} 的音量响应事件监听器 ID: ${volumeResponseId.value}`
    );
    window.electronAPI.removeVolumeResponseListener(volumeResponseId.value);
    volumeResponseId.value = null;
  }

  // 移除设置音量响应事件监听器
  if (setVolumeResponseId.value) {
    console.log(
      `移除设备 ${props.device.sn} 的设置音量响应事件监听器 ID: ${setVolumeResponseId.value}`
    );
    window.electronAPI.removeSetVolumeResponseListener(
      setVolumeResponseId.value
    );
    setVolumeResponseId.value = null;
  }
};

// 生命周期钩子
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  document.addEventListener("keydown", handleKeydown);

  // console.log(
  //   "🌈 最初播放状态值：",
  //   props.device.sn,
  //   "---",
  //   props.device.playStatus
  // );

  // 添加滚动事件监听器
  window.addEventListener(
    "scroll",
    () => {
      if (showMenu.value) {
        showMenu.value = false;
      }
    },
    true
  );
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("keydown", handleKeydown);

  // 移除滚动事件监听器
  window.removeEventListener(
    "scroll",
    () => {
      if (showMenu.value) {
        showMenu.value = false;
      }
    },
    true
  );
});

/**
 * 格式化播放状态
 * @param {string|number} status - 播放状态
 * @returns {string} 格式化后的播放状态
 */
const formatPlayStatus = (status) => {
  // 设备段只有两个值 0：播放中 1：暂停
  if (status === 0 || status === "playing") return 2; //'暂停'
  if (status === 1 || status === "paused") return 1; //'播放中'
  if (status === 3 || status === "error") return 3; //'错误'
  return 0; // 空闲状态
};

// 打开编辑对话框 - 现在只需触发edit事件，由父组件处理
const openEditDialog = () => {
  // 触发edit事件，传递设备信息
  emit("edit", props.device);
};

// 定位设备状态
const isLocating = ref(false);
const locateResponseId = ref(null);
const locateStopResponseId = ref(null);
let locateStartResponseHandler = null;
let locateStopResponseHandler = null;

// 定位设备 - 开关功能
const locateDevice = async () => {
  try {
    if (isLocating.value) {
      // 如果正在定位，则停止定位
      console.log(`停止定位设备: ${props.device.sn}`);

      // 设置定位响应事件监听器
      setupLocateListeners();

      // 发送停止定位命令
      await window.electronAPI.stopLocateDevice(props.device.sn);

      console.log(`已发送停止定位命令: ${props.device.sn}`);
    } else {
      // 如果没有定位，则开始定位
      console.log(`开始定位设备: ${props.device.sn}`);

      // 设置定位响应事件监听器
      setupLocateListeners();

      // 发送开始定位命令
      await window.electronAPI.startLocateDevice(props.device.sn);

      console.log(`已发送开始定位命令: ${props.device.sn}`);
    }
  } catch (error) {
    console.error(`定位设备操作失败: ${props.device.sn}`, error);
  }
};

// 设置定位响应事件监听器
const setupLocateListeners = () => {
  // 清理之前的监听器
  cleanupLocateListeners();

  // 设置开始定位响应监听器
  locateStartResponseHandler = (data) => {
    // 检查是否是当前设备的响应
    if (data.sn === props.device.sn) {
      console.log(`收到设备 ${props.device.sn} 的开始定位响应:`, data);

      if (data.success) {
        console.log(`设备 ${props.device.sn} 开始定位成功`);
        isLocating.value = true;
      } else {
        console.error(
          `设备 ${props.device.sn} 开始定位失败: 错误码 ${data.code}`
        );
      }
    }
  };

  // 设置停止定位响应监听器
  locateStopResponseHandler = (data) => {
    // 检查是否是当前设备的响应
    if (data.sn === props.device.sn) {
      console.log(`收到设备 ${props.device.sn} 的停止定位响应:`, data);

      if (data.success) {
        console.log(`设备 ${props.device.sn} 停止定位成功`);
        isLocating.value = false;
      } else {
        console.error(
          `设备 ${props.device.sn} 停止定位失败: 错误码 ${data.code}`
        );
      }
    }
  };

  // 注册事件监听器并保存ID
  locateResponseId.value = window.electronAPI.onLocateStartResponse(
    locateStartResponseHandler
  );
  locateStopResponseId.value = window.electronAPI.onLocateStopResponse(
    locateStopResponseHandler
  );

  console.log(
    `设备 ${props.device.sn} 注册定位响应事件监听器 ID: ${locateResponseId.value}, ${locateStopResponseId.value}`
  );

  // 设置定时器，5秒后自动清理监听器
  setTimeout(() => {
    cleanupLocateListeners();
  }, 5000);
};

// 清理定位响应事件监听器
const cleanupLocateListeners = () => {
  // 移除开始定位响应事件监听器
  if (locateResponseId.value) {
    console.log(
      `移除设备 ${props.device.sn} 的开始定位响应事件监听器 ID: ${locateResponseId.value}`
    );
    window.electronAPI.removeLocateStartResponseListener(
      locateResponseId.value
    );
    locateResponseId.value = null;
  }

  // 移除停止定位响应事件监听器
  if (locateStopResponseId.value) {
    console.log(
      `移除设备 ${props.device.sn} 的停止定位响应事件监听器 ID: ${locateStopResponseId.value}`
    );
    window.electronAPI.removeLocateStopResponseListener(
      locateStopResponseId.value
    );
    locateStopResponseId.value = null;
  }
};

// 点亮屏幕
const turnScreenOn = async () => {
  try {
    console.log(`点亮设备屏幕: ${props.device.sn}`);

    // 设置屏幕控制响应事件监听器
    setupScreenControlListeners();

    // 发送点亮屏幕命令
    await window.electronAPI.turnScreenOn(props.device.sn);

    console.log(`已发送点亮屏幕命令: ${props.device.sn}`);
  } catch (error) {
    console.error(`点亮设备屏幕失败: ${props.device.sn}`, error);
  }
};

// 关闭屏幕
const turnScreenOff = async () => {
  try {
    console.log(`关闭设备屏幕: ${props.device.sn}`);

    // 设置屏幕控制响应事件监听器
    setupScreenControlListeners();

    // 发送关闭屏幕命令
    await window.electronAPI.turnScreenOff(props.device.sn);

    console.log(`已发送关闭屏幕命令: ${props.device.sn}`);
  } catch (error) {
    console.error(`关闭设备屏幕失败: ${props.device.sn}`, error);
  }
};

// 设置快捷入口
const handleQuickAccess = async (isShow = true) => {
  try {
    console.log(
      `设备快捷入口: ${props.device.sn} - ${isShow ? "显示" : "隐藏"}`
    );

    // 设置屏幕控制响应事件监听器
    setupScreenControlListeners();

    // 发送关闭屏幕命令
    await window.electronAPI.quickAccess(props.device.sn, isShow);

    console.log(`已发送设置快捷入口命令: ${props.device.sn} - ${isShow}`);
  } catch (error) {
    console.error(`设置快捷入口失败: ${props.device.sn}`, error);
  }
};

// 设置屏幕控制响应事件监听器
const setupScreenControlListeners = () => {
  // 清理之前的监听器
  cleanupScreenControlListeners();

  // 设置点亮屏幕响应监听器
  screenOnResponseHandler = (data) => {
    // 检查是否是当前设备的响应
    if (data.sn === props.device.sn) {
      console.log(`收到设备 ${props.device.sn} 的点亮屏幕响应:`, data);

      if (data.success) {
        console.log(`设备 ${props.device.sn} 点亮屏幕成功`);
      } else {
        console.error(
          `设备 ${props.device.sn} 点亮屏幕失败: 错误码 ${data.code}`
        );
      }
    }
  };

  // 设置关闭屏幕响应监听器
  screenOffResponseHandler = (data) => {
    // 检查是否是当前设备的响应
    if (data.sn === props.device.sn) {
      console.log(`收到设备 ${props.device.sn} 的关闭屏幕响应:`, data);

      if (data.success) {
        props.device.onuse = false;
        console.log(`设备 ${props.device.sn} 关闭屏幕成功`);
      } else {
        console.error(
          `设备 ${props.device.sn} 关闭屏幕失败: 错误码 ${data.code}`
        );
      }
    }
  };

  // 注册事件监听器并保存ID
  screenOnResponseId.value = window.electronAPI.onScreenOnResponse(
    screenOnResponseHandler
  );
  screenOffResponseId.value = window.electronAPI.onScreenOffResponse(
    screenOffResponseHandler
  );

  console.log(
    `设备 ${props.device.sn} 注册屏幕控制响应事件监听器 ID: ${screenOnResponseId.value}, ${screenOffResponseId.value}`
  );

  // 设置定时器，5秒后自动清理监听器
  setTimeout(() => {
    cleanupScreenControlListeners();
  }, 5000);
};

// 清理屏幕控制响应事件监听器
const cleanupScreenControlListeners = () => {
  // 移除点亮屏幕响应事件监听器
  if (screenOnResponseId.value) {
    console.log(
      `移除设备 ${props.device.sn} 的点亮屏幕响应事件监听器 ID: ${screenOnResponseId.value}`
    );
    window.electronAPI.removeScreenOnResponseListener(screenOnResponseId.value);
    screenOnResponseId.value = null;
  }

  // 移除关闭屏幕响应事件监听器
  if (screenOffResponseId.value) {
    console.log(
      `移除设备 ${props.device.sn} 的关闭屏幕响应事件监听器 ID: ${screenOffResponseId.value}`
    );
    window.electronAPI.removeScreenOffResponseListener(
      screenOffResponseId.value
    );
    screenOffResponseId.value = null;
  }
};

// 重置视野状态
const isResettingView = ref(false);
const resetViewResponseId = ref(null);
let resetViewResponseHandler = null;

// 重置设备视野
const resetDeviceView = async () => {
  try {
    console.log(`重置设备视野: ${props.device.sn}`);

    // 设置状态为正在重置视野
    isResettingView.value = true;

    // 设置重置视野响应事件监听器
    setupResetViewListener();

    // 发送重置视野命令
    await window.electronAPI.resetDeviceView(props.device.sn);

    console.log(`已发送重置视野命令: ${props.device.sn}`);

    // 设置超时，如果长时间没有收到响应，重置状态
    setTimeout(() => {
      if (isResettingView.value) {
        console.warn(`设备 ${props.device.sn} 重置视野操作超时，重置状态`);
        isResettingView.value = false;
      }
    }, 5000); // 5秒超时
  } catch (error) {
    console.error(`重置设备视野失败: ${props.device.sn}`, error);
    isResettingView.value = false;
  }
};

// 设置重置视野响应事件监听器
const setupResetViewListener = () => {
  // 清理之前的监听器
  cleanupResetViewListener();

  // 设置重置视野响应监听器
  resetViewResponseHandler = (data) => {
    // 检查是否是当前设备的响应
    if (data.sn === props.device.sn) {
      console.log(`收到设备 ${props.device.sn} 的重置视野响应:`, data);

      if (data.success) {
        console.log(`设备 ${props.device.sn} 重置视野成功`);
      } else {
        console.error(
          `设备 ${props.device.sn} 重置视野失败: 错误码 ${data.code}`
        );
      }

      // 重置状态
      isResettingView.value = false;
    }
  };

  // 注册事件监听器并保存ID
  resetViewResponseId.value = window.electronAPI.onResetViewResponse(
    resetViewResponseHandler
  );

  console.log(
    `设备 ${props.device.sn} 注册重置视野响应事件监听器 ID: ${resetViewResponseId.value}`
  );

  // 设置定时器，5秒后自动清理监听器
  setTimeout(() => {
    cleanupResetViewListener();
  }, 5000);
};

// 清理重置视野响应事件监听器
const cleanupResetViewListener = () => {
  // 移除重置视野响应事件监听器
  if (resetViewResponseId.value) {
    console.log(
      `移除设备 ${props.device.sn} 的重置视野响应事件监听器 ID: ${resetViewResponseId.value}`
    );
    window.electronAPI.removeResetViewResponseListener(
      resetViewResponseId.value
    );
    resetViewResponseId.value = null;
  }
};

// 投屏相关状态
const isScreenCasting = ref(false);
const rtspStreamUrl = ref("");
const rtspPlayerRef = ref(null);
const streamInfo = ref(null);
const screenCastError = ref(null);
const isProcessingCast = ref(false); // 是否正在处理投屏操作（开始或停止）
const castCooldown = ref(false); // 投屏冷却期，防止频繁点击

// 全屏状态
const isFullscreen = ref(false);

// 处理ESC键退出全屏
const handleKeydown = (event) => {
  if (event.key === "Escape" && isFullscreen.value) {
    console.log(`ESC键退出全屏模式: ${props.device.sn}`);
    isFullscreen.value = false;

    // 触发maximize-stream事件，传递全屏状态
    emit("maximize-stream", {
      device: props.device,
      streamUrl: rtspStreamUrl.value,
      isFullscreen: false,
    });
  }
};

// 事件监听器引用 - 使用 ref 使其具有响应性
const screenCastResponseId = ref(null);
const screenCastStopResponseId = ref(null);
const screenOnResponseId = ref(null);
const screenOffResponseId = ref(null);
// 定位设备事件监听器引用已在上面定义

// 事件处理函数
let screenCastResponseHandler = null;
let screenCastStopResponseHandler = null;
let screenOnResponseHandler = null;
let screenOffResponseHandler = null;
// 定位设备事件处理函数已在上面定义

// 修改图标获取逻辑
const getStatusIcon = (isDoRef, icon) => {
  // 修正：直接使用传入的函数获取值
  return computed(() => {
    const isActive = isDoRef();
    // 当投屏模式下使用彩色图标
    if (isActive) {
      return new URL(`../../assets/svg/status/${icon}.svg`, import.meta.url)
        .href;
    }
    // 当投屏模式下使用白色图标
    if (isScreenCasting.value) {
      return new URL(`../../assets/svg/card/white_${icon}.svg`, import.meta.url)
        .href;
    }
    // 当投屏模式下使用默认图标
    return new URL(`../../assets/svg/status/${icon}_no.svg`, import.meta.url)
      .href;
  });
};
const useIcon = getStatusIcon(() => props.device.onuse, "use");
const controlIcon = getStatusIcon(() => props.device.controlled, "controlled");

// 可选：状态映射表
const playIconMap = {
  0: playIcon, // 播放图标
  1: playPauseIcon, // 暂停图标
  3: playErrorIcon, // 错误图标
};

// 封装图标响应式计算
const getPlayIcon = (playStatusGetter) => {
  return computed(() => {
    const status = formatPlayStatus(playStatusGetter());

    // console.log(
    //   "🚗 前播放状态值：",
    //   props.device.sn,
    //   "---",
    //   props.device.playStatus,
    //   "\n 数据如下",
    //   props.device
    // );

    if (isScreenCasting.value) {
      return playWhiteIcon;
    }

    return playIconMap[status];
  });
};

// 监听屏幕投射响应事件
onMounted(() => {
  // 确保在挂载前清理任何可能存在的监听器
  cleanupEventListeners();

  console.log(`设备 ${props.device.sn} 组件挂载，注册事件监听器`);

  // 添加屏幕投射响应事件监听
  screenCastResponseHandler = (data) => {
    // 检查是否是当前设备的响应
    if (data.sn === props.device.sn) {
      console.log(`收到设备 ${props.device.sn} 的投屏响应:`, data);

      // 防止重复处理
      if (isScreenCasting.value && streamInfo.value) {
        console.warn(
          `设备 ${props.device.sn} 已经在投屏中，忽略重复的投屏响应`
        );
        return;
      }

      if (data.status === 1) {
        // 成功获取到RTSP流信息
        streamInfo.value = data;

        // 构建RTSP URL - 不包含SN号
        const { address, port } = data;
        const rtspUrl = `rtsp://${address}:${port}`;

        try {
          // 启动RTSP流代理服务
          window.electronAPI
            .startRtspStream(props.device.sn, rtspUrl)
            .then((streamInfoResult) => {
              console.log("RTSP流代理服务启动成功:", streamInfoResult);

              // 使用WebSocket URL
              rtspStreamUrl.value = streamInfoResult.wsUrl;
              console.log("WebSocket URL设置为:", rtspStreamUrl.value);

              // 显示播放器
              isScreenCasting.value = true;
              screenCastError.value = null;

              console.log(`开始播放WebSocket流: ${rtspStreamUrl.value}`);

              // 重置处理状态
              isProcessingCast.value = false;
            })
            .catch((error) => {
              console.error("启动RTSP流代理服务失败:", error);
              screenCastError.value = "启动流媒体服务失败";
              isScreenCasting.value = false;

              // 重置处理状态
              isProcessingCast.value = false;

              // 设置冷却期，防止频繁点击
              castCooldown.value = true;
              setTimeout(() => {
                castCooldown.value = false;
              }, 2000); // 2秒冷却期
            });
        } catch (error) {
          console.error("调用RTSP流代理服务时出错:", error);
          screenCastError.value = "启动流媒体服务失败";
          isScreenCasting.value = false;

          // 重置处理状态
          isProcessingCast.value = false;

          // 设置冷却期，防止频繁点击
          castCooldown.value = true;
          setTimeout(() => {
            castCooldown.value = false;
          }, 2000); // 2秒冷却期
        }

        // 触发cast事件
        emit("cast", props.device, rtspStreamUrl.value);
      } else {
        // 投屏失败
        screenCastError.value = "设备投屏失败";
        isScreenCasting.value = false;
        console.error(`设备 ${props.device.sn} 投屏失败:`, data);

        // 重置处理状态
        isProcessingCast.value = false;

        // 设置冷却期，防止频繁点击
        castCooldown.value = true;
        setTimeout(() => {
          castCooldown.value = false;
        }, 2000); // 2秒冷却期
      }
    }
  };
  // 注册事件监听器并保存ID
  screenCastResponseId.value = window.electronAPI.onScreenCastResponse(
    screenCastResponseHandler
  );
  console.log(
    `设备 ${props.device.sn} 注册屏幕投射响应事件监听器 ID: ${screenCastResponseId.value}`
  );

  // 添加停止投屏响应事件监听
  screenCastStopResponseHandler = (data) => {
    // 检查是否是当前设备的响应
    if (data.sn === props.device.sn) {
      console.log(`收到设备 ${props.device.sn} 的停止投屏响应:`, data);

      // 防止重复处理
      if (!isScreenCasting.value && !streamInfo.value) {
        console.warn(
          `设备 ${props.device.sn} 已经停止投屏，忽略重复的停止投屏响应`
        );
        return;
      }

      if (data.status === 0) {
        // 停止投屏成功
        console.log(`设备 ${props.device.sn} 停止投屏成功`);

        // 重置状态
        isScreenCasting.value = false;
        rtspStreamUrl.value = "";
        streamInfo.value = null;
      }
    }
  };
  // 注册事件监听器并保存ID
  screenCastStopResponseId.value = window.electronAPI.onScreenCastStopResponse(
    screenCastStopResponseHandler
  );
  console.log(
    `设备 ${props.device.sn} 注册停止投屏响应事件监听器 ID: ${screenCastStopResponseId.value}`
  );
});

// 清理事件监听器的函数
const cleanupEventListeners = () => {
  console.log(`清理设备 ${props.device.sn} 的事件监听器`);

  // 移除屏幕投射响应事件监听器
  if (screenCastResponseId.value) {
    console.log(
      `移除设备 ${props.device.sn} 的屏幕投射响应事件监听器 ID: ${screenCastResponseId.value}`
    );
    window.electronAPI.removeScreenCastResponseListener(
      screenCastResponseId.value
    );
    screenCastResponseId.value = null;
  }

  // 移除停止投屏响应事件监听器
  if (screenCastStopResponseId.value) {
    console.log(
      `移除设备 ${props.device.sn} 的停止投屏响应事件监听器 ID: ${screenCastStopResponseId.value}`
    );
    window.electronAPI.removeScreenCastStopResponseListener(
      screenCastStopResponseId.value
    );
    screenCastStopResponseId.value = null;
  }

  // 清理音量响应事件监听器
  cleanupVolumeResponseListeners();

  // 清理屏幕控制响应事件监听器
  cleanupScreenControlListeners();

  // 清理定位响应事件监听器
  cleanupLocateListeners();

  // 清理重置视野响应事件监听器
  cleanupResetViewListener();
};

// 组件卸载时移除事件监听器
onUnmounted(() => {
  console.log(`设备 ${props.device.sn} 组件卸载，清理资源`);

  // 清理事件监听器
  cleanupEventListeners();

  // 如果正在投屏，停止投屏
  if (isScreenCasting.value) {
    stopScreenCast();
  }
});

// 组件被 keep-alive 激活时
onActivated(() => {
  console.log(`设备 ${props.device.sn} 组件被激活，重新注册事件监听器`);

  // 重新注册事件监听器
  if (!screenCastResponseId.value && !screenCastStopResponseId.value) {
    // 添加屏幕投射响应事件监听
    screenCastResponseId.value = window.electronAPI.onScreenCastResponse(
      screenCastResponseHandler
    );
    console.log(
      `设备 ${props.device.sn} 重新注册屏幕投射响应事件监听器 ID: ${screenCastResponseId.value}`
    );

    // 添加停止投屏响应事件监听
    screenCastStopResponseId.value =
      window.electronAPI.onScreenCastStopResponse(
        screenCastStopResponseHandler
      );
    console.log(
      `设备 ${props.device.sn} 重新注册停止投屏响应事件监听器 ID: ${screenCastStopResponseId.value}`
    );
  }
});

// 组件被 keep-alive 停用时
onDeactivated(() => {
  console.log(`设备 ${props.device.sn} 组件被停用，清理事件监听器`);

  // 清理事件监听器
  cleanupEventListeners();
});

// 打开投屏
const openCastDialog = async () => {
  try {
    // 检查是否正在处理投屏操作或在冷却期
    if (isProcessingCast.value) {
      console.log(`设备 ${props.device.sn} 正在处理投屏操作，忽略点击`);
      return;
    }

    if (castCooldown.value) {
      console.log(`设备 ${props.device.sn} 在投屏冷却期，忽略点击`);
      return;
    }

    console.log(`开始投屏: ${props.device.sn}`);

    // 设置正在处理投屏操作状态
    isProcessingCast.value = true;

    // 检查设备是否在线
    if (!props.device.isOnline) {
      console.error(`设备 ${props.device.sn} 不在线，无法投屏`);
      isProcessingCast.value = false;
      return;
    }

    // 如果已经在投屏，则停止
    if (isScreenCasting.value) {
      await stopScreenCast();
      return;
    }

    // 检查当前监控设备数量
    const availableSlots = 6 - props.monitoredDevices.size;

    if (availableSlots <= 0) {
      ElMessage.warning(
        `当前已有 ${props.monitoredDevices.size} 台设备在监控中，最多只能监控6台设备`
      );
      isProcessingCast.value = false;
      return;
    }

    // 创建监控命令
    const monitorCommand = {
      type: CommandType.MONITOR, // 监控命令类型
      data: { id: Date.now() },
    };

    // 发送命令前先添加到监控设备集合
    props.monitoredDevices.add(props.device.sn);

    // 发送命令
    await window.electronAPI.sendCommand(props.device.sn, monitorCommand);

    console.log(`投屏命令已发送: ${props.device.sn}`);

    // 设置超时，如果长时间没有收到响应，重置状态
    setTimeout(() => {
      if (isProcessingCast.value && !isScreenCasting.value) {
        console.warn(`设备 ${props.device.sn} 投屏操作超时，重置状态`);
        isProcessingCast.value = false;
        // 从监控设备集合中移除
        props.monitoredDevices.delete(props.device.sn);
      }
    }, 10000); // 10秒超时
  } catch (error) {
    console.error(`投屏失败: ${props.device.sn}`, error);
    screenCastError.value = error.message || "投屏失败";
    isProcessingCast.value = false;
    // 从监控设备集合中移除
    props.monitoredDevices.delete(props.device.sn);
  }
};

// 放大显示流媒体
const maximizeStream = () => {
  console.log(`放大显示流媒体: ${props.device.sn}`);

  // 切换全屏状态
  isFullscreen.value = !isFullscreen.value;

  console.log(
    `设备 ${props.device.sn} 全屏状态: ${isFullscreen.value ? "开启" : "关闭"}`
  );

  // // 触发maximize-stream事件，传递全屏状态
  // emit("maximize-stream", {
  //   device: props.device,
  //   streamUrl: rtspStreamUrl.value,
  //   isFullscreen: isFullscreen.value,
  // });
};

// 停止投屏
const stopScreenCast = async () => {
  console.log(`停止投屏: ${props.device.sn}`);

  try {
    // 设置正在处理投屏操作状态
    isProcessingCast.value = true;

    // 防止重复停止
    if (!isScreenCasting.value && !streamInfo.value) {
      console.log(`设备 ${props.device.sn} 没有正在进行的投屏，跳过停止操作`);
      isProcessingCast.value = false;
      return;
    }

    // 立即重置状态，防止重复操作
    const wasScreenCasting = isScreenCasting.value;
    const savedStreamInfo = streamInfo.value;

    // 重置状态
    isScreenCasting.value = false;
    rtspStreamUrl.value = "";
    streamInfo.value = null;
    isFullscreen.value = false; // 重置全屏状态

    // 从监控设备集合中移除
    props.monitoredDevices.delete(props.device.sn);

    // 1. 发送停止监控命令
    if (wasScreenCasting) {
      try {
        const stopMonitorCommand = {
          type: CommandType.MONITOR_STOP, // 停止监控命令类型
          data: { id: Date.now() },
        };
        await window.electronAPI.sendCommand(
          props.device.sn,
          stopMonitorCommand
        );
        console.log(`停止监控命令已发送: ${props.device.sn}`);

        // 等待设备响应
        await new Promise((resolve) => setTimeout(resolve, 500));
      } catch (commandError) {
        console.error(`发送停止监控命令失败: ${props.device.sn}`, commandError);
      }
    }

    // 2. 停止播放器
    if (rtspPlayerRef.value) {
      console.log(`停止播放器: ${props.device.sn}`);
      try {
        rtspPlayerRef.value.stop();
      } catch (playerError) {
        console.error(`停止播放器失败: ${props.device.sn}`, playerError);
      }

      // 等待一小段时间，确保播放器完全停止
      await new Promise((resolve) => setTimeout(resolve, 300));
    }

    // 3. 停止RTSP流代理服务
    if (savedStreamInfo) {
      try {
        console.log(`开始停止RTSP流代理服务: ${props.device.sn}`);
        const stopResult = await window.electronAPI.stopRtspStream(
          props.device.sn
        );
        console.log(`RTSP流代理服务已停止: ${props.device.sn}`, stopResult);

        // 等待更长的时间，确保FFmpeg进程有足够的时间终止
        console.log(`等待FFmpeg进程终止: ${props.device.sn}`);
        await new Promise((resolve) => setTimeout(resolve, 2000));
        console.log(`等待完成: ${props.device.sn}`);
      } catch (rtspError) {
        console.error(`停止RTSP流代理服务失败: ${props.device.sn}`, rtspError);
      }
    }
    console.log(`投屏停止完成: ${props.device.sn}`);

    // 设置冷却期，防止频繁点击
    castCooldown.value = true;
    setTimeout(() => {
      castCooldown.value = false;
      console.log(`设备 ${props.device.sn} 投屏冷却期结束，可以再次点击`);
    }, 3000); // 3秒冷却期
  } catch (error) {
    console.error(`停止投屏失败: ${props.device.sn}`, error);
  } finally {
    // 无论成功还是失败，都重置处理状态
    isProcessingCast.value = false;
  }
};

// 处理流播放错误
const handleStreamError = (error) => {
  console.error(`播放流错误: ${props.device.sn}`, error);
  screenCastError.value = error.message || "播放流失败";
};

// 处理流开始播放
const handleStreamPlay = () => {
  console.log(`流开始播放: ${props.device.sn}`);
  screenCastError.value = null;
};

// 处理流停止播放
const handleStreamStop = () => {
  console.log(`流停止播放: ${props.device.sn}`);
  isScreenCasting.value = false;
};

// 重置设备视野功能已在上面实现

const batteryIconClass = computed(() => {
  const val = Number(props.device.battery);
  if (!isNaN(val)) {
    return val > 15 ? "battery-full" : "battery-low";
  }
  return "";
});

// 监听设备在线状态，当设备离线时自动停止投屏
watch(
  () => props.device.isOnline,
  (newValue) => {
    if (!newValue && isScreenCasting.value) {
      console.log(`设备 ${props.device.sn} 离线，自动停止投屏`);
      stopScreenCast();
    }
    if (!newValue && isFullscreen.value) {
      console.log(`设备 ${props.device.sn} 离线，退出全屏模式`);
      isFullscreen.value = false;
    }
  }
);
// 创建一个响应式引用，专门用于跟踪播放状态
const currentPlayStatus = computed(() => props.device.playStatus);
</script>

<style scoped>
.device-card {
  background: var(
    --color-card-background-online
  ); /* 默认使用在线设备的淡蓝色背景 */
  border-radius: 8px; /* 更大的圆角 */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12); /* 加重阴影 */
  transition: all 0.25s ease;
  max-width: 100%; /* 确保不会超出容器 */
  min-height: 120px; /* 进一步减小最小高度 */
  border: 2px solid transparent; /* 更粗的边框，但默认透明 */
  position: relative;
  box-sizing: border-box; /* 确保padding不会增加元素的总宽度 */
  aspect-ratio: 1.68;
  width: 100%;
  height: auto; /* 高度自适应，受宽度和比例影响 */
}

/* 全屏模式样式 */
.device-card.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
  aspect-ratio: unset;
  max-width: unset;
  min-height: unset;
  box-shadow: none;
  border: none;
  background: #000; /* 全屏模式下使用黑色背景 */
}

/* 全屏模式下的内容区域 */
.device-card.fullscreen .content {
  padding: 0;
  width: 100%;
  height: 100%;
}

/* 全屏模式下的RTSP播放器容器 */
.device-card.fullscreen .rtsp-player-container {
  width: 100%;
  height: 100%;
}
.device-card.batch-mode {
  cursor: pointer;
}

/* 只有在线设备才有悬停效果 */
.device-card:not(.offline):hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15); /* 加重悬停时的阴影 */
  border-color: var(--color-primary);
  transform: translateY(-2px);
}

.device-card.offline {
  background-color: var(--color-card-background-offline);
  border-color: transparent;
  opacity: 0.95;
  cursor: default; /* 确保鼠标指针不变为手型 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 暗色主题下的离线设备卡片 */
[data-theme="dark"] .device-card.offline {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.content {
  padding: 24px; /* 减小内边距 */
}

.device-content {
  display: flex;
  flex-direction: column;
  gap: 8px; /* 减小间距 */
  position: relative;
  z-index: 2;
}

/* 设备头部样式 */
.device-header {
  display: flex;
  flex-direction: column;
  justify-content: baseline;
  color: var(--color-text-primary);
}
.header-batch-mode {
  color: white;
  width: 80%;
  padding: 7px;
  border-radius: 6px;
  background: rgba(7, 7, 7, 0.2);
}

/* 全屏模式下的header-batch-mode样式 */
.device-card.fullscreen .header-batch-mode {
  position: fixed;
  top: 30px;
  left: 30px;
  width: 20vw;
  min-width: 300px;
  z-index: 10000;
  gap: 20px;
}

.device-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.state-icon {
  width: 14px;
  height: 14px;
}

/* 修改后的样式 */
.v-line {
  height: 19px;
  width: 1px;
  background-color: #989898;
  margin: 0 4px; /* 左右留出间距 */
}
.v-line-batch {
  background-color: white;
}

.status-group {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 10px;
  margin: 0 8px;
  height: 19px;
  gap: 20px;
  padding: 0 5%;
}
.status-group img {
  width: 16px;
  height: 16px;
}
.status-group .invert {
  width: 14px;
  height: 14px;
}
.status-group.status-min {
  padding: 0 2px;
}
.online-status {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  flex: 2;
  overflow: hidden;
}
.online-status.screen {
  flex: 1;
}

.battery {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 18px;
  font-size: 13px;
  font-weight: 500;
  color: black;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  border-radius: 4px;
  margin-left: 8px;
  padding: 0;
  overflow: hidden;
  flex: 1;
}

.battery-full {
  background-image: url("../../assets/svg/card/battery_full.svg");
}

.battery-low {
  background-image: url("../../assets/svg/card/battery_low.svg");
}

.battery::after {
  content: attr(data-battery);
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 13px;
  font-weight: 600;
  pointer-events: none;
}

/* 设备ID样式 */
.device-id {
  padding: 3px 5px;
  display: inline-flex;
  align-items: center;
  height: 22px;
  flex-shrink: 0; /* 防止ID被压缩 */

  font-size: 28px;
  font-style: normal;
  font-weight: 540;
  line-height: normal;
  letter-spacing: 2px;
}
.device-id.screen {
  font-size: 20px;
}

/* 设备SN号样式 */
.device-sn {
  margin-top: 18px;
  color: var(--color-text-primary);
  font-size: 16px;
  font-style: normal;
  font-weight: 250;
  line-height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 离线设备的文本颜色 */
.device-card.offline .device-sn,
.device-card.offline .status-item {
  color: var(--color-text-primary);
}

/* 暗色主题下离线设备的文本颜色 */
[data-theme="dark"] .device-card.offline .device-sn,
[data-theme="dark"] .device-card.offline .status-item {
  color: var(--color-text-secondary);
  font-weight: 500;
}

/* 状态指示器基础样式 */
.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  transition: all 0.3s ease;
  margin-left: 2px; /* 与SN号保持一点距离 */
}

/* 在线状态 - 绿色 */
.status-indicator.online {
  background-color: var(--color-success);
  box-shadow: 0 0 6px var(--color-success);
  animation: pulse 2s infinite;
}

/* 离线状态 - 灰色 */
.status-indicator.offline {
  background-color: #fe2938;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

/* 设备状态布局 */
.device-status {
  display: flex;
  flex-direction: column;
  gap: 6px; /* 减小间距 */
  font-size: 13px;
  color: var(--color-text-primary);
  border-radius: 6px;
  padding: 8px; /* 减小内边距 */
}

.status-row {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

/* 状态项样式 */
.status-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  flex: 1;
}

/* 状态标签样式 */
.status-label {
  color: var(--color-text-secondary);
  font-weight: 500;
  font-size: 12px;
}

/* 状态值样式 */
.status-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.dropdown-menu {
  position: fixed; /* 改为fixed定位，避免被其他卡片覆盖 */
  background: var(--color-card-background-online);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  overflow: hidden;
  width: 140px;
  z-index: 1000; /* 提高z-index，确保在最上层 */
  animation: fadeIn 0.15s ease-out; /* 添加淡入动画，减少闪烁 */
  border: 1px solid var(--color-border);
}

/* 深色主题下的下拉菜单 */
[data-theme="dark"] .dropdown-menu {
  background-color: #2d3748; /* 深色但不是纯黑色的背景 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  padding: 10px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--color-text-primary);
  border-radius: 4px;
  margin: 2px 4px;
}

.dropdown-item:hover {
  background-color: var(--color-background-light);
  transform: translateX(2px);
}

.dropdown-item i {
  color: var(--color-text-secondary);
}

/* 深色主题下的下拉菜单项 */
[data-theme="dark"] .dropdown-item {
  color: rgba(255, 255, 255, 0.9); /* 高对比度的白色文本 */
}

[data-theme="dark"] .dropdown-item i {
  color: rgba(255, 255, 255, 0.7); /* 图标颜色稍微淡一些 */
}

[data-theme="dark"] .dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.1); /* 半透明白色背景 */
}

/* 编辑设备 */
.item-edit:hover i.icon-edit {
  color: var(--color-primary);
}
.item-edit:hover {
  background-color: rgba(33, 150, 243, 0.1); /* 淡蓝色背景 */
}

/* 音量控制 */
.item-volume i.icon-volume {
  color: #2196f3; /* 蓝色 - 保留用于文本图标 */
}
.item-volume {
  position: relative;
}
.item-volume:hover {
  background-color: rgba(33, 150, 243, 0.1); /* 淡蓝色背景 */
}

/* 音量控制子菜单 */
.volume-submenu {
  position: fixed; /* 改为fixed定位，避免被其他元素覆盖 */
  background-color: var(--color-white);
  border-radius: 6px;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
  padding: 12px;
  width: 60px; /* 减小宽度，适合竖直布局 */
  height: 180px; /* 设置高度，适合竖直布局 */
  z-index: 1001;
  animation: fadeIn 0.15s ease-out;
}

/* 深色主题下的音量控制子菜单 */
[data-theme="dark"] .volume-submenu {
  background-color: #2d3748; /* 深色但不是纯黑色的背景 */
}

.volume-slider-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  gap: 8px;
}

.volume-slider {
  width: 6px; /* 减小宽度 */
  height: 120px; /* 增加高度 */
  -webkit-appearance: none;
  appearance: none;
  background: var(--color-border);
  outline: none;
  border-radius: 3px;
  cursor: pointer;
  writing-mode: bt-lr; /* 竖直方向 */
  -webkit-appearance: slider-vertical; /* 竖直滑块 */
  /* 不需要翻转，默认就是上方100%，下方0% */
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--color-primary);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s;
}

.volume-value {
  text-align: center;
  font-weight: 600;
  color: var(--color-primary);
  font-size: 14px;
  margin-top: 8px;
}

/* 关闭屏幕 */
.item-screen:hover i.icon-screen {
  color: #9c27b0; /* 紫色 - 保留用于文本图标 */
}
.item-screen:hover {
  background-color: rgba(156, 39, 176, 0.1); /* 淡紫色背景 */
}

/* 关闭设备 */
.item-power:hover i.icon-power {
  color: #ff9800; /* 橙色 */
}
.item-power:hover {
  background-color: rgba(255, 152, 0, 0.1); /* 淡橙色背景 */
}

/* 重启设备 */
.item-restart:hover i.icon-restart {
  color: #4caf50; /* 绿色 */
}
.item-restart:hover {
  background-color: rgba(76, 175, 80, 0.1); /* 淡绿色背景 */
}

/* 删除设备 */
.item-delete:hover i.icon-delete {
  color: var(--color-danger);
}
.item-delete:hover {
  background-color: rgba(244, 67, 54, 0.1); /* 淡红色背景 */
}

.delete-item {
  border-top: 1px solid var(--color-border-light);
  margin-top: 4px;
  padding-top: 8px;
}

/* 深色主题下的删除项分隔线 */
[data-theme="dark"] .delete-item {
  border-top-color: rgba(255, 255, 255, 0.1); /* 半透明白色分隔线 */
}

/* 图标样式 */
[class^="icon-"] {
  font-style: normal;
  font-family: monospace;
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  font-size: 14px;
}

.icon-edit::before {
  content: "✎";
}
.icon-power::before {
  content: "⏻";
  font-size: 14px;
}
.icon-restart::before {
  content: "↻";
  font-size: 16px;
}
.icon-delete::before {
  content: "×";
  font-size: 18px;
}
.icon-menu::before {
  content: "⋮";
  font-size: 18px;
}

/* 图片图标 */
.icon-locate-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("../../assets/images/yvr_location.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
}

.icon-cast-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("../../assets/images/yvr_monitor.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
}

.icon-reset-view-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("../../assets/images/yvr_recenter.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
}

.icon-restart-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("../../assets/images/yvr_action_restart.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
  /* 移除任何可能的默认滤镜 */
  filter: none;
}

.icon-edit-img {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-image: url("../../assets/images/yvr_edit.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
}

/* 快捷菜单栏中的图标 - 亮色主题下应用滤镜，使图标更暗 */
.quick-action-bar .icon-locate-img,
.quick-action-bar .icon-cast-img,
.quick-action-bar .icon-reset-view-img,
.quick-action-bar .icon-restart-img {
  filter: brightness(0) invert(0.2) !important; /* 先转为黑色，再轻微反色，使其变为深灰色，使用!important确保覆盖其他样式 */
}

/* 快捷菜单栏样式 */
.quick-action-bar {
  position: absolute;
  bottom: 24px;
  right: 24px;
  z-index: 2;

  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 12px;
  padding-top: 8px;
}

/* 全屏模式下的快捷菜单栏样式 */
.device-card.fullscreen .quick-action-bar {
  position: fixed;
  bottom: 100px;
  left: 50%;
  right: unset;
  transform: translateX(-50%);
  z-index: 10000;
  display: flex;
  gap: 32px;
}

/* 深色主题下的快捷菜单栏分隔线 */
[data-theme="dark"] .quick-action-bar {
  border-top-color: rgba(255, 255, 255, 0.1);
}

.more-btn {
  width: 32px;
  height: 32px;
  border: none;
  cursor: pointer;
  background-color: transparent;
}

/* 快捷操作按钮 */
.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background-color: var(--color-white-trans);
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: var(--shadow-sm);
  position: relative;
  margin-left: 18px;
}
.full-btn {
  width: 56px;
  height: 56px;
}

.action-btn i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 18px;
  height: 18px;
}

.action-btn:hover:not(.disabled) {
  background-color: var(--color-background-dark);
  transition: background-color 0.2s; /* 只保留背景色动画 */
}

/* 禁用状态 */
.action-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--color-background-dark);
  box-shadow: none;
}

/* 加载指示器 */
.loading-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 12px;
  height: 12px;
  border: 2px solid var(--color-primary);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

/* 小型加载指示器 - 用于音量控制等 */
.loading-indicator-small {
  display: inline-block;
  width: 10px;
  height: 10px;
  border: 1.5px solid var(--color-primary);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  vertical-align: middle;
  margin-right: 4px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 深色主题下的操作按钮 - 颜色互换 */
[data-theme="dark"] .action-btn {
  background-color: var(--color-background-dark);
  color: var(--color-text-secondary);
}

[data-theme="dark"] .action-btn:hover:not(.disabled) {
  background-color: var(--color-white-trans);
  color: var(--color-text-primary);
}

/* 深色主题下的定位按钮激活状态 */
[data-theme="dark"] .locate-btn.active {
  background-color: rgba(33, 150, 243, 0.3);
  color: #90caf9; /* 浅蓝色，更适合深色主题 */
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.5);
}

[data-theme="dark"] .locate-btn.active:hover {
  background-color: rgba(33, 150, 243, 0.4);
  color: #bbdefb; /* 更亮的蓝色 */
}

/* 深色主题下的图标反色 */
[data-theme="dark"] .icon-locate-img,
[data-theme="dark"] .icon-cast-img,
[data-theme="dark"] .icon-reset-view-img,
[data-theme="dark"] .icon-restart-img {
  filter: brightness(0) invert(1) !important; /* 先转为黑色，再反色为白色，使用!important确保覆盖其他样式 */
}

/* 下拉菜单中的图标 - 亮色主题下应用滤镜，使图标更暗 */
.dropdown-menu .icon-edit-img,
.dropdown-menu .icon-restart-img,
.dropdown-menu .icon-delete-img {
  filter: brightness(0) invert(0.2); /* 先转为黑色，再轻微反色，使其变为深灰色 */
}

/* 深色主题下的下拉菜单图标 */
[data-theme="dark"] .dropdown-menu .icon-edit-img,
[data-theme="dark"] .dropdown-menu .icon-restart-img,
[data-theme="dark"] .dropdown-menu .icon-delete-img {
  filter: brightness(0) invert(1); /* 先转为黑色，再反色为白色 */
}

/* 定位按钮特殊样式 */
.locate-btn:hover {
  background-color: rgba(33, 150, 243, 0.2);
  color: var(--color-primary);
}

/* 定位按钮激活状态 */
.locate-btn.active {
  background-color: rgba(33, 150, 243, 0.4);
  color: var(--color-primary);
  box-shadow: 0 0 8px rgba(33, 150, 243, 0.6);
  animation: pulse-blue 2s infinite;
}

/* 定位按钮激活状态下的悬停效果 */
.locate-btn.active:hover {
  background-color: rgba(33, 150, 243, 0.5);
}

/* 重置视野按钮特殊样式 */
.reset-view-btn:hover {
  background-color: rgba(76, 175, 80, 0.2);
  color: var (--color-success);
}

/* 重置视野按钮处理中状态 */
.reset-view-btn.processing {
  background-color: rgba(76, 175, 80, 0.4);
  color: var(--color-success);
  pointer-events: none; /* 禁止点击 */
}

/* 定位按钮脉动动画 */
@keyframes pulse-blue {
  0% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(33, 150, 243, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
  }
}

/* 投屏按钮特殊样式 */
.cast-btn:hover:not(.disabled) {
  background-color: rgba(76, 175, 80, 0.2);
  color: var (--color-success);
}

/* 重置视野按钮特殊样式 */
.reset-view-btn:hover {
  background-color: rgba(156, 39, 176, 0.2);
  color: #9c27b0;
}

/* 重启按钮特殊样式 */
.restart-btn:hover {
  background-color: rgba(255, 152, 0, 0.2);
}

/* RTSP播放器容器 */
.rtsp-player-container {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  border-radius: var(--border-radius);
  overflow: hidden;
  background-color: #000; /* 保留黑色背景，因为视频播放器通常需要黑色背景 */
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.full-screen {
  position: relative;
}

/* 放大按钮 */
.maximize-stream-btn {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.maximize-stream-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
  border-color: rgba(255, 255, 255, 0.5);
  opacity: 1;
  transform: scale(1.1);
}

/* 关闭流按钮 */
.close-stream-btn {
  position: absolute;
  top: 29px;
  right: 29px;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  opacity: 0.7;
  transition: all var(--transition-fast);
}

.close-stream-btn:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.8);
}

.icon-close::before {
  content: "×";
  font-size: 18px;
}

/* 放大图标 */
.icon-maximize::before {
  content: "⤢";
  font-size: 16px;
}

/* 使用 appearance 属性移除默认样式 */
.device-select-checkbox input.device-select[type="checkbox"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
  width: 28px;
  height: 28px;
  position: absolute;
  bottom: 24px;
  left: 24px;
  border: 2px solid var(--color-primary);
  background-color: var(--color-card-background-online);
  border-radius: 4px;
  outline: none;
}

/* 添加选中状态样式，移除position:relative以保持位置不变 */
.device-select-checkbox input.device-select[type="checkbox"]:checked {
  background-color: var(--color-primary);
}

/* 添加选中状态的勾选标记 */
.device-select-checkbox input.device-select[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 9px;
  top: 3px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
</style>
