<template>
  <div class="custom-select-container" :class="{ 'is-disabled': disabled }">
    <div
      ref="triggerRef"
      class="custom-select-trigger"
      :class="{ 'bg-gray-200 cursor-not-allowed': disabled }"
      @click="toggleDropdown"
      :aria-expanded="isOpen"
      :aria-haspopup="true"
      role="combobox"
    >
      <span class="selected-text">{{ selectedOption?.label || placeholder }}</span>
      <i class="fa fa-chevron-down custom-select-arrow" :class="{ active: isOpen }"></i>
    </div>
    
    <div
      ref="optionsContainerRef"
      class="custom-select-options"
      :class="{ active: isOpen }"
      role="listbox"
    >
      <div
        v-for="option in options"
        :key="option.value"
        class="custom-select-option"
        :class="{ selected: isOptionSelected(option) }"
        :aria-selected="isOptionSelected(option)"
        @click.stop="selectOption(option)"
        @keydown.enter.stop="selectOption(option)"
        @keydown.space.stop="selectOption(option)"
        role="option"
        tabindex="0"
      >
        {{ option.label }}
        <span v-if="showCount && option.count" class="option-count">({{ option.count }})</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, defineProps, defineEmits } from 'vue';

// 组件 props
const props = defineProps({
  modelValue: {
    type: [String, Number, null],
    default: null
  },
  options: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请选择...'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showCount: {
    type: Boolean,
    default: false
  }
});

// 组件 emits
const emits = defineEmits(['update:modelValue', 'change']);

// 组件状态
const isOpen = ref(false);
const triggerRef = ref(null);
const optionsContainerRef = ref(null);

// 计算属性：当前选中的选项
const selectedOption = computed(() => {
  return props.options.find(option => option.value === props.modelValue) || null;
});

// 方法：切换下拉菜单显示/隐藏
const toggleDropdown = () => {
  if (props.disabled) return;
  isOpen.value = !isOpen.value;
  
  if (isOpen.value) {
    document.addEventListener('click', handleOutsideClick);
    document.addEventListener('keydown', handleEscapeKey);
  } else {
    document.removeEventListener('click', handleOutsideClick);
    document.removeEventListener('keydown', handleEscapeKey);
  }
};

// 方法：选择选项
const selectOption = (option) => {
  if (props.disabled) return;
  
  emits('update:modelValue', option.value);
  emits('change', option);
  
  isOpen.value = false;
  document.removeEventListener('click', handleOutsideClick);
  document.removeEventListener('keydown', handleEscapeKey);
};

// 方法：检查选项是否被选中
const isOptionSelected = (option) => {
  return option.value === props.modelValue;
};

// 方法：处理点击外部关闭下拉菜单
const handleOutsideClick = (event) => {
  if (
    triggerRef.value &&
    !triggerRef.value.contains(event.target) &&
    optionsContainerRef.value &&
    !optionsContainerRef.value.contains(event.target)
  ) {
    isOpen.value = false;
    document.removeEventListener('click', handleOutsideClick);
    document.removeEventListener('keydown', handleEscapeKey);
  }
};

// 方法：处理 ESC 键关闭下拉菜单
const handleEscapeKey = (event) => {
  if (event.key === 'Escape') {
    isOpen.value = false;
    document.removeEventListener('click', handleOutsideClick);
    document.removeEventListener('keydown', handleEscapeKey);
  }
};

// 生命周期钩子：组件挂载后
onMounted(() => {
  // 确保初始值有效
  const initialOption = props.options.find(option => option.value === props.modelValue);
  if (!initialOption && props.modelValue !== null) {
    emits('update:modelValue', null);
  }
});

// 生命周期钩子：组件卸载前
onBeforeUnmount(() => {
  document.removeEventListener('click', handleOutsideClick);
  document.removeEventListener('keydown', handleEscapeKey);
});

// 监听选中值变化
watch(() => props.modelValue, (newValue) => {
  // 如果外部更新了值，确保下拉菜单关闭
  if (isOpen.value) {
    isOpen.value = false;
    document.removeEventListener('click', handleOutsideClick);
    document.removeEventListener('keydown', handleEscapeKey);
  }
});
</script>

<style scoped>
/* 基础样式 */
.custom-select-container {
  position: relative;
  width: 100%;
}

.custom-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 16px;
  background-color: #f3f4f6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  border: none;
}

.custom-select-trigger:focus {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.5);
}

.is-disabled .custom-select-trigger {
  opacity: 0.7;
  cursor: not-allowed;
}

.custom-select-options {
  position: absolute;
  left: 0;
  right: 0;
  margin-top: 4px;
  background-color: #f9fafb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 10;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  transform: translateY(-8px);
}

.custom-select-options.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.custom-select-option {
  padding: 8px 16px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.15s ease, color 0.15s ease;
}

.custom-select-option:hover {
  background-color: #818cf8;
  color: white;
}

.custom-select-option.selected {
  background-color: #4f46e5;
  color: white;
}

.custom-select-arrow {
  transition: transform 0.2s ease;
}

.custom-select-arrow.active {
  transform: rotate(180deg);
}

.option-count {
  margin-left: 4px;
  font-size: 0.75rem;
  color: #6b7280;
}

.selected-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>    