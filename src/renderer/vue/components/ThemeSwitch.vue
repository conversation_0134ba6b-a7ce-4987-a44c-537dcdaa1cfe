<template>
  <div class="switch-control">
    <div class="switch-header">
      <label class="switch-label">
        <span class="switch-text">{{ isDarkMode ? '暗色主题' : '亮色主题' }}</span>
        <div class="switch-wrapper">
          <input
            type="checkbox"
            id="theme-mode"
            name="themeMode"
            v-model="isDarkMode"
            @change="toggleTheme"
          >
          <span class="switch-slider"></span>
        </div>
      </label>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';

const isDarkMode = ref(false);

// 切换主题
const toggleTheme = () => {
  if (isDarkMode.value) {
    document.documentElement.setAttribute('data-theme', 'dark');
    localStorage.setItem('theme', 'dark');
  } else {
    document.documentElement.setAttribute('data-theme', 'light');
    localStorage.setItem('theme', 'light');
  }
};

// 初始化主题
const initTheme = () => {
  const savedTheme = localStorage.getItem('theme');
  if (savedTheme === 'light') {
    isDarkMode.value = false;
  } else {
    // 默认使用暗色主题
    isDarkMode.value = true;
  }
};

// 监听系统主题变化
const listenForSystemThemeChanges = () => {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

  // 初始检查 - 只设置状态，不应用主题
  if (mediaQuery.matches && !localStorage.getItem('theme')) {
    isDarkMode.value = true;
  }

  // 监听变化 - 只在用户未手动设置主题时更新状态
  mediaQuery.addEventListener('change', (e) => {
    if (!localStorage.getItem('theme')) {
      isDarkMode.value = e.matches;
    }
  });
};

// 生命周期钩子
onMounted(() => {
  initTheme();
  listenForSystemThemeChanges();
});

// 导出组件状态
defineExpose({
  isDarkMode
});
</script>

<style scoped>
.switch-control {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-card-background);
  border-radius: var(--border-radius-sm);
  border-left: 4px solid var(--color-primary-light);
}

.switch-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.switch-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.switch-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.switch-wrapper {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch-wrapper input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-text-disabled);
  transition: .4s;
  border-radius: 24px;
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: var(--color-card-background);
  transition: .4s;
  border-radius: 50%;
}

input:checked + .switch-slider {
  background-color: var(--color-primary);
}

input:checked + .switch-slider:before {
  transform: translateX(26px);
}

input:disabled + .switch-slider {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
