<template>
  <div class="dialog-overlay" @click="$emit('cancel')">
    <div class="dialog-content" @click.stop>
      <div class="dialog-header">
        <h3>{{ title }}</h3>
        <button class="btn-close" @click="$emit('cancel')">&times;</button>
      </div>

      <div class="dialog-body">
        <div class="assignment-header">
          <div class="group-selector">
            <label for="group-select" class="group-label">分组:</label>
            <div v-if="groups.length > 0">
              <select
                id="group-select"
                v-model="selectedGroupId"
                class="group-select"
                @change="onGroupChange"
              >
                <option :value="null" disabled>请选择分组</option>
                <option
                  v-for="group in groups"
                  :key="group.id"
                  :value="group.id"
                >
                  {{ group.name }} ({{ group.devices.length }})
                </option>
              </select>
            </div>
            <div v-else class="no-groups-message">
              <span>暂无分组，请先</span>
              <button class="btn btn-text create-group-btn" @click="$emit('createGroup')">创建分组</button>
            </div>
          </div>

          <div class="search-box">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="搜索设备..."
              @input="filterDevices"
            >
          </div>
        </div>

        <div class="device-list">
          <div v-if="loading" class="loading-indicator">
            <div class="spinner"></div>
            <p>加载中...</p>
          </div>

          <div v-else-if="filteredDevices.length === 0" class="empty-message">
            暂无可分配的设备
          </div>

          <div v-else class="device-items">
            <div class="device-list-header">
              <div class="select-all">
                <input
                  type="checkbox"
                  :checked="isAllSelected"
                  :indeterminate="isIndeterminate"
                  @change="toggleSelectAll"
                >
                <span>全选</span>
              </div>
              <div class="device-count">
                已选择 {{ selectedDevices.length }} / {{ filteredDevices.length }} 个设备
              </div>
            </div>

            <div class="device-grid">
              <div
                v-for="device in filteredDevices"
                :key="device.sn"
                class="device-item"
                :class="{
                  'selected': isDeviceSelected(device.sn),
                  'offline': !device.isOnline
                }"
                @click="toggleDeviceSelection(device.sn)"
              >
                <div class="device-info">
                  <div class="device-name">
                    <span class="device-id">ID: {{ device.id || '未设置' }}</span>
                    <span class="device-sn">SN: {{ device.sn }}</span>
                  </div>
                  <div class="device-status">{{ device.isOnline ? '在线' : '离线' }}</div>
                </div>
                <div class="checkbox-wrapper">
                  <input
                    type="checkbox"
                    :checked="isDeviceSelected(device.sn)"
                    @change="toggleDeviceSelection(device.sn)"
                    @click.stop
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <button class="btn btn-secondary" @click="$emit('cancel')">取消</button>
        <button class="btn btn-primary" @click="assignDevices">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';

// 定义组件属性
const props = defineProps({
  // 设备列表
  devices: {
    type: Array,
    default: () => []
  },
  // 所有设备组列表
  groups: {
    type: Array,
    default: () => []
  },
  // 已选择的设备SN列表
  initialSelectedDevices: {
    type: Array,
    default: () => []
  },
  // 对话框标题
  title: {
    type: String,
    default: '分配设备到分组'
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['cancel', 'assign', 'createGroup']);

// 搜索查询
const searchQuery = ref('');
// 已选择的设备SN列表
const selectedDevices = ref([...props.initialSelectedDevices]);
// 过滤后的设备列表
const filteredDevices = ref([...props.devices]);
// 选中的分组ID
const selectedGroupId = ref(null);

// 当前选中的分组
const selectedGroup = computed(() => {
  if (!selectedGroupId.value) return null;
  return props.groups.find(g => g.id === selectedGroupId.value) || null;
});

// 处理分组变化
const onGroupChange = () => {
  console.log('选择的分组ID:', selectedGroupId.value);
};

// 是否全部选中
const isAllSelected = computed(() => {
  return filteredDevices.value.length > 0 &&
         selectedDevices.value.length === filteredDevices.value.length;
});

// 是否部分选中
const isIndeterminate = computed(() => {
  return selectedDevices.value.length > 0 &&
         selectedDevices.value.length < filteredDevices.value.length;
});

// 检查设备是否被选中
const isDeviceSelected = (sn) => {
  return selectedDevices.value.includes(sn);
};

// 切换设备选择状态
const toggleDeviceSelection = (sn) => {
  const index = selectedDevices.value.indexOf(sn);
  if (index === -1) {
    selectedDevices.value.push(sn);
  } else {
    selectedDevices.value.splice(index, 1);
  }
};

// 切换全选状态
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedDevices.value = [];
  } else {
    selectedDevices.value = filteredDevices.value.map(device => device.sn);
  }
};

// 过滤设备
const filterDevices = () => {
  if (!searchQuery.value) {
    filteredDevices.value = [...props.devices];
    return;
  }

  const query = searchQuery.value.toLowerCase();
  filteredDevices.value = props.devices.filter(device => {
    return (
      device.sn.toLowerCase().includes(query) ||
      (device.id && device.id.toString().toLowerCase().includes(query))
    );
  });
};

// 分配设备
const assignDevices = () => {
  if (!selectedGroupId.value) {
    alert('请选择一个分组');
    return;
  }

  if (selectedDevices.value.length === 0) {
    alert('请选择至少一个设备');
    return;
  }

  emit('assign', {
    groupId: selectedGroupId.value,
    devices: selectedDevices.value
  });
};

// 监听设备列表变化
watch(() => props.devices, (newDevices) => {
  filteredDevices.value = [...newDevices];
  filterDevices();
}, { deep: true });

// 监听初始选中设备变化
watch(() => props.initialSelectedDevices, (newSelectedDevices) => {
  selectedDevices.value = [...newSelectedDevices];
}, { deep: true });

// 组件挂载时初始化
onMounted(() => {
  filterDevices();
});
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog-content {
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: dialog-fade-in 0.3s ease;
}

@keyframes dialog-fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.btn-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: color 0.2s ease;
}

.btn-close:hover {
  color: var(--color-text-primary);
}

.dialog-body {
  padding: var(--spacing-md);
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.assignment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.group-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.group-label {
  font-weight: 600;
}

.group-select {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: white;
  min-width: 200px;
  color: var(--color-text-primary);
  font-weight: 500;
}

.no-groups-message {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--color-text-secondary);
}

.create-group-btn {
  color: var(--color-primary);
  padding: 4px 8px;
  font-weight: 500;
  text-decoration: underline;
  background: none;
  border: none;
  cursor: pointer;
}

.create-group-btn:hover {
  color: var(--color-primary-dark);
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-box input {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: var(--color-background-light);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-box input:focus {
  border-color: var(--color-primary);
  outline: none;
}

.device-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.device-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-background-light);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  border: 1px solid var(--color-border);
  border-bottom: none;
}

.select-all {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.device-count {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: var(--color-background-light);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
  border: 1px solid var(--color-border);
  overflow-y: auto;
  max-height: 400px;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.device-item:hover {
  background-color: var(--color-background-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.device-item.selected {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
}

.device-item.offline {
  opacity: 0.7;
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.device-name {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.device-id {
  font-weight: 600;
  font-size: 14px;
}

.device-sn {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.device-status {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-sm);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--color-text-secondary);
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--color-border);
}

.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-secondary {
  background-color: var(--color-background-light);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-background-dark);
}
</style>
