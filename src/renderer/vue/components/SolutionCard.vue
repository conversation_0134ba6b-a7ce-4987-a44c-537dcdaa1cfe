<template>
  <div class="solution-card-container">
    <div class="solution-card">
      <div class="card-content">
        <div class="card-title">{{ title }}</div>
        <p v-if="description" class="card-description">{{ description }}</p>
        <div class="card-meta">
          <slot name="meta"></slot>
        </div>
      </div>
      <div class="card-actions">
        <slot name="actions"></slot>
      </div>
    </div>

    <div class="update">
      <slot name="time">{{ time }}</slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  isEmpty: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    required: true,
  },
  description: {
    type: String,
    default: "",
  },
  time: {
    type: String,
    default: "",
  },
});
</script>

<style scoped>
.solution-card {
  border-radius: var(--border-radius);
  overflow: hidden;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
  display: flex;
  flex-direction: column;
  width: 25vw;
  height: auto;
  aspect-ratio: 280 / 400;
  margin: var(--spacing-md) auto;
  background-image: url("@assets/images/solutions_bg.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding-bottom: 8vh;
}

.solution-card:hover {
  /* transform: translateY(-2px);
  box-shadow: var(--shadow-md); */
}

.card-content {
  flex: 1;
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  justify-content: center;
}

.empty-icon {
  font-size: 48px;
  color: var(--color-text-disabled);
  margin-bottom: var(--spacing-md);
}

.card-title {
  color: white;
  font-size: 36px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 4px;
}

.card-description {
  margin: 0 0 var(--spacing-md);
  font-size: 14px;
  color: var(--color-text-secondary);
}

.card-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  font-size: 14px;
  color: var(--color-text-secondary);
}

.card-actions {
  padding: var(--spacing-md);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

.solution-card-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.update {
  color: #6e6e6e;
  text-align: center;
  font-size: 10px;
  font-style: normal;
  font-weight: 340;
  line-height: 18px; /* 180% */
}
</style>
