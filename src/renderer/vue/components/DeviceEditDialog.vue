<template>
  <div class="dialog-overlay" @click="$emit('cancel')">
    <div class="dialog-content" @click.stop>
      <img
        class="btn-close"
        @click="$emit('cancel')"
        src="@assets/svg/close.svg"
      />
      <div class="dialog-header">编辑设备</div>

      <div class="dialog-body">
        <!-- 设备基本信息 -->
        <div class="device-info-section">
          <div class="device-sn-display">
            <span class="label">设备序列号:</span>
            <span class="value">{{ device.sn }}</span>
          </div>

          <div class="device-status-display">
            <span class="label">设备状态:</span>
            <span
              class="status-badge"
              :class="{ online: device.isOnline, offline: !device.isOnline }"
            >
              {{ device.isOnline ? "在线" : "离线" }}
            </span>
          </div>
        </div>

        <!-- 设备ID编辑 -->
        <div class="form-group">
          <label for="device-id">设备ID:</label>
          <input
            type="text"
            id="device-id"
            v-model="deviceId"
            class="form-control"
            placeholder="请输入设备ID"
          />
        </div>

        <!-- 设备分组选择 -->
        <!-- <div class="form-group">
          <label>设备分组 (单选):</label>
          <div class="group-selection">
            <div v-if="loading.groups" class="loading-indicator">
              <div class="spinner-small"></div>
              <span>加载分组...</span>
            </div>
            <div v-else-if="deviceGroups.length === 0" class="empty-groups">
              暂无可用分组，请先创建分组
            </div>
            <div v-else class="group-list">
              <div
                v-for="group in deviceGroups"
                :key="group.id"
                class="group-item"
                :class="{ selected: selectedGroup === group.id }"
                @click="selectGroup(group.id)"
              >
                <div class="group-radio">
                  <input
                    type="radio"
                    name="deviceGroup"
                    :checked="selectedGroup === group.id"
                    @click.stop
                    @change="selectGroup(group.id)"
                  />
                  <span class="radio-custom"></span>
                </div>
                <div class="group-info">
                  <div class="group-name">{{ group.name }}</div>
                  <div class="group-device-count">
                    {{ group.devices.length }} 个设备
                  </div>
                </div>
              </div>
              <div
                class="group-item"
                :class="{ selected: selectedGroup === null }"
                @click="selectGroup(null)"
              >
                <div class="group-radio">
                  <input
                    type="radio"
                    name="deviceGroup"
                    :checked="selectedGroup === null"
                    @click.stop
                    @change="selectGroup(null)"
                  />
                  <span class="radio-custom"></span>
                </div>
                <div class="group-info">
                  <div class="group-name">不分配到任何组</div>
                </div>
              </div>
            </div>
          </div>
        </div> -->
      </div>

      <!-- ID冲突提示 -->
      <div v-if="idConflict" class="dialog-conflict-alert">
        <div class="conflict-icon">
          <i class="icon-warning"></i>
        </div>
        <div class="conflict-message">
          <div class="conflict-title">ID冲突</div>
          <div class="conflict-description">
            ID {{ deviceId }} 已被设备 {{ idConflict.sn }} 使用。
            <div class="conflict-options">
              <label class="conflict-checkbox">
                <input type="checkbox" v-model="forceSwap" />
                <span class="checkbox-text">强制交换两个设备的ID</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="dialog-footer">
        <!-- <button class="btn btn-secondary" @click="$emit('cancel')">取消</button> -->
        <button class="btn btn-primary" @click="saveChanges" :disabled="saving">
          <span v-if="saving">保存中...</span>
          <span v-else-if="idConflict && forceSwap">强制交换并保存</span>
          <span v-else>保存</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useElectronAPI } from "../plugins/electron";
import alertService from "../plugins/alert";

// 使用Electron API
const electronAPI = useElectronAPI();

// 定义props
const props = defineProps({
  device: {
    type: Object,
    required: true,
  },
});

// 定义事件
const emit = defineEmits(["save", "cancel"]);

// 状态
const deviceId = ref(props.device.id || "");
const deviceGroups = ref([]);
const selectedGroup = ref(null); // 单选，只存储一个组ID或null
const loading = ref({
  groups: false,
  deviceGroups: false,
});
const saving = ref(false);
const idConflict = ref(null);
const forceSwap = ref(false);

// 生命周期钩子
onMounted(async () => {
  await fetchDeviceGroups();
  await fetchDeviceGroupMemberships();
});

// 获取所有设备分组
const fetchDeviceGroups = async () => {
  try {
    loading.value.groups = true;
    const groups = await electronAPI.getDeviceGroups();
    deviceGroups.value = groups;
    console.log("获取设备分组成功:", groups);
  } catch (error) {
    console.error("获取设备分组失败:", error);
    await alertService.alert({
      title: "获取分组失败",
      message: error.message || "获取设备分组失败",
      confirmButtonText: "确定",
    });
  } finally {
    loading.value.groups = false;
  }
};

// 获取设备所属的分组
const fetchDeviceGroupMemberships = async () => {
  try {
    loading.value.deviceGroups = true;

    // 找出设备所属的分组（现在只会有一个或没有）
    const deviceSn = props.device.sn;
    let foundGroupId = null;

    for (const group of deviceGroups.value) {
      if (group.devices.includes(deviceSn)) {
        foundGroupId = group.id;
        break; // 找到一个就退出循环，因为一个设备只能属于一个组
      }
    }

    selectedGroup.value = foundGroupId;
    console.log("设备所属分组:", foundGroupId);
  } catch (error) {
    console.error("获取设备分组关系失败:", error);
  } finally {
    loading.value.deviceGroups = false;
  }
};

// 选择分组
const selectGroup = (groupId) => {
  selectedGroup.value = groupId;
  console.log("选择分组:", groupId);
};

// 保存更改
const saveChanges = async () => {
  try {
    saving.value = true;

    // 1. 更新设备ID
    // 注意：我们不在这里直接调用API，而是将更新后的数据传递给父组件
    // 由父组件统一处理API调用，这样可以避免重复调用和错误处理
    console.log("准备更新设备ID:", deviceId.value);

    // 如果ID没有变化，直接跳过ID更新
    if (deviceId.value === props.device.id) {
      idConflict.value = null;
    } else {
      try {
        // 尝试检查ID是否已被使用
        // 这里只是为了检测冲突，不实际更新
        if (!idConflict.value) {
          // 只有在没有已知冲突的情况下才检查
          try {
            await electronAPI.updateDeviceId(
              props.device.sn,
              deviceId.value,
              false
            );
            // 如果没有抛出错误，说明没有冲突
            idConflict.value = null;
          } catch (error) {
            if (error.message && error.message.includes("已被设备")) {
              // 提取冲突设备的SN
              const match = error.message.match(/已被设备\s+([^\s]+)\s+使用/);
              if (match && match[1]) {
                idConflict.value = { sn: match[1] };
                // 如果检测到冲突且用户没有选择强制交换，则中断保存过程
                if (!forceSwap.value) {
                  return;
                }
              }
            } else {
              // 其他错误，直接抛出
              throw error;
            }
          }
        }
      } catch (error) {
        // 如果是其他错误（非冲突错误），直接抛出
        if (!error.message || !error.message.includes("已被设备")) {
          throw error;
        }
      }
    }

    // 2. 更新设备分组
    // 获取设备当前所属的分组
    let currentGroup = null;
    for (const group of deviceGroups.value) {
      if (group.devices.includes(props.device.sn)) {
        currentGroup = group.id;
        break; // 找到一个就退出循环，因为一个设备只能属于一个组
      }
    }

    // 需要添加到的分组
    const groupsToAdd = selectedGroup.value ? [selectedGroup.value] : [];

    // 需要从中移除的分组
    const groupsToRemove =
      currentGroup && currentGroup !== selectedGroup.value
        ? [currentGroup]
        : [];

    // 不在这里执行添加和移除操作，而是将数据传递给父组件
    console.log(`设备 ${props.device.sn} 需要添加到分组:`, groupsToAdd);
    console.log(`设备 ${props.device.sn} 需要从分组移除:`, groupsToRemove);

    // 通知父组件保存
    emit("save", {
      sn: props.device.sn,
      id: deviceId.value,
      addedGroups: groupsToAdd,
      removedGroups: groupsToRemove,
      forceSwap: forceSwap.value,
    });

    // 不在这里显示成功提示，由父组件处理
  } catch (error) {
    console.error("准备设备信息失败:", error);
    await alertService.alert({
      title: "操作失败",
      message: error.message || "准备设备信息失败",
      confirmButtonText: "确定",
    });
  } finally {
    saving.value = false;
  }
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999; /* 提高z-index，确保在最上层 */
  animation: fadeIn 0.2s ease-out; /* 添加淡入动画 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-content {
  background-color: var(--color-dialog-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideIn 0.3s ease-out; /* 添加滑入动画 */
  position: relative; /* 确保定位正确 */
  z-index: 10000; /* 确保内容在遮罩之上 */
  padding: 34px;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-md);
  color: var(--color-menu-text);
  font-size: 24px;
  text-align: center;
  font-style: normal;
  font-weight: 480;
  line-height: 18px;
}

.btn-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text-secondary);
  width: 28px;
  height: 28px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.dialog-body {
  padding: var(--spacing-md);
  overflow-y: auto;
  flex: 1;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

/* 设备信息部分 */
.device-info-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border);
}

.device-sn-display,
.device-status-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.label {
  color: var(--color-text-secondary);
  font-size: 14px;
}

.value {
  font-weight: 600;
  color: var(--color-text-primary);
}

.status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.online {
  background-color: var(--color-success-bg);
  color: var(--color-success);
}

.status-badge.offline {
  background-color: var(--color-danger-bg);
  color: var(--color-danger);
}

/* 表单样式 */
.form-group {
  margin-top: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  color: var(--color-text-primary);
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: all 0.2s;
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
  outline: none;
}

.form-hint {
  margin-top: 4px;
  font-size: 12px;
  color: var(--color-text-secondary);
}

/* 分组选择样式 */
.group-selection {
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  max-height: 200px;
  overflow-y: auto;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  gap: var(--spacing-sm);
  color: var(--color-text-secondary);
}

.spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-groups {
  padding: var(--spacing-md);
  text-align: center;
  color: var(--color-text-secondary);
}

.group-list {
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
}

.group-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s;
}

.group-item:last-child {
  border-bottom: none;
}

.group-item:hover {
  background-color: var(--color-background-hover);
}

.group-item.selected {
  background-color: var(--color-box-bg);
}

.group-radio {
  position: relative;
  margin-right: var(--spacing-sm);
}

.group-radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.radio-custom {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: var(--color-background-light);
  border: 2px solid var(--color-border);
  border-radius: 50%;
  transition: all 0.2s;
}

.group-radio input[type="radio"]:checked ~ .radio-custom {
  background-color: var(--color-background-light);
  border-color: var(--color-primary);
}

.group-radio input[type="radio"]:checked ~ .radio-custom:after {
  content: "";
  position: absolute;
  left: 2px;
  top: 2px;
  width: 10px;
  height: 10px;
  background-color: var(--color-primary);
  border-radius: 50%;
}

.group-info {
  flex: 1;
}

.group-name {
  font-weight: 600;
  color: var(--color-text-primary);
}

.group-device-count {
  font-size: 12px;
  color: var(--color-text-secondary);
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;

  width: 220px;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
}

.btn-primary:disabled {
  background-color: var(--color-primary-light);
  cursor: not-allowed;
}

.btn-secondary {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-background-dark);
}

/* 冲突提示样式 */
.dialog-conflict-alert {
  margin: var(--spacing-md) 0;
  padding: var(--spacing-md);
  background-color: var(--color-warning-bg);
  border: 1px solid var(--color-warning);
  border-radius: var(--border-radius);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.conflict-icon {
  color: var(--color-warning);
  font-size: 24px;
  line-height: 1;
}

.conflict-message {
  flex: 1;
}

.conflict-title {
  font-weight: 600;
  color: var(--color-warning-dark);
  margin-bottom: var(--spacing-xs);
}

.conflict-description {
  color: var(--color-text-secondary);
  font-size: 14px;
}

.conflict-options {
  margin-top: var(--spacing-sm);
}

.conflict-checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
}

.checkbox-text {
  font-size: 14px;
  color: var(--color-text-primary);
}
</style>
