<template>
  <div class="device-group-selector">
    <div class="selector-header">
      <h4>{{ title || '设备分组' }}</h4>
      <button v-if="showRefreshButton" class="btn btn-text" @click="refreshGroups">
        <i class="icon-refresh"></i>
      </button>
    </div>
    
    <div class="group-list">
      <div
        class="group-item"
        :class="{ 'active': !selectedGroupId }"
        @click="selectGroup(null)"
      >
        <div class="group-name">全部设备</div>
      </div>
      
      <div
        v-for="group in groups"
        :key="group.id"
        class="group-item"
        :class="{ 'active': selectedGroupId === group.id }"
        @click="selectGroup(group.id)"
      >
        <div class="group-name">{{ group.name }}</div>
        <div class="device-count">{{ group.devices.length }}个设备</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 定义组件属性
const props = defineProps({
  // 设备组列表
  groups: {
    type: Array,
    default: () => []
  },
  // 选中的设备组ID
  selectedGroupId: {
    type: String,
    default: null
  },
  // 标题
  title: {
    type: String,
    default: '设备分组'
  },
  // 是否显示刷新按钮
  showRefreshButton: {
    type: Boolean,
    default: true
  }
});

// 定义事件
const emit = defineEmits(['select', 'refresh']);

// 选择设备组
const selectGroup = (groupId) => {
  emit('select', groupId);
};

// 刷新设备组列表
const refreshGroups = () => {
  emit('refresh');
};

// 组件挂载时刷新设备组列表
onMounted(() => {
  refreshGroups();
});
</script>

<style scoped>
.device-group-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--color-background-light);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.selector-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.group-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.group-item {
  display: flex;
  flex-direction: column;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.group-item:hover {
  background-color: var(--color-background-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.group-item.active {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
}

.group-name {
  font-weight: 600;
  font-size: 14px;
}

.device-count {
  font-size: 12px;
  color: var(--color-text-secondary);
  margin-top: 4px;
}

.btn-text {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
}

.btn-text:hover {
  background-color: var(--color-background-dark);
  color: var(--color-text-primary);
}
</style>
