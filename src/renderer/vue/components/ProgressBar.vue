<template>
  <div class="progress-wrapper">
    <div class="deploy-progress-container">
      <div class="deploy-progress-bar" :style="{ width: `${progress || 0}%` }"></div>
    </div>
    <span class="deploy-progress-text">
      {{ progress || 0 }}%
      <span v-if="currentFile > 0" class="file-count">({{ Math.round((currentFile / totalFiles) * 100) }}%)</span>
    </span>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { useElectronAPI } from '../plugins/electron';

const electronAPI = useElectronAPI();

// 定义组件属性
const props = defineProps({
  // 设备SN
  deviceSN: {
    type: String,
    required: true
  },
  // 初始进度值
  initialProgress: {
    type: Number,
    default: 0
  }
});

// 内部状态
const progress = ref(props.initialProgress || 0);
const progressHandler = ref(null);
const currentFile = ref(0);
const totalFiles = ref(0);

// 进度事件处理函数
const handleTransferProgress = (data) => {
  if (!data || !data.deviceSN) {
    console.warn('进度事件缺少设备SN:', data);
    return;
  }

  // 只处理当前设备的进度事件
  if (data.deviceSN === props.deviceSN) {
    // 更新进度
    progress.value = Math.round(data.progress * 100) || 0;

    // 更新文件计数（如果有）
    if (data.fileIndex !== undefined) {
      // fileIndex 是从0开始的，所以加1使其从1开始
      currentFile.value = data.fileIndex + 1;
    } else if (data.currentFile !== undefined) {
      // 兼容可能的 currentFile 字段
      currentFile.value = data.currentFile;
    }

    if (data.totalFiles !== undefined) {
      totalFiles.value = data.totalFiles;
    }

    // 只在开发环境下的关键节点记录日志
    if (process.env.NODE_ENV === 'development' && (progress.value === 0 || progress.value === 100)) {
      console.log(`设备 ${props.deviceSN} 的进度更新为 ${progress.value}%，文件：${currentFile.value}/${totalFiles.value}`);
    }
  }
};

// 注册事件监听器
const registerEventListeners = () => {
  // 先移除旧的事件监听器
  removeEventListeners();

  // 添加传输进度事件监听
  if (process.env.NODE_ENV === 'development') {
    console.log(`注册设备 ${props.deviceSN} 的进度事件监听器`);
  }
  progressHandler.value = electronAPI.onTransferProgress(handleTransferProgress);
};

// 移除事件监听器
const removeEventListeners = () => {
  if (progressHandler.value) {
    try {
      electronAPI.offTransferProgress(progressHandler.value);
      progressHandler.value = null;
    } catch (error) {
      console.error('移除进度事件监听器失败:', error);
    }
  }
};

// 监听初始进度值变化
watch(() => props.initialProgress, (newValue) => {
  progress.value = newValue || 0;
});

// 组件挂载时注册事件监听器
onMounted(() => {
  registerEventListeners();
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  removeEventListeners();
});
</script>

<style scoped>
/* 进度条包装器 */
.progress-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 0 0 auto; /* 不伸缩 */
  width: 40%; /* 增加宽度以容纳文件计数 */
  min-width: 100px;
  margin: 0 var(--spacing-sm); /* 左右间距 */
}

/* 部署进度条样式 */
.deploy-progress-container {
  width: 85%;
  height: 6px;
  background-color: var(--color-border-light);
  border-radius: var(--border-radius-sm);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-right: var(--spacing-xs);
}

.deploy-progress-bar {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-sm);
  transition: width var(--transition-fast) ease;
  box-shadow: var(--shadow-sm);
}

/* 完成时的动画效果 */
.deploy-progress-bar[style*="width: 100%"] {
  background-image: linear-gradient(45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent);
  background-size: 20px 20px;
  animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
  from { background-position: 0 0; }
  to { background-position: 20px 0; }
}

.deploy-progress-text {
  font-size: 12px;
  color: var(--color-primary);
  font-weight: 700;
  white-space: nowrap;
  min-width: calc(var(--spacing-md) + var(--spacing-sm));
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.file-count {
  font-size: 10px;
  font-weight: normal;
  color: var(--color-text-secondary);
  margin-top: -2px;
}
</style>
