<template>
  <div class="dialog-overlay">
    <div class="dialog-content">
      <img
        class="close-button"
        @click="$emit('close')"
        src="@assets/svg/close.svg"
      />
      <div class="dialog-header">
        <div class="title">选择资源类型</div>
      </div>
      <el-radio-group v-model="selectedType" class="resource-types">
        <el-radio
          v-for="type in mainTypeOptions"
          class="option-item"
          :key="type.value"
          @click="selectedType = type.value"
        >
          <img
            v-if="selectedType == type.value"
            src="@assets/svg/option_s.svg"
            alt="option"
          />
          <img v-else src="@assets/svg/option.svg" alt="" />
          {{ type.label }}
        </el-radio>
      </el-radio-group>
      <button class="btn btn-primary" @click="onConfirm">
        <div class="btn-text">确定</div>
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import {
  MainFileType,
  MainTypeLabels,
} from "../../../shared/types/resource.js";

// 定义事件
const emit = defineEmits(["select", "close"]);

// 主类型选项
const mainTypeOptions = computed(() => {
  return [
    { value: MainFileType.VIDEO, label: MainTypeLabels[MainFileType.VIDEO] },
    { value: MainFileType.IMAGE, label: MainTypeLabels[MainFileType.IMAGE] },
    { value: MainFileType.APP, label: MainTypeLabels[MainFileType.APP] },
  ];
});

// 选中的类型
const selectedType = ref(mainTypeOptions.value[0]?.value);

// 确认选择
const onConfirm = () => {
  emit("select", selectedType.value);
  emit("close");
};
</script>

<style scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.title {
  width: 100%;
  color: var(--color-menu-text);
  text-align: center;
  font-size: 24px;
  font-style: normal;
  font-weight: 480;
  line-height: 18px; /* 75% */
}

.dialog-content {
  background-color: var(--color-dialog-background);
  border-radius: 16px;
  width: 32vw;
  aspect-ratio: 1.6;
  max-width: 90vw;
  overflow: hidden;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 34px;

  position: relative;
}

.dialog-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--color-text-primary);
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-text-secondary);
  width: 28px;
  height: 28px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--color-danger);
}

.resource-types {
  padding: 24px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 15px;

  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 36px;
  cursor: pointer;
}
.option-item img {
  width: 28px;
  height: 28px;
}
.btn-primary {
  width: 220px;
  padding: 8px 14px;
}
</style>
