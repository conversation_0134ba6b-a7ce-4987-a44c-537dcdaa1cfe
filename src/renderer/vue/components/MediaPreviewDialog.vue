<template>
  <!-- 视频播放对话框 -->
  <div
    v-if="type === 'video' && show"
    class="media-player-dialog"
    @keydown.esc="close"
    @click="close"
    tabindex="0"
  >
    <div class="media-player-container" @click.stop>
      <!-- 左上角资源名称 -->
      <div class="resource-name" @click="close">
        <img src="@assets/svg/control/play_min.svg" alt="" />
        <span>{{ media ? media.showName || media.fileName : "视频播放" }}</span>
      </div>

      <div class="media-player-content">
        <video
          ref="videoPlayer"
          :src="mediaUrl"
          @error="handleMediaError"
          @timeupdate="handleTimeUpdate"
          @loadedmetadata="handleLoadedMetadata"
        ></video>

        <!-- 自定义控制栏 -->
        <div class="custom-controls">
          <!-- 进度条 -->
          <div class="progress-bar">
            <div class="progress" :style="{ width: progress + '%' }"></div>
            <input
              type="range"
              class="progress-slider"
              :value="progress"
              @input="handleProgressChange"
              min="0"
              max="100"
            />
          </div>

          <!-- 控制按钮 -->
          <div class="control-buttons">
            <div class="control-progress">
              <button class="control-btn" @click="handleRewind">
                <img src="@assets/svg/control/video_pre.svg" alt="" />
              </button>
              <button class="control-btn" @click="handlePlayPause">
                <img
                  v-if="isPlaying"
                  src="@assets/svg/control/video_pause.svg"
                  alt=""
                />
                <img v-else src="@assets/svg/control/video_pre.svg" alt="" />
              </button>
              <button class="control-btn" @click="handleForward">
                <img src="@assets/svg/control/video_next.svg" alt="" />
              </button>
            </div>

            <button
              v-if="isPublished"
              class="control-btn stop-btn"
              @click="handleStopControl"
            >
              <img src="@assets/svg/control/play_stop.svg" alt="" />
              <span>取消播放</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 图片预览对话框 -->
  <div
    v-if="type === 'image' && show"
    class="media-preview-dialog"
    @keydown.esc="close"
    @click="close"
    tabindex="0"
  >
    <div class="media-preview-container" @click.stop>
      <!-- 左上角资源名称 -->
      <div class="resource-name" @click="close">
        <img src="@assets/svg/control/play_min.svg" alt="" />
        <span>{{ media ? media.showName || media.fileName : "图片预览" }}</span>
      </div>

      <!-- 右下角取消播控按钮 - 只在资源已发布时显示 -->
      <div
        v-if="isPublished"
        class="img-stop-control"
        @click="handleStopControl"
      >
        <img src="@assets/svg/control/play_stop.svg" alt="" />
        <span>取消播放</span>
      </div>

      <div class="media-preview-content">
        <img
          :src="mediaUrl"
          :alt="media ? media.showName || media.fileName : '图片预览'"
          @error="handleMediaError"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick, onMounted, computed } from "vue";
import alertService from "../plugins/alert";

// 定义props
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "image",
    validator: (value) => ["image", "video"].includes(value),
  },
  media: {
    type: Object,
    default: null,
  },
  mediaUrl: {
    type: String,
    default: "",
  },
  showPreview: {
    type: Boolean,
    default: false,
  },
  fullscreen: {
    type: Boolean,
    default: false,
  },
  isPublished: {
    type: Boolean,
    default: false,
  },
});

// 定义事件
const emit = defineEmits(["close", "error", "stop-control"]);

// 视频播放器引用
const videoPlayer = ref(null);

// 视频控制相关状态
const isPlaying = ref(false);
const progress = ref(0);
const duration = ref(0);

const rightValue = computed(() => (props.fullscreen ? "0px" : "330px"));

// 监听show属性变化
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      // 显示对话框时设置焦点
      nextTick(() => {
        if (props.type === "video") {
          const dialogContainer = document.querySelector(
            ".media-player-dialog"
          );
          if (dialogContainer) {
            dialogContainer.focus();
          }

          if (videoPlayer.value) {
            videoPlayer.value.focus();
          }
        } else {
          const dialogContainer = document.querySelector(
            ".media-preview-dialog"
          );
          if (dialogContainer) {
            dialogContainer.focus();
          }
        }
      });
    }
  }
);

// 关闭对话框
const close = () => {
  // 如果是视频，暂停播放
  if (props.type === "video" && videoPlayer.value) {
    videoPlayer.value.pause();
  }

  // 触发关闭事件
  emit("close");
};

// 处理取消播控
const handleStopControl = async () => {
  // 确认对话框
  const confirmed = await alertService.confirm({
    title: "确认停止",
    message: `确定要停止此发布记录吗？这将停止所有相关设备的发布。`,
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  });

  if (!confirmed) {
    return;
  }
  // 触发停止播控事件
  emit("stop-control");
  // 关闭对话框
  close();
};

// 处理媒体加载错误
const handleMediaError = (event) => {
  console.error(`${props.type === "video" ? "视频" : "图片"}加载失败:`, event);

  // 显示错误提示
  alertService.alert({
    title: `${props.type === "video" ? "播放" : "预览"}失败`,
    message: `无法加载${props.type === "video" ? "视频" : "图片"}文件`,
    type: "error",
  });

  // 触发错误事件
  emit("error", event);

  // 关闭对话框
  close();
};

// 处理播放/暂停
const handlePlayPause = () => {
  if (videoPlayer.value) {
    if (isPlaying.value) {
      videoPlayer.value.pause();
    } else {
      videoPlayer.value.play();
    }
    isPlaying.value = !isPlaying.value;
  }
};

// 处理快进
const handleForward = () => {
  if (videoPlayer.value) {
    videoPlayer.value.currentTime = Math.min(
      videoPlayer.value.currentTime + 10,
      videoPlayer.value.duration
    );
  }
};

// 处理快退
const handleRewind = () => {
  if (videoPlayer.value) {
    videoPlayer.value.currentTime = Math.max(
      videoPlayer.value.currentTime - 10,
      0
    );
  }
};

// 处理进度条变化
const handleProgressChange = (event) => {
  if (videoPlayer.value) {
    const newTime = (event.target.value / 100) * videoPlayer.value.duration;
    videoPlayer.value.currentTime = newTime;
  }
};

// 处理时间更新
const handleTimeUpdate = () => {
  if (videoPlayer.value) {
    progress.value =
      (videoPlayer.value.currentTime / videoPlayer.value.duration) * 100;
  }
};

// 处理视频元数据加载
const handleLoadedMetadata = () => {
  if (videoPlayer.value) {
    duration.value = videoPlayer.value.duration;
    // 自动开始播放
    videoPlayer.value.play();
    isPlaying.value = true;
  }
};

// 组件挂载后
onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped>
/* 共享样式 */
.media-player-dialog,
.media-preview-dialog {
  position: absolute;
  top: 0;
  left: 0;
  right: v-bind(rightValue); /* 右侧播放列表宽度 */
  bottom: 0;
  background-color: transparent;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  z-index: 99; /* 降低z-index，确保在折叠按钮下方 */
  pointer-events: none; /* 允许点击穿透到下层 */
  border-top-left-radius: 8px;
  overflow: hidden;
}

.media-player-container,
.media-preview-container {
  width: 100%;
  height: 100%;
  background-color: var(--color-background);
  border-radius: 0;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  pointer-events: auto; /* 恢复容器内的点击事件 */
  z-index: 99; /* 保持与父元素相同的z-index */
  position: relative; /* 添加相对定位 */
}

/* 左上角资源名称样式 */
.resource-name {
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  z-index: 100;
  transition: all 0.3s ease;

  max-width: 60vw;
  white-space: nowrap; /* 禁止换行 */
  text-overflow: ellipsis; /* 超出内容用省略号表示 */
  overflow: hidden; /* 隐藏超出内容 */

  color: #fff;
  text-align: left;
  font-size: 23.717px;
  font-style: normal;
  font-weight: 480;
  line-height: 17.788px; /* 75% */
}

.resource-name:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 右下角取消播控按钮样式 */
.img-stop-control {
  position: absolute;
  right: 36px;
  bottom: 36px;
  color: #f69422;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  z-index: 101;
}

.media-player-content,
.media-preview-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  height: 100%;
  position: relative;
  padding: 80px 30px;
}

.media-player-content video {
  max-width: 100%;
  max-height: 100%;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.media-preview-content img {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
}

/* 自定义控制栏样式 */
.custom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 进度条样式 */
.progress-bar {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  cursor: pointer;
}

.progress {
  position: absolute;
  height: 100%;
  background: var(--color-primary);
  border-radius: 2px;
  transition: width 0.1s linear;
}

.progress-slider {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

/* 控制按钮样式 */
.control-buttons {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
}

.control-progress {
  display: flex;
  gap: 20px;
  align-items: center;
}

.control-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.control-btn:hover {
  color: var(--color-primary);
}

.control-btn i {
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 取消播控按钮样式 */
.stop-btn {
  color: #f69422;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stop-btn:hover {
  color: #f69422;
  opacity: 0.8;
}

.stop-btn span {
  font-size: 14px;
}

/* 分隔线 */
.control-divider {
  width: 1px;
  height: 20px;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 10px;
}

/* 深色主题适配 */
[data-theme="dark"] .media-player-container,
[data-theme="dark"] .media-preview-container {
  background-color: var(--color-background-dark);
}

[data-theme="dark"] .media-player-content,
[data-theme="dark"] .media-preview-content {
  background-color: var(--color-background-darkest);
}

[data-theme="dark"] .custom-controls {
  background: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .progress-bar {
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .control-divider {
  background: rgba(255, 255, 255, 0.2);
}
</style>
