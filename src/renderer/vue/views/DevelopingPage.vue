<template>
  <div class="page">
    <div class="header">
      <h3>{{ pageTitle }}</h3>
    </div>
    <div class="empty-page">
      <h1>{{ pageTitle }}功能开发中</h1>
      <p>此页面尚未完成，敬请期待</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// 根据路由路径获取页面标题
const pageTitle = computed(() => {
  switch (route.path) {
    case '/devices':
      return '设备管理';
    case '/add-device':
      return '添加设备';
    case '/control':
      return '播控';
    case '/solutions':
      return '方案管理';
    default:
      return '页面';
  }
});
</script>

<style scoped>
.page {
  padding: var(--spacing-md); /* 使用中等内边距 */
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--color-text-primary);
  padding-bottom: 4px;
}

.empty-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  margin: var(--spacing-xs);
  text-align: center;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  width: calc(100% - var(--spacing-xs) * 2); /* 减去左右margin */
  box-sizing: border-box;
  flex: 1; /* 占据剩余空间 */
  overflow: auto; /* 内容过多时允许滚动 */
}

h1 {
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
}

p {
  color: var(--color-text-secondary);
}
</style>
