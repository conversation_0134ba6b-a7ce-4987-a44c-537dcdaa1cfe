<template>
  <div class="page">
    <div class="header-title">设置</div>
    <div class="settings-panel">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else-if="error" class="error">{{ error }}</div>
      <div v-else>
        <div class="settings-section">
          <div class="switch-header">
            <label class="switch-label">
              <span class="switch-text">受控模式</span>
              <div class="switch-wrapper">
                <input
                  type="checkbox"
                  id="controlled-mode"
                  name="controlledMode"
                  v-model="isControlledMode"
                  @change="toggleControlledMode"
                />
                <span class="switch-slider"></span>
              </div>
            </label>
            <span class="desc">开启后，所有的VR设备将处于受控状态。</span>
          </div>

          <div class="path-control" v-show="false">
            <div class="path-row">
              <div class="path-title">资源存储路径</div>
              <div
                class="path-display"
                :class="{ 'path-empty': !resourcesPath }"
              >
                {{
                  resourcesPath ||
                  "使用默认路径 (Documents/DeviceManagerResources)"
                }}
              </div>
              <div class="path-actions">
                <button
                  class="path-button select-button"
                  @click="selectResourcesPath"
                >
                  选择路径
                </button>
                <button
                  class="path-button reset-button"
                  @click="clearResourcesPath"
                  :disabled="!resourcesPath"
                >
                  恢复默认
                </button>
              </div>
            </div>
            <div class="path-description">
              修改后的路径将在应用重启后生效。如果更改了路径，请手动将原路径下的文件复制到新路径。
            </div>
          </div>
        </div>

        <!-- <div class="settings-section">
          <h4>外观设置</h4>
          <LogoSettings />
        </div> -->

        <!-- 版本信息区域 -->
        <div class="version-info">
          <div class="version-info-content">
            <div class="version-header">
              <span class="app-name">{{ versionInfo.productName }}</span>
              <span class="version-number">v{{ versionInfo.version }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 升级对话框 -->
    <UpdateDialog ref="updateDialog" />
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useElectronAPI } from "../plugins/electron";
import LogoSettings from "../components/LogoSettings.vue";
import UpdateDialog from "../components/UpdateDialog.vue";

// 使用Electron API
const electronAPI = useElectronAPI();

// 状态
const loading = ref(false);
const error = ref(null);
const settings = ref({
  controlledMode: false,
  basic: {
    serverName: "",
    udpPort: 9944,
    wsPort: 50208,
    resourcesPath: "",
  },
});

// 计算属性
const isControlledMode = ref(false);
const resourcesPath = ref("");

// 版本信息
const versionInfo = ref({
  version: "1.0.0",
  name: "PFDM",
  productName: "企业管理套件",
  description: "",
  copyright: `© ${new Date().getFullYear()} All rights reserved.`,
  company: "",
});

// 状态
const checkingUpdate = ref(false);
const updateDialog = ref(null);

// 方法
const fetchSettings = async () => {
  try {
    loading.value = true;
    error.value = null;
    const result = await electronAPI.getSettings();
    if (result) {
      settings.value = result;
      isControlledMode.value = result.controlledMode;
      resourcesPath.value = result.basic?.resourcesPath || "";
    }
  } catch (err) {
    error.value = err.message || "获取设置失败";
    console.error("获取设置失败:", err);
  } finally {
    loading.value = false;
  }
};

const saveSettings = async (showLoading = true) => {
  try {
    if (showLoading) {
      loading.value = true;
    }
    error.value = null;

    // 将响应式对象转换为普通 JavaScript 对象
    const settingsToSave = JSON.parse(JSON.stringify(settings.value));

    // 保存设置
    await electronAPI.saveSettings(settingsToSave);
    return true;
  } catch (err) {
    error.value = err.message || "保存设置失败";
    console.error("保存设置失败:", err);
    return false;
  } finally {
    if (showLoading) {
      loading.value = false;
    }
  }
};

const toggleControlledMode = async () => {
  try {
    // 更新设置中的受控模式值
    settings.value.controlledMode = isControlledMode.value;

    // 保存设置 - 后端会自动向所有在线设备发送更新命令
    // 传递 false 参数，不显示加载状态
    await saveSettings(false);

    console.log("受控模式设置已更新");
  } catch (err) {
    console.error("更新受控模式设置失败:", err);
    error.value = err.message || "更新受控模式设置失败";
  }
};

// 选择资源路径
const selectResourcesPath = async () => {
  try {
    // 调用 Electron API 打开文件夹选择对话框
    const result = await electronAPI.selectFiles({
      properties: ["openDirectory"],
      title: "选择资源存储路径",
    });

    if (result && result.length > 0) {
      const dirPath = result[0].path;
      resourcesPath.value = dirPath;

      // 更新设置中的资源路径值
      if (!settings.value.basic) {
        settings.value.basic = {};
      }
      settings.value.basic.resourcesPath = dirPath;

      // 保存设置
      await saveSettings(false);

      console.log("资源路径设置已更新:", dirPath);
    }
  } catch (err) {
    console.error("选择资源路径失败:", err);
    error.value = err.message || "选择资源路径失败";
  }
};

// 清除资源路径（恢复默认）
const clearResourcesPath = async () => {
  try {
    resourcesPath.value = "";

    // 更新设置中的资源路径值
    if (!settings.value.basic) {
      settings.value.basic = {};
    }
    settings.value.basic.resourcesPath = "";

    // 保存设置
    await saveSettings(false);

    console.log("资源路径已恢复默认");
  } catch (err) {
    console.error("恢复默认资源路径失败:", err);
    error.value = err.message || "恢复默认资源路径失败";
  }
};

// 获取版本信息
const fetchVersionInfo = async () => {
  try {
    const result = await electronAPI.getAppVersion();
    if (result) {
      versionInfo.value = result;
    }
  } catch (err) {
    console.error("获取版本信息失败:", err);
  }
};

// 方法
const handleCheckUpdate = async () => {
  if (checkingUpdate.value) return;
  checkingUpdate.value = true;
  try {
    await updateDialog.value.checkUpdate();
  } finally {
    checkingUpdate.value = false;
  }
};

// 生命周期钩子
onMounted(async () => {
  await fetchSettings();
  await fetchVersionInfo();
});
</script>

<style scoped>
.page {
  padding: var(--spacing-md) var(--spacing-md) 0 var(--spacing-md); /* 移除底部内边距 */
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
  background-color: var(--color-background-light); /* 添加背景色 */
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.header h3 {
  margin: 0;
  font-size: 18px;
  color: var(--color-text-primary);
  padding-bottom: 4px;
}

.loading,
.error {
  padding: var(--spacing-md);
  text-align: center;
  margin: var(--spacing-md) 0;
}

.error {
  color: var(--color-danger);
}

.settings-panel {
  padding: var(--spacing-md); /* 减小内边距 */
  background-color: transparent; /* 设置为透明 */
  box-sizing: border-box;
  flex: 1; /* 占据剩余空间 */
  overflow: auto; /* 内容过多时允许滚动 */
}

.settings-section {
  margin: var(--spacing-md) 0;
  background-color: var(--color-box-bg);
  border-radius: 16px;
  padding: 34px;
  width: 50vw;
}

.settings-section h4 {
  margin: 0 0 var(--spacing-md) 0; /* 从 spacing-sm (8px) 增加到 spacing-md (16px) */
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  border-bottom: 2px solid var(--color-primary);
  padding-bottom: var(--spacing-xs);
  position: relative;
}

.settings-section h4::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: var(--color-primary);
}

.switch-header {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.switch-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}

.switch-text {
  font-size: 13px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.switch-wrapper {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 22px;
}

.switch-wrapper input {
  opacity: 0;
  width: 0;
  height: 0;
}

.desc {
  color: var(--color-menu-text);
  font-size: 12px;
  font-style: normal;
  font-weight: 340;
  line-height: 22px; /* 157.143% */
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-text-disabled);
  transition: all var(--transition-normal);
  border-radius: 24px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background-color: var(--color-card-background);
  transition: all var(--transition-normal);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.switch-slider:hover:before {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

input:checked + .switch-slider {
  background-color: var(--color-primary);
}

input:checked + .switch-slider:before {
  transform: translateX(22px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

input:disabled + .switch-slider {
  opacity: 0.6;
  cursor: not-allowed;
}

.path-control {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-md);
  background-color: var(--color-card-background);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--color-primary-light);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  min-height: 80px;
}

.path-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xs);
  height: 40px;
}

.path-control:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.path-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
  white-space: nowrap;
  flex: 0 0 100px;
}

.path-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.path-button {
  padding: var(--spacing-xs) var(--spacing-sm);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
  height: 28px;
  white-space: nowrap;
  min-width: 70px;
}

.select-button {
  background-color: var(--color-primary);
  color: white;
}

.select-button:hover {
  background-color: var(--color-primary-dark);
}

.reset-button {
  background-color: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
}

.reset-button:hover:not(:disabled) {
  background-color: var(--color-danger-light);
  color: var(--color-danger);
  border-color: var(--color-danger);
}

.reset-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.path-display {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--color-background-light);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--color-border);
  font-size: 12px;
  color: var(--color-text-primary);
  word-break: break-all;
  height: 28px;
  flex: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: auto;
  margin-left: var(--spacing-md);
}

.path-empty {
  color: var(--color-text-secondary);
  font-style: italic;
}

.path-description {
  font-size: 11px;
  color: var(--color-text-secondary);
  line-height: 1.3;
  margin-top: 4px;
}

/* 版本信息样式 - 更紧凑的版本 */
.version-info {
  position: fixed;
  bottom: 34px;
  right: 34px;
  padding: 8px 12px;
  background-color: var(--color-card-background);
  border-radius: var(--border-radius);
  border: 1px solid var(--color-border);
  text-align: right;
  font-size: 0.8em;
  z-index: 100;
}

.version-info-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.version-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.app-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-secondary);
}

.version-number {
  font-size: 11px;
  color: var(--color-text-secondary);
  font-weight: 400;
}

.header-title {
  color: var(--color-menu-text);
  text-align: left;
  font-size: 24px;
  font-style: normal;
  font-weight: 480;
  line-height: 18px;
  padding: 20px 0 0 20px;
}
</style>
