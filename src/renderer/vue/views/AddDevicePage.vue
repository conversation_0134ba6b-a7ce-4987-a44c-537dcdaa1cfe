<template>
  <div class="page">
    <div class="header">
      <button class="back-btn" @click="handleBackNavigation">
        <img src="@assets/svg/back_dark.svg" alt="back" class="back-icon" />
        <span>添加设备</span>
      </button>

      <button
        class="btn btn-primary"
        :disabled="selectedDevices.length === 0"
        :class="selectedDevices.length === 0 ? 'btn-disabled' : ''"
        @click="batchAddDevices"
      >
        添加
      </button>
    </div>

    <div class="content-panel">
      <div class="device-count">
        <div>
          发现设备数量: <span>{{ availableDevices.length }}</span>
        </div>
        <el-tooltip
          class="custom-tooltip"
          effect="customized"
          placement="bottom-end"
        >
          <template #content>
            <div class="my-tooltip-content">
              <div id="title">如何让设备出现在设备列表？</div>
              <pre>
1.前往VR设备【设置】-【网络】，将VR设备与电
脑连接在同一局域网下；
2.在【资源库】打开【企业应用套件】应用；
3.在播控应用主界面，点击【设置】，打开【接受控制端
控制】开关；</pre
              >
            </div>
          </template>
          <div class="tip-text">
            <img src="@assets/svg/tip.svg" alt="tip" />
            <span>如何让设备出现在设备列表？</span>
          </div>
        </el-tooltip>
      </div>

      <div class="device-list-header">
        <label class="device-checkbox">
          <input
            type="checkbox"
            :checked="isAllSelected"
            :indeterminate="isIndeterminate"
            @change="toggleSelectAll"
          />
          <span class="checkbox-custom"></span>
          <span>全选</span>
        </label>
        <div>设备号</div>
        <div>设备SN号</div>
        <div>更多操作</div>
      </div>

      <div class="device-list-container">
        <div v-if="availableDevices.length === 0" class="empty-message">
          未找到可添加的设备
        </div>
        <div
          v-for="device in availableDevices"
          :key="device.sn"
          class="available-device-item"
          :class="{ removing: removingDevices.includes(device.sn) }"
        >
          <label class="device-checkbox">
            <input
              type="checkbox"
              class="device-select"
              v-model="selectedDevices"
              :value="device.sn"
              @change="updateSelectAllState"
            />
            <span class="checkbox-custom"></span>
          </label>
          <div class="device-id">自动</div>
          <div class="device-sn">{{ device.sn }}</div>
          <div class="device-actions" @click="addDevice(device.sn)">添加</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import { useElectronAPI } from "../plugins/electron";
import alertService from "../plugins/alert";
import { ElMessage } from "element-plus";

// 使用路由
const router = useRouter();

// 使用Electron API
const electronAPI = useElectronAPI();

// 状态
const availableDevices = ref([]);
const selectedDevices = ref([]);
const removingDevices = ref([]);

// 计算属性
const isAllSelected = computed(() => {
  return (
    availableDevices.value.length > 0 &&
    selectedDevices.value.length === availableDevices.value.length
  );
});

const isIndeterminate = computed(() => {
  return (
    selectedDevices.value.length > 0 &&
    selectedDevices.value.length < availableDevices.value.length
  );
});

// 方法
const toggleSelectAll = (event) => {
  if (event.target.checked) {
    selectedDevices.value = availableDevices.value.map((device) => device.sn);
  } else {
    selectedDevices.value = [];
  }
};

const updateSelectAllState = () => {
  // 这个方法在单个设备选择状态变化时调用
  // 在Vue中，由于使用了v-model，selectedDevices会自动更新
  // 所以这里不需要额外的逻辑
};

const addDevice = async (sn) => {
  try {
    await electronAPI.addDevice(sn);
    showSuccess(`设备 ${sn} 添加成功`);
    removeDeviceFromList(sn);
  } catch (error) {
    console.error("添加设备失败:", error);
    showError(`添加设备失败: ${error.message || "未知错误"}`);
  }
};

const batchAddDevices = async () => {
  if (selectedDevices.value.length === 0) return;

  try {
    // 批量添加设备
    for (const sn of selectedDevices.value) {
      await electronAPI.addDevice(sn);
      removeDeviceFromList(sn);
    }

    showSuccess(`成功添加 ${selectedDevices.value.length} 个设备`);
    selectedDevices.value = []; // 清空选择
  } catch (error) {
    console.error("批量添加设备失败:", error);
    showError(`批量添加设备失败: ${error.message || "未知错误"}`);
  }
};

const removeDeviceFromList = (sn) => {
  // 添加到正在移除的设备列表，用于动画效果
  removingDevices.value.push(sn);

  // 延迟移除设备，等待动画完成
  setTimeout(() => {
    availableDevices.value = availableDevices.value.filter(
      (device) => device.sn !== sn
    );
    selectedDevices.value = selectedDevices.value.filter(
      (deviceSn) => deviceSn !== sn
    );
    removingDevices.value = removingDevices.value.filter(
      (deviceSn) => deviceSn !== sn
    );
  }, 300);
};

const handleDeviceConnected = async ({ ip, status }) => {
  if (!status?.sn) return;

  try {
    // 检查设备是否已添加
    const isAdded = await electronAPI.isDeviceAdded(status.sn);
    if (isAdded) return; // 如果设备已添加，不显示在可用设备列表中

    // 检查设备是否已在列表中
    const existingDevice = availableDevices.value.find(
      (device) => device.sn === status.sn
    );
    if (!existingDevice) {
      // 添加设备到列表
      availableDevices.value.push({
        sn: status.sn,
        ip: ip,
        status: status,
      });
      console.log("AddDevicePage: 添加设备到可用列表", status.sn);
    }
  } catch (error) {
    console.error("处理设备连接事件失败:", error);
  }
};

const handleDeviceDisconnected = async (ip) => {
  // 更新设备列表，移除已断开连接的设备
  const devicesToRemove = [];

  for (const device of availableDevices.value) {
    // 检查设备是否在线
    const isOnline = await electronAPI.isDeviceOnline(device.sn);
    if (!isOnline) {
      devicesToRemove.push(device.sn);
    }
  }

  // 移除离线设备
  for (const sn of devicesToRemove) {
    removeDeviceFromList(sn);
  }
};

const handleBackNavigation = () => {
  // 返回设备管理页面
  router.push("/devices");
};

const showSuccess = async (message) => {
  ElMessage.success(message);
};

const showError = async (message) => {
  await alertService.alert({
    title: "操作失败",
    message: message,
    confirmButtonText: "确定",
  });
};

// 生命周期钩子
onMounted(async () => {
  // 获取可用设备列表
  await updateAvailableDevices();

  // 设置事件监听器
  setupEventListeners();
});

onUnmounted(() => {
  // 移除事件监听器
  removeEventListeners();
});

// 更新可用设备列表
const updateAvailableDevices = async () => {
  try {
    // 获取所有在线设备
    const onlineDevices = await electronAPI.getOnlineDevices();

    // 过滤出未添加的设备
    for (const device of onlineDevices) {
      if (!device.status?.sn) continue;

      const isAdded = await electronAPI.isDeviceAdded(device.status.sn);
      if (!isAdded) {
        availableDevices.value.push({
          sn: device.status.sn,
          ip: device.ip,
          status: device.status,
        });
      }
    }

    console.log(
      "AddDevicePage: 可用设备列表更新完成，共",
      availableDevices.value.length,
      "个设备"
    );
  } catch (error) {
    console.error("获取可用设备列表失败:", error);
    showError(`获取可用设备列表失败: ${error.message || "未知错误"}`);
  }
};

// 设置事件监听器
const setupEventListeners = () => {
  // 监听设备连接事件
  electronAPI.onDeviceConnected(handleDeviceConnected);

  // 监听设备断开连接事件
  electronAPI.onDeviceDisconnected(handleDeviceDisconnected);

  // 监听设备添加事件
  electronAPI.onDeviceAdded(({ sn }) => {
    removeDeviceFromList(sn);
  });

  console.log("AddDevicePage: 事件监听器设置完成");
};

// 移除事件监听器
const removeEventListeners = () => {
  // 移除设备连接事件监听器
  electronAPI.removeAllListeners("device-connected");

  // 移除设备断开连接事件监听器
  electronAPI.removeAllListeners("device-disconnected");

  // 移除设备添加事件监听器
  electronAPI.removeAllListeners("device-added");
};
</script>

<style scoped>
.page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--color-background);
}

.back-icon {
  width: 18px;
  height: 18px;
  margin-right: 12px;
}

.header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  justify-content: space-between;
  background-color: var(--color-background-light);
}

.header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.content-panel {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background-color: var(
    --color-background-light
  ); /* 使用变量而不是默认的白色 */
}

.device-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  color: #595b6a;
  font-size: 14px;
  font-style: normal;
  font-weight: 305;
  line-height: 18px; /* 128.571% */
}

.device-list-header {
  display: grid;
  grid-template-columns: 80px 1fr 2fr 100px;
  align-items: center;
  gap: 16px;
  height: 46px;
  padding: 0 16px;
  background-color: #d9e8f8;
  border: 1px solid #e5effb;

  color: #1d2129;
  font-size: 18px;
  font-style: normal;
  font-weight: 340;
  line-height: 14px; /* 77.778% */
}

.device-list-container {
  overflow: hidden;
  border-bottom: 1px solid #d3e7fe;
  background: #eff6fe;
}

.available-device-item {
  display: grid;
  justify-content: space-between;
  grid-template-columns: 80px 1fr 2fr 100px;
  gap: 16px;
  padding: 18px 16px;
  border-bottom: 1px solid var(--color-border);
  align-items: center;
  transition: all 0.3s ease;
}

.available-device-item:last-child {
  border-bottom: none;
}

.available-device-item:hover {
  background-color: var(--color-background-hover);
}

.device-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.device-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkbox-custom {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  background-color: #eff6fe;

  border-radius: 2px;
  border: 2px solid #1890ff;

  margin-right: 8px;
  transition: all 0.2s;
}

.device-checkbox input[type="checkbox"]:checked ~ .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.device-checkbox input[type="checkbox"]:checked ~ .checkbox-custom:after {
  content: "";
  position: absolute;
  left: 5px;
  top: 1.5px;
  width: 3px;
  height: 7px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.device-checkbox input[type="checkbox"]:indeterminate ~ .checkbox-custom {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.device-checkbox input[type="checkbox"]:indeterminate ~ .checkbox-custom:after {
  content: "";
  position: absolute;
  left: 3px;
  top: 7px;
  width: 10px;
  height: 2px;
  background-color: white;
}

.device-id,
.device-sn,
.device-actions {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  color: #595b6a;
  font-size: 18px;
  font-style: normal;
  font-weight: 340;
  line-height: 20px; /* 77.778% */
}
.device-actions {
  color: #3593ff;
  cursor: pointer;
  display: flex;
  justify-content: flex-end;
  margin-right: 40px;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 4px;
  border: none;
  background-color: var(--color-primary);
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background-color: var(--color-primary-dark, #0c7cd5);
}

.empty-message {
  padding: 24px;
  text-align: center;
  color: var(--color-text-secondary);
}

/* 移除动画 */
.removing {
  opacity: 0;
  transform: translateX(20px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.available-device-item {
  animation: fadeIn 0.3s ease-out;
}
.tip-text {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  gap: 8px;
  color: var(--color-primary);
}
.my-tooltip-content {
  padding: 24px 34px;
  border-radius: 16px;
  background: var(--color-dialog-background);
  box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.15);
  #title {
    text-align: center;
    color: var(--color-menu-text);
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
    margin-bottom: 12px;
  }
  pre {
    color: var(--color-menu-text);
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }
}

:deep(.el-popper) {
  background: transparent !important;
  border-color: transparent !important;
}
:deep(el-popper > .el-popper__arrow::before) {
  background: transparent !important;
  border-color: transparent !important;
}
</style>
