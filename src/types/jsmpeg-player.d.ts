declare module '@cycjimmy/jsmpeg-player' {
  interface JSMpegOptions {
    canvas?: HTMLCanvasElement;
    autoplay?: boolean;
    audio?: boolean;
    loop?: boolean;
    pauseWhenHidden?: boolean;
    videoBufferSize?: number;
    audioBufferSize?: number;
    onSourceEstablished?: () => void;
    onSourceCompleted?: () => void;
    onStalled?: () => void;
    onError?: (error: any) => void;
  }

  class JSMpeg {
    constructor(url: string, options?: JSMpegOptions);
    play(): void;
    pause(): void;
    stop(): void;
    destroy(): void;
    volume: number;
    currentTime: number;
    duration: number;
  }

  export default JSMpeg;
}
