<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备资源预览</title>
    <style>
        :root {
            /* 颜色变量 */
            --color-primary: #2196F3;
            --color-primary-light: #64B5F6;
            --color-primary-dark: #1976D2;
            --color-background: #F5F5F5;
            --color-background-dark: #E0E0E0;
            --color-background-light: #FFFFFF;
            --color-text-primary: #333333;
            --color-text-secondary: #757575;
            --color-border: #E0E0E0;
            --color-border-light: #EEEEEE;
            --color-border-dark: #BDBDBD;
            --color-success: #4CAF50;
            --color-warning: #FFC107;
            --color-danger: #F44336;
            --color-info: #2196F3;

            /* 间距变量 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;

            /* 圆角变量 */
            --border-radius-sm: 4px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-circle: 50%;

            /* 阴影变量 */
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        /* 暗色主题 */
        [data-theme="dark"] {
            --color-background: #121212;
            --color-background-dark: #1E1E1E;
            --color-background-light: #2D2D2D;
            --color-text-primary: #FFFFFF;
            --color-text-secondary: #AAAAAA;
            --color-border: #333333;
            --color-border-light: #444444;
            --color-border-dark: #555555;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
                Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: var(--color-text-primary);
            background-color: var(--color-background);
            padding: var(--spacing-md);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding-bottom: var(--spacing-sm);
            border-bottom: 1px solid var(--color-border);
        }

        .header h1 {
            font-size: 24px;
            font-weight: 500;
        }

        .header-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .btn {
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: var(--border-radius-sm);
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn-primary {
            background-color: var(--color-primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--color-primary-dark);
        }

        .btn-text {
            background-color: transparent;
            color: var(--color-text-secondary);
        }

        .btn-text:hover {
            color: var(--color-text-primary);
            background-color: var(--color-background-dark);
        }

        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }

        .resource-card {
            background-color: var(--color-background-light);
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s;
            cursor: pointer;
            position: relative;
        }

        .resource-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .resource-thumbnail {
            width: 100%;
            height: 150px;
            background-color: var(--color-background-dark);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
        }

        .resource-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .resource-thumbnail .icon {
            font-size: 48px;
            color: var(--color-text-secondary);
        }

        .resource-info {
            padding: var(--spacing-sm);
        }

        .resource-name {
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .resource-meta {
            font-size: 12px;
            color: var(--color-text-secondary);
            display: flex;
            justify-content: space-between;
        }

        .resource-type {
            text-transform: uppercase;
            font-weight: 500;
        }

        .resource-size {
            text-align: right;
        }

        .resource-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .resource-card:hover .resource-overlay {
            opacity: 1;
        }

        .resource-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .resource-action {
            width: 36px;
            height: 36px;
            border-radius: var(--border-radius-circle);
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .resource-action:hover {
            transform: scale(1.1);
        }

        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--color-text-secondary);
        }

        .loading {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--color-text-secondary);
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background-color: var(--color-background-light);
            border-radius: var(--border-radius);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow: auto;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            padding: var(--spacing-md);
            border-bottom: 1px solid var(--color-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 500;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--color-text-secondary);
        }

        .modal-body {
            padding: var(--spacing-md);
        }

        .preview-image {
            max-width: 100%;
            max-height: 70vh;
            display: block;
            margin: 0 auto;
        }

        .preview-video {
            max-width: 100%;
            max-height: 70vh;
            display: block;
            margin: 0 auto;
        }

        .preview-info {
            margin-top: var(--spacing-md);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--color-border);
        }

        .preview-info-item {
            display: flex;
            margin-bottom: var(--spacing-xs);
        }

        .preview-info-label {
            width: 100px;
            font-weight: 500;
            color: var(--color-text-secondary);
        }

        .preview-info-value {
            flex: 1;
        }

        /* 方案信息 */
        .solution-info {
            margin-bottom: var(--spacing-md);
            padding: var(--spacing-md);
            background-color: var(--color-background-light);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
        }

        .solution-name {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
            color: var(--color-primary);
        }

        .solution-meta {
            display: flex;
            gap: var(--spacing-md);
            color: var(--color-text-secondary);
            font-size: 12px;
        }

        /* 分组过滤器 */
        .filter-bar {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-md);
            padding: var(--spacing-sm) var(--spacing-md);
            background-color: var(--color-background-light);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
        }

        .filter-label {
            margin-right: var(--spacing-md);
            font-weight: 500;
            color: var(--color-text-secondary);
        }

        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xs);
        }

        .filter-option {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            background-color: var(--color-background);
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-option:hover {
            background-color: var(--color-background-dark);
        }

        .filter-option.active {
            background-color: var(--color-primary);
            color: white;
        }

        /* 应用预览 */
        .app-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xl);
        }

        .app-icon {
            font-size: 64px;
            margin-bottom: var(--spacing-md);
        }

        .app-name {
            font-size: 18px;
            font-weight: 500;
        }

        /* 主题切换按钮 */
        .theme-toggle {
            background: none;
            border: none;
            cursor: pointer;
            color: var(--color-text-secondary);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .resource-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }

            .solution-meta {
                flex-direction: column;
                gap: var(--spacing-xs);
            }

            .filter-bar {
                flex-direction: column;
                align-items: flex-start;
            }

            .filter-label {
                margin-bottom: var(--spacing-xs);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>设备资源预览</h1>
            <div class="header-actions">
                <button class="btn btn-text" id="refreshBtn">刷新</button>
                <button class="btn btn-text theme-toggle" id="themeToggle">切换主题</button>
            </div>
        </div>

        <div id="resourceContainer">
            <div class="loading">加载中...</div>
        </div>
    </div>

    <div class="modal" id="previewModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">资源预览</div>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 预览内容将在这里动态插入 -->
            </div>
        </div>
    </div>

    <script>
        // 资源路径配置
        const resourcesPath = 'received_files_192.168.1.100';
        const configPath = `${resourcesPath}/config.dat`;

        // 调试模式 - 设置为true可以在控制台显示详细日志
        const DEBUG = true;
        let resources = [];
        let configData = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 加载资源
            loadResources();

            // 事件监听
            document.getElementById('refreshBtn').addEventListener('click', loadResources);
            document.getElementById('themeToggle').addEventListener('click', toggleTheme);
            document.getElementById('modalClose').addEventListener('click', closeModal);
        });

        // 加载资源
        function loadResources() {
            const container = document.getElementById('resourceContainer');
            container.innerHTML = '<div class="loading">加载资源中...</div>';

            // 首先加载并解析config.dat文件
            loadConfigData()
                .then(() => {
                    // 然后获取资源文件列表
                    return fetchResourceList();
                })
                .then(data => {
                    resources = data;
                    renderResources();
                })
                .catch(error => {
                    container.innerHTML = `<div class="empty-state">加载资源失败: ${error.message}</div>`;
                    console.error('加载资源失败:', error);
                });
        }

        // 加载并解析config.dat文件
        function loadConfigData() {
            if (DEBUG) console.log('尝试加载config.dat文件:', configPath);

            return fetch(configPath)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`无法加载config.dat文件 (${response.status} ${response.statusText})`);
                    }
                    if (DEBUG) console.log('config.dat文件加载成功，正在解析JSON');
                    return response.json();
                })
                .then(data => {
                    configData = data;
                    if (DEBUG) {
                        console.log('成功解析config.dat文件:');
                        console.log('- 方案名称:', data.name);
                        console.log('- UUID:', data.UUID);
                        console.log('- 资源数量:', data.list ? data.list.length : 0);
                        if (data.list && data.list.length > 0) {
                            console.log('- 第一个资源示例:', data.list[0]);
                        }
                    }
                    return data;
                })
                .catch(error => {
                    console.error('加载config.dat文件失败:', error);
                    // 如果无法加载config.dat，我们仍然继续，但会使用文件系统信息
                    configData = null;
                    return null;
                });
        }

        // 获取资源列表
        function fetchResourceList() {
            // 如果成功加载了config.dat，使用其中的资源列表
            if (configData && configData.list && Array.isArray(configData.list)) {
                console.log('使用config.dat中的资源列表');

                // 从config.dat中提取资源信息
                return Promise.resolve(configData.list.map(item => {
                    // 构建资源路径
                    // 注意：我们完全按照config.dat中的路径信息来构建，不做任何假设

                    // 处理路径的辅助函数
                    function buildPath(basePath, relativePath) {
                        // 移除开头的斜杠，避免双斜杠
                        const cleanBase = basePath.endsWith('/') ? basePath.slice(0, -1) : basePath;
                        const cleanRelative = relativePath.startsWith('/') ? relativePath.slice(1) : relativePath;
                        return `${cleanBase}/${cleanRelative}`;
                    }

                    // 主资源文件路径 - 完全按照config.dat中的path属性
                    // 注意：path属性可能是形如 "image3/" 的相对路径
                    const resourcePath = buildPath(resourcesPath, `${item.path || ''}${item.fileName}`);

                    // 缩略图路径 - 相对于资源的path属性
                    let thumbnailPath = null;
                    if (item.thumbnail) {
                        // 如果thumbnail是相对路径，应该相对于资源的path
                        // 例如，如果path是"image3/"，thumbnail是"thumbnail_xxx.jpg"
                        // 那么完整路径应该是"resourcesPath/image3/thumbnail_xxx.jpg"
                        thumbnailPath = buildPath(resourcesPath, buildPath(item.path || '', item.thumbnail));
                    }

                    // 海报图路径 - 相对于资源的path属性
                    let posterPath = null;
                    if (item.poster) {
                        // 如果poster是相对路径，应该相对于资源的path
                        posterPath = buildPath(resourcesPath, buildPath(item.path || '', item.poster));
                    }

                    if (DEBUG) {
                        console.log(`资源 "${item.fileName}" 的路径信息:`);
                        console.log(`- 原始path属性: "${item.path}"`);
                        console.log(`- 原始fileName属性: "${item.fileName}"`);
                        console.log(`- 构建的完整路径: "${resourcePath}"`);
                        console.log(`- 缩略图路径: "${thumbnailPath}"`);
                        console.log(`- 海报图路径: "${posterPath}"`);
                    }

                    return {
                        name: item.fileName,
                        showName: item.showName || item.fileName,
                        path: resourcePath,
                        type: getResourceTypeFromConfig(item),
                        size: '未知', // 文件大小需要从文件系统获取
                        lastModified: '未知', // 修改时间需要从文件系统获取
                        groups: item.groups || [],
                        thumbnail: thumbnailPath,
                        poster: posterPath,
                        MD5: item.MD5 || '',
                        index: item.index || 0,
                        configItem: item // 保存原始配置项
                    };
                }));
            } else {
                console.log('无法使用config.dat，将扫描资源目录');

                // 如果无法加载config.dat，则扫描资源目录
                // 在实际应用中，这里需要一个服务器端API来扫描目录
                // 这里我们返回一些模拟数据
                if (DEBUG) console.log('使用模拟数据');

                // 处理路径的辅助函数
                function buildPath(basePath, relativePath) {
                    // 移除开头的斜杠，避免双斜杠
                    const cleanBase = basePath.endsWith('/') ? basePath.slice(0, -1) : basePath;
                    const cleanRelative = relativePath.startsWith('/') ? relativePath.slice(1) : relativePath;
                    return `${cleanBase}/${cleanRelative}`;
                }

                // 使用与config.dat相同的路径构建逻辑
                return Promise.resolve([
                    {
                        name: 'video1.mp4',
                        showName: 'Video 1',
                        // 模拟config.dat中的结构，这里我们使用path和fileName
                        path: buildPath(resourcesPath, 'r1/video1.mp4'),
                        // 原始path属性，用于构建缩略图和海报图路径
                        originalPath: 'r1/',
                        type: 'video',
                        size: '10.5 MB',
                        lastModified: '2023-05-15 14:30:22',
                        groups: ['默认'],
                        thumbnail: buildPath(resourcesPath, 'r1/thumbnail_video1.jpg'),
                        poster: buildPath(resourcesPath, 'r1/poster_video1.jpg'),
                        MD5: '',
                        index: 1
                    },
                    {
                        name: 'image1.jpg',
                        showName: 'Image 1',
                        path: buildPath(resourcesPath, 'r2/image1.jpg'),
                        originalPath: 'r2/',
                        type: 'image',
                        size: '2.3 MB',
                        lastModified: '2023-05-16 09:12:45',
                        groups: ['默认'],
                        thumbnail: buildPath(resourcesPath, 'r2/thumbnail_image1.jpg'),
                        poster: null,
                        MD5: '',
                        index: 2
                    },
                    {
                        name: 'app1.apk',
                        showName: 'Application 1',
                        path: buildPath(resourcesPath, 'r3/app1.apk'),
                        originalPath: 'r3/',
                        type: 'application',
                        size: '15.8 MB',
                        lastModified: '2023-05-14 16:45:30',
                        groups: ['默认'],
                        thumbnail: buildPath(resourcesPath, 'r3/thumbnail_app1.jpg'),
                        poster: null,
                        MD5: '',
                        index: 3
                    }
                ]);
            }
        }

        // 从配置项中获取资源类型
        function getResourceTypeFromConfig(item) {
            // 根据配置项中的type字段确定资源类型
            // 假设：1=视频，2=图片，3=应用
            switch (item.type) {
                case 1:
                    return 'video';
                case 2:
                    return 'image';
                case 3:
                    return 'application';
                default:
                    return 'other';
            }
        }

        // 渲染资源列表
        function renderResources() {
            const container = document.getElementById('resourceContainer');

            if (resources.length === 0) {
                container.innerHTML = '<div class="empty-state">没有找到资源</div>';
                return;
            }

            // 添加方案信息
            let headerInfo = '';
            if (configData) {
                headerInfo = `
                    <div class="solution-info">
                        <div class="solution-name">${configData.name || '未命名方案'}</div>
                        <div class="solution-meta">
                            <div>UUID: ${configData.UUID || '未知'}</div>
                            <div>资源数量: ${resources.length}</div>
                        </div>
                    </div>
                `;
                container.innerHTML = headerInfo;
            }

            // 添加分组过滤器
            let groups = ['全部'];
            if (configData && configData.groups) {
                groups = groups.concat(configData.groups);
            } else {
                // 从资源中提取分组
                const groupSet = new Set();
                resources.forEach(resource => {
                    if (resource.groups && Array.isArray(resource.groups)) {
                        resource.groups.forEach(group => groupSet.add(group));
                    }
                });
                groups = groups.concat(Array.from(groupSet));
            }

            let filterHtml = `
                <div class="filter-bar">
                    <div class="filter-label">分组:</div>
                    <div class="filter-options">
            `;

            groups.forEach(group => {
                filterHtml += `
                    <div class="filter-option" data-group="${group}">${group}</div>
                `;
            });

            filterHtml += `
                    </div>
                </div>
            `;

            container.innerHTML += filterHtml;

            // 添加资源网格
            let html = '<div class="resource-grid">';

            resources.forEach(resource => {
                html += `
                    <div class="resource-card" data-path="${resource.path}" data-groups="${resource.groups.join(',')}">
                        <div class="resource-thumbnail">
                            ${getThumbnail(resource)}
                            <div class="resource-overlay">
                                <div class="resource-actions">
                                    <div class="resource-action" onclick="previewResource('${resource.path}')">
                                        <i class="icon">👁️</i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="resource-info">
                            <div class="resource-name" title="${resource.showName || resource.name}">${resource.showName || resource.name}</div>
                            <div class="resource-meta">
                                <div class="resource-type">${resource.type}</div>
                                <div class="resource-size">${resource.size}</div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML += html;

            // 添加点击事件
            document.querySelectorAll('.resource-card').forEach(card => {
                card.addEventListener('click', () => {
                    const path = card.getAttribute('data-path');
                    previewResource(path);
                });
            });

            // 添加分组过滤事件
            document.querySelectorAll('.filter-option').forEach(option => {
                option.addEventListener('click', () => {
                    const group = option.getAttribute('data-group');
                    filterByGroup(group);

                    // 更新选中状态
                    document.querySelectorAll('.filter-option').forEach(opt => {
                        opt.classList.remove('active');
                    });
                    option.classList.add('active');
                });
            });

            // 默认选中"全部"
            document.querySelector('.filter-option[data-group="全部"]').classList.add('active');
        }

        // 按分组过滤资源
        function filterByGroup(group) {
            const cards = document.querySelectorAll('.resource-card');

            cards.forEach(card => {
                if (group === '全部') {
                    card.style.display = '';
                } else {
                    const groups = card.getAttribute('data-groups').split(',');
                    if (groups.includes(group)) {
                        card.style.display = '';
                    } else {
                        card.style.display = 'none';
                    }
                }
            });
        }

        // 获取资源缩略图
        function getThumbnail(resource) {
            // 如果有缩略图，优先使用缩略图
            if (resource.thumbnail) {
                return `<img src="${resource.thumbnail}" alt="${resource.showName || resource.name}">`;
            }

            // 如果有海报图，使用海报图
            if (resource.poster) {
                return `<img src="${resource.poster}" alt="${resource.showName || resource.name}">`;
            }

            // 如果是图片，直接使用图片本身
            if (resource.type === 'image') {
                return `<img src="${resource.path}" alt="${resource.showName || resource.name}">`;
            }

            // 其他类型使用图标
            switch (resource.type) {
                case 'video':
                    return `<div class="icon">🎬</div>`;
                case 'application':
                    return `<div class="icon">📱</div>`;
                default:
                    return `<div class="icon">📁</div>`;
            }
        }

        // 预览资源
        function previewResource(path) {
            const resource = resources.find(r => r.path === path);
            if (!resource) return;

            const modal = document.getElementById('previewModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalBody = document.getElementById('modalBody');

            modalTitle.textContent = resource.showName || resource.name;

            let previewContent = '';

            switch (resource.type) {
                case 'image':
                    previewContent = `<img src="${resource.path}" class="preview-image" alt="${resource.showName || resource.name}">`;
                    break;
                case 'video':
                    previewContent = `
                        <video class="preview-video" controls${resource.poster ? ` poster="${resource.poster}"` : ''}>
                            <source src="${resource.path}" type="video/mp4">
                            您的浏览器不支持视频标签。
                        </video>
                    `;
                    break;
                case 'application':
                    previewContent = `
                        <div class="app-preview">
                            <div class="app-icon">📱</div>
                            <div class="app-name">${resource.showName || resource.name}</div>
                        </div>
                    `;
                    break;
                default:
                    previewContent = `<div class="empty-state">无法预览此类型的文件</div>`;
            }

            previewContent += `
                <div class="preview-info">
                    <div class="preview-info-item">
                        <div class="preview-info-label">显示名称</div>
                        <div class="preview-info-value">${resource.showName || resource.name}</div>
                    </div>
                    <div class="preview-info-item">
                        <div class="preview-info-label">文件名</div>
                        <div class="preview-info-value">${resource.name}</div>
                    </div>
                    <div class="preview-info-item">
                        <div class="preview-info-label">类型</div>
                        <div class="preview-info-value">${resource.type}</div>
                    </div>
                    <div class="preview-info-item">
                        <div class="preview-info-label">分组</div>
                        <div class="preview-info-value">${resource.groups.join(', ') || '无'}</div>
                    </div>
                    <div class="preview-info-item">
                        <div class="preview-info-label">大小</div>
                        <div class="preview-info-value">${resource.size}</div>
                    </div>
                    <div class="preview-info-item">
                        <div class="preview-info-label">MD5</div>
                        <div class="preview-info-value">${resource.MD5 || '未知'}</div>
                    </div>
                    <div class="preview-info-item">
                        <div class="preview-info-label">路径</div>
                        <div class="preview-info-value">${resource.path}</div>
                    </div>
                </div>
            `;

            modalBody.innerHTML = previewContent;
            modal.classList.add('active');
        }

        // 关闭预览模态框
        function closeModal() {
            const modal = document.getElementById('previewModal');
            modal.classList.remove('active');
        }

        // 切换主题
        function toggleTheme() {
            const body = document.body;
            if (body.getAttribute('data-theme') === 'dark') {
                body.removeAttribute('data-theme');
            } else {
                body.setAttribute('data-theme', 'dark');
            }
        }
    </script>
</body>
</html>
