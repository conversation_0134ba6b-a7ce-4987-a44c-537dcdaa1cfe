const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('开始测试 Vue 构建...');

// 清理 dist 目录
console.log('清理 dist 目录...');
try {
  if (fs.existsSync('dist')) {
    execSync('npm run clean', { stdio: 'inherit' });
  }
} catch (error) {
  console.error('清理失败:', error.message);
}

// 构建 Vue 项目
console.log('构建 Vue 项目...');
try {
  execSync('npm run build:vue', { stdio: 'inherit' });
  console.log('Vue 构建完成');
} catch (error) {
  console.error('Vue 构建失败:', error.message);
  process.exit(1);
}

// 检查构建输出
console.log('检查构建输出...');

// 可能的 index.html 路径
const possiblePaths = [
  path.join(__dirname, 'dist', 'renderer', 'index.html'),
  path.join(__dirname, 'dist', 'index.html'),
  path.join(__dirname, 'dist', 'renderer', 'assets', 'index.html'),
  path.join(__dirname, 'src', 'renderer', 'vue', 'dist', 'index.html')
];

let found = false;
for (const p of possiblePaths) {
  console.log(`检查路径: ${p}`);
  if (fs.existsSync(p)) {
    console.log(`找到 index.html: ${p}`);
    found = true;
    break;
  }
}

if (!found) {
  console.error('未找到 index.html 文件');
  
  // 列出 dist 目录内容
  console.log('列出 dist 目录内容:');
  try {
    const distPath = path.join(__dirname, 'dist');
    if (fs.existsSync(distPath)) {
      const files = fs.readdirSync(distPath);
      files.forEach(file => console.log(`- ${file}`));
      
      // 如果有 renderer 目录，列出其内容
      const rendererPath = path.join(distPath, 'renderer');
      if (fs.existsSync(rendererPath)) {
        console.log('列出 renderer 目录内容:');
        const rendererFiles = fs.readdirSync(rendererPath);
        rendererFiles.forEach(file => console.log(`- ${file}`));
      }
    } else {
      console.log('dist 目录不存在');
    }
  } catch (error) {
    console.error('无法列出目录内容:', error.message);
  }
  
  process.exit(1);
}

console.log('测试完成');
