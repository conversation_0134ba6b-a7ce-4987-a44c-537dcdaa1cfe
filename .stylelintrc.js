module.exports = {
  extends: ['stylelint-config-standard'],
  rules: {
    // 选择器命名规范
    'selector-class-pattern': '^[a-z][a-zA-Z0-9]*(-[a-z][a-zA-Z0-9]*)*$',
    
    // 允许空的代码块（对于某些CSS框架可能需要）
    'block-no-empty': null,
    
    // 颜色格式
    'color-hex-case': 'lower',
    'color-hex-length': 'short',
    
    // 字体
    'font-family-name-quotes': 'always-where-required',
    
    // 数值
    'number-leading-zero': 'always',
    'unit-case': 'lower',
    
    // 属性
    'property-case': 'lower',
    'property-no-vendor-prefix': true,
    
    // 声明块
    'declaration-block-trailing-semicolon': 'always',
    'declaration-colon-space-after': 'always',
    'declaration-colon-space-before': 'never',
    
    // 其他
    'no-descending-specificity': null,
    'no-duplicate-selectors': true,
    'max-nesting-depth': 3
  }
};